# Phase 3: Model Architecture Design Configuration
# Windows-compatible paths with RTX 3070 memory management

# Input/Output Paths (Windows format)
paths:
  input_audio: "data\\processed\\phase2\\filtered_audio"
  input_metadata: "data\\processed\\phase1\\metadata"
  input_quality: "data\\processed\\phase2\\quality_metrics"
  output_root: "data\\processed\\phase3"
  model_configs: "data\\processed\\phase3\\model_configs"
  architecture_specs: "data\\processed\\phase3\\architecture_specs"
  logs_output: "data\\processed\\phase3\\processing_logs"

# Model Architecture Parameters
model_architecture:
  # Core Architecture Type
  architecture_type: "encoder_decoder_tcn"  # Encoder-Decoder with Temporal Convolutional Networks
  
  # Input Processing
  input_features:
    audio_sample_rate: 22050
    audio_channels: 1
    sequence_length: 1024        # Input sequence length (frames)
    feature_dim: 80              # Mel-spectrogram features
    hop_length: 512              # Hop length for feature extraction
    n_fft: 2048                  # FFT window size
    
  # Encoder Configuration
  encoder:
    type: "temporal_cnn"         # Temporal Convolutional Network
    layers: 6                    # Number of TCN layers
    channels: [64, 128, 256, 256, 128, 64]  # Channel progression
    kernel_size: 3               # Convolution kernel size
    dilation_base: 2             # Dilation factor base
    dropout: 0.1                 # Dropout rate
    activation: "relu"           # Activation function
    normalization: "batch_norm"  # Normalization type
    
  # Attention Mechanism
  attention:
    type: "multi_head"           # Multi-head attention
    num_heads: 8                 # Number of attention heads
    embed_dim: 256               # Embedding dimension
    dropout: 0.1                 # Attention dropout
    
  # Decoder Configuration
  decoder:
    type: "temporal_cnn"         # Temporal Convolutional Network
    layers: 4                    # Number of TCN layers
    channels: [256, 128, 64, 32] # Channel progression
    kernel_size: 3               # Convolution kernel size
    dilation_base: 2             # Dilation factor base
    dropout: 0.1                 # Dropout rate
    activation: "relu"           # Activation function
    normalization: "batch_norm"  # Normalization type
    
  # Output Configuration (Simplified TJA)
  output:
    num_classes: 5               # TJA note types: 0, 1, 2, 3, 4
    sequence_length: 512         # Output sequence length
    activation: "softmax"        # Output activation
    
# Memory Management (RTX 3070 constraints)
memory:
  max_vram_usage_gb: 7.0         # Maximum VRAM usage (7GB limit)
  max_ram_usage_gb: 6.0          # Maximum RAM usage
  batch_size: 8                  # Training batch size
  gradient_accumulation: 4       # Gradient accumulation steps
  mixed_precision: true          # Use mixed precision (FP16)
  
# Model Optimization
optimization:
  # Model size optimization
  parameter_sharing: true        # Share parameters where possible
  weight_pruning: false          # Weight pruning (post-training)
  quantization: false            # Model quantization (post-training)
  
  # Memory optimization
  gradient_checkpointing: true   # Gradient checkpointing
  activation_checkpointing: true # Activation checkpointing
  
# Training Configuration (for architecture validation)
training:
  learning_rate: 0.001           # Initial learning rate
  optimizer: "adamw"             # Optimizer type
  weight_decay: 0.01             # Weight decay
  scheduler: "cosine"            # Learning rate scheduler
  warmup_steps: 1000             # Warmup steps
  
# Loss Function Configuration
loss:
  primary_loss: "cross_entropy"  # Primary loss function
  label_smoothing: 0.1           # Label smoothing factor
  class_weights: [1.0, 1.2, 1.5, 1.5, 1.2]  # Class weights for imbalanced data
  
# Validation Parameters
validation:
  validation_split: 0.2          # Validation data split
  early_stopping_patience: 10    # Early stopping patience
  metric_monitoring: "accuracy"  # Primary metric to monitor
  
# Processing Parameters
processing:
  max_workers: 2                 # Parallel processing workers
  timeout_seconds: 120           # Timeout per model creation
  retry_attempts: 2              # Retry attempts for failed operations
  
# Quality Gates
quality_gates:
  min_model_creation_rate: 0.95  # Minimum 95% model creation success rate
  max_model_size_mb: 500         # Maximum model size (MB)
  max_inference_time_ms: 100     # Maximum inference time per sample (ms)
  
# Logging Configuration
logging:
  level: "INFO"                  # Logging level
  format: "%(asctime)s - %(levelname)s - %(message)s"
  file_encoding: "utf-8"         # Unicode support
  max_log_size_mb: 100           # Maximum log file size
  backup_count: 5                # Number of backup log files
  
# Error Handling
error_handling:
  continue_on_error: true        # Continue processing if individual operations fail
  save_error_details: true      # Save detailed error information
  create_error_report: true     # Generate error summary report
  
# Output Format
output:
  save_model_configs: true       # Save model configuration files
  save_architecture_specs: true # Save architecture specifications
  save_memory_profiles: true    # Save memory usage profiles
  create_model_summary: true    # Create model architecture summary
  export_onnx: false             # Export to ONNX format (optional)
  
# Architecture Variants (for experimentation)
variants:
  lightweight:
    encoder_channels: [32, 64, 128, 128, 64, 32]
    decoder_channels: [128, 64, 32, 16]
    attention_heads: 4
    embed_dim: 128
    
  standard:
    encoder_channels: [64, 128, 256, 256, 128, 64]
    decoder_channels: [256, 128, 64, 32]
    attention_heads: 8
    embed_dim: 256
    
  enhanced:
    encoder_channels: [128, 256, 512, 512, 256, 128]
    decoder_channels: [512, 256, 128, 64]
    attention_heads: 16
    embed_dim: 512
