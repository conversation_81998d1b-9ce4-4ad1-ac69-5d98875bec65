# 🧩 Phase 7: Basic Note Type Classification

**Status**: 📋 Planned  
**Estimated Duration**: 4 days  
**Dependencies**: [Phase 6.5: Advanced Feature Extraction](phase_06_5_feature_extraction.md)
**Next Phase**: [Phase 8: Note Sequence Pattern Learning](phase_08_sequence_patterns.md)

---

## 1. **Phase Purpose**

This phase trains machine learning models to classify note candidates into basic TJA note types (don, ka, rest, big notes). This step is isolated because:

- **Note classification** requires supervised learning with labeled training data
- **Feature engineering** needs audio-specific expertise for optimal performance
- **Model architecture** must balance accuracy with inference speed
- **Class imbalance handling** is critical for rare note types (big notes, special notes)

**Why Isolated**: Note classification is the first ML training phase requiring different expertise than signal processing. The classification results form the foundation for all subsequent pattern learning phases.

---

## 2. **Inputs & Outputs**

### **Inputs**
```python
# From Phase 6.5 (exact format match)
advanced_features: List[Dict] = [   # ML-ready feature vectors
    {
        "candidate_id": int,            # Matching candidate ID from Phase 6
        "window_id": int,               # Original window identifier
        "feature_vector": np.ndarray,   # Combined feature vector (shape: [128,])
        "spectral_features": Dict,      # Detailed spectral features
        "rhythmic_features": Dict,      # Rhythmic analysis features
        "harmonic_features": Dict,      # Harmonic content features
        "energy_features": Dict,        # Energy-based features
        "contextual_features": Dict,    # Context-aware features
        "quality_metrics": Dict         # Feature quality assessment
    }
]

# Training labels (from TJA parsing)
training_labels: List[Dict] = [
    {
        "candidate_id": int,
        "true_note_type": str,      # "don", "ka", "rest", "big_don", "big_ka"
        "confidence": float,        # Label confidence (1.0 for clear cases)
        "context": Dict,            # Surrounding notes for context
        "timing_accuracy": float    # Timing alignment accuracy (0-1)
    }
]

# Input directory structure (matching Phase 6.5 outputs)
data\\processed\\phase6_5\\
├── advanced_features\\*.json       # ML-ready feature dictionaries
├── feature_matrices\\              # NumPy arrays for ML training
│   ├── {filename}_features.npy    # Feature matrix (N_candidates x 128)
│   ├── {filename}_spectrograms.npy # Mel spectrograms (N_candidates x 80 x 32)
│   └── ...
├── extraction_stats\\*.json        # Feature extraction statistics
└── extraction_summary.json         # Overall extraction statistics
```

### **Outputs**
```python
# Trained classification model
note_classifier: Dict = {
    "model": torch.nn.Module,       # Trained PyTorch model
    "feature_scaler": StandardScaler, # Feature normalization
    "label_encoder": LabelEncoder,  # Note type encoding
    "model_metadata": {
        "architecture": str,        # Model architecture name
        "input_features": int,      # Number of input features
        "num_classes": int,         # Number of note types
        "training_samples": int,    # Training dataset size
        "validation_accuracy": float, # Best validation accuracy
        "class_weights": Dict       # Class balancing weights
    }
}

# Classified notes (for Phase 8 compatibility)
classified_notes: List[Dict] = [
    {
        "note_id": int,                 # Sequential note identifier
        "candidate_id": int,            # Original candidate ID from Phase 6
        "time_position": float,         # Time position in seconds
        "predicted_note_type": str,     # Predicted note type ('don', 'ka', 'rest', etc.)
        "prediction_confidence": float, # Model confidence (0-1)
        "beat_alignment": float,        # Alignment to nearest beat
        "measure_position": float,      # Position within measure (0-1)
        "context_features": Dict        # Contextual features used
    }
]

# Note predictions (detailed classification results for Phase 8)
note_predictions: List[Dict] = [
    {
        "note_id": int,                 # Matching note_id from classified_notes
        "candidate_id": int,            # Original candidate ID
        "class_probabilities": Dict,    # Probability for each note type
        "feature_importance": Dict,     # Feature contribution scores
        "prediction_time": float,       # Inference time (ms)
        "model_version": str,           # Model version used
        "preprocessing_applied": List[str] # Preprocessing steps applied
    }
]

# Output directory structure (Windows-compatible paths)
data\\processed\\phase7\\
├── models\\
│   ├── note_classifier.pth        # Trained model weights
│   ├── feature_scaler.pkl         # Feature normalization
│   └── model_metadata.json        # Model information
├── classified_notes\\              # Classified notes for Phase 8
│   ├── {filename}.json            # List of classified_notes Dicts
│   └── ...
├── predictions\\                   # Detailed prediction results for Phase 8
│   ├── {filename}.json            # List of note_predictions Dicts
│   └── ...
├── training_data\\                 # Processed training data
│   ├── features.npy               # Feature matrix
│   ├── labels.npy                 # Label vector
│   └── train_test_split.json      # Data split information
└── classification_report.json     # Training and evaluation results
```

---

## 3. **Implementation Plan**

### **Recommended Tools**
```python
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix
from sklearn.utils.class_weight import compute_class_weight
import librosa
from pathlib import Path
import json
import pickle
from tqdm import tqdm
import matplotlib.pyplot as plt
import seaborn as sns
```

### **Feature Engineering Pipeline**
```python
def extract_classification_features(
    audio_snippet: np.ndarray,
    sr: int = 22050,
    candidate_info: Dict = None
) -> np.ndarray:
    """
    Extract comprehensive features for note classification.
    
    Args:
        audio_snippet: Audio data for the candidate window
        sr: Sample rate
        candidate_info: Additional candidate metadata
        
    Returns:
        Feature vector for classification
    """
    
    features = []
    
    if len(audio_snippet) == 0:
        return np.zeros(50)  # Return zero features for empty audio
    
    # 1. Temporal features
    # RMS energy
    rms = librosa.feature.rms(y=audio_snippet)[0]
    features.extend([
        np.mean(rms),
        np.std(rms),
        np.max(rms),
        np.min(rms)
    ])
    
    # Zero crossing rate
    zcr = librosa.feature.zero_crossing_rate(audio_snippet)[0]
    features.extend([
        np.mean(zcr),
        np.std(zcr)
    ])
    
    # 2. Spectral features
    # Spectral centroid (brightness)
    spectral_centroid = librosa.feature.spectral_centroid(y=audio_snippet, sr=sr)[0]
    features.extend([
        np.mean(spectral_centroid),
        np.std(spectral_centroid)
    ])
    
    # Spectral rolloff
    spectral_rolloff = librosa.feature.spectral_rolloff(y=audio_snippet, sr=sr)[0]
    features.extend([
        np.mean(spectral_rolloff),
        np.std(spectral_rolloff)
    ])
    
    # Spectral bandwidth
    spectral_bandwidth = librosa.feature.spectral_bandwidth(y=audio_snippet, sr=sr)[0]
    features.extend([
        np.mean(spectral_bandwidth),
        np.std(spectral_bandwidth)
    ])
    
    # 3. MFCC features (first 13 coefficients)
    mfcc = librosa.feature.mfcc(y=audio_snippet, sr=sr, n_mfcc=13)
    mfcc_features = []
    for i in range(13):
        mfcc_features.extend([
            np.mean(mfcc[i]),
            np.std(mfcc[i])
        ])
    features.extend(mfcc_features)
    
    # 4. Onset strength
    onset_strength = librosa.onset.onset_strength(y=audio_snippet, sr=sr)
    features.extend([
        np.mean(onset_strength),
        np.max(onset_strength),
        np.std(onset_strength)
    ])
    
    # 5. Contextual features (if available)
    if candidate_info:
        features.extend([
            candidate_info.get("beat_position", 0.0),
            candidate_info.get("onset_strength", 0.0),
            1.0 if candidate_info.get("beat_subdivision") == "quarter" else 0.0,
            1.0 if candidate_info.get("beat_subdivision") == "eighth" else 0.0,
            candidate_info.get("candidate_confidence", 0.0)
        ])
    else:
        features.extend([0.0] * 5)
    
    return np.array(features, dtype=np.float32)

class NoteClassificationDataset(Dataset):
    """PyTorch dataset for note classification."""
    
    def __init__(self, features: np.ndarray, labels: np.ndarray):
        self.features = torch.FloatTensor(features)
        self.labels = torch.LongTensor(labels)
    
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return self.features[idx], self.labels[idx]

class OptimizedNoteClassifier(nn.Module):
    """
    Optimized neural network for note type classification.

    Features:
    - Attention mechanism for feature importance
    - Progressive dropout for regularization
    - BatchNorm for training stability
    - Memory-efficient architecture for RTX 3070
    """

    def __init__(self, input_size: int, num_classes: int = 5, dropout_rates=[0.3, 0.4, 0.2, 0.3]):
        super(OptimizedNoteClassifier, self).__init__()

        # Feature extraction with progressive dropout and batch normalization
        self.feature_extractor = nn.Sequential(
            nn.Linear(input_size, 256),
            nn.BatchNorm1d(256),
            nn.ReLU(),
            nn.Dropout(dropout_rates[0]),

            nn.Linear(256, 128),
            nn.BatchNorm1d(128),
            nn.ReLU(),
            nn.Dropout(dropout_rates[1]),

            nn.Linear(128, 64),
            nn.BatchNorm1d(64),
            nn.ReLU(),
            nn.Dropout(dropout_rates[2])
        )

        # Multi-head attention for feature importance
        self.attention = nn.MultiheadAttention(
            embed_dim=64,
            num_heads=4,  # Optimized for RTX 3070 memory
            dropout=0.1,
            batch_first=True
        )

        # Classification head with residual connection
        self.classifier = nn.Sequential(
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Dropout(dropout_rates[3]),
            nn.Linear(32, num_classes)
        )

        # Initialize weights using Xavier initialization
        self.apply(self._init_weights)

    def _init_weights(self, module):
        """Initialize model weights for better convergence."""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)

    def forward(self, x):
        # Extract features
        features = self.feature_extractor(x)

        # Apply attention mechanism (reshape for attention)
        features_reshaped = features.unsqueeze(1)  # Add sequence dimension
        attended_features, attention_weights = self.attention(
            features_reshaped, features_reshaped, features_reshaped
        )
        attended_features = attended_features.squeeze(1)  # Remove sequence dimension

        # Classification with residual connection
        output = self.classifier(attended_features + features)  # Residual connection

        return output, attention_weights

# Enhanced Dataset Classes for Augmentation
class EnhancedNoteClassificationDataset(Dataset):
    """Enhanced dataset with mixup and cutmix augmentation."""

    def __init__(self, features, labels, mixup_alpha=0.2, cutmix_alpha=1.0):
        self.features = torch.FloatTensor(features)
        self.labels = torch.LongTensor(labels)
        self.mixup_alpha = mixup_alpha
        self.cutmix_alpha = cutmix_alpha

    def __len__(self):
        return len(self.features)

    def __getitem__(self, idx):
        feature = self.features[idx]
        label = self.labels[idx]

        # Apply mixup augmentation with probability
        if random.random() < 0.3 and self.mixup_alpha > 0:
            # Select random sample for mixing
            mix_idx = random.randint(0, len(self.features) - 1)
            mix_feature = self.features[mix_idx]
            mix_label = self.labels[mix_idx]

            # Generate mixing coefficient
            lam = np.random.beta(self.mixup_alpha, self.mixup_alpha)

            # Mix features
            feature = lam * feature + (1 - lam) * mix_feature

            # Return mixed sample with both labels
            return feature, (label, mix_label, lam)

        return feature, label

class LabelSmoothingCrossEntropy(nn.Module):
    """Label smoothing cross entropy loss for better generalization."""

    def __init__(self, num_classes, smoothing=0.1, weight=None):
        super(LabelSmoothingCrossEntropy, self).__init__()
        self.num_classes = num_classes
        self.smoothing = smoothing
        self.weight = weight

    def forward(self, pred, target):
        # Handle mixup targets
        if isinstance(target, tuple):
            target_a, target_b, lam = target
            loss_a = self._smooth_loss(pred, target_a)
            loss_b = self._smooth_loss(pred, target_b)
            return lam * loss_a + (1 - lam) * loss_b
        else:
            return self._smooth_loss(pred, target)

    def _smooth_loss(self, pred, target):
        log_prob = F.log_softmax(pred, dim=-1)

        # Create smoothed target distribution
        smooth_target = torch.zeros_like(log_prob)
        smooth_target.fill_(self.smoothing / (self.num_classes - 1))
        smooth_target.scatter_(1, target.unsqueeze(1), 1.0 - self.smoothing)

        # Apply class weights if provided
        if self.weight is not None:
            smooth_target = smooth_target * self.weight.unsqueeze(0)

        return torch.mean(torch.sum(-smooth_target * log_prob, dim=-1))

class TrainingMonitor:
    """Monitor training progress and quality gates."""

    def __init__(self, phase_name, config):
        self.phase_name = phase_name
        self.config = config
        self.metrics_history = defaultdict(list)
        self.best_metric = 0.0
        self.patience_counter = 0

        # Quality gates for Phase 7
        self.quality_gates = {
            "min_accuracy": 0.85,
            "min_f1_score": 0.80,
            "max_loss": 0.5
        }

    def log_metrics(self, epoch, metrics):
        """Log metrics and check quality gates."""

        # Store metrics
        for key, value in metrics.items():
            self.metrics_history[key].append(value)

        # Check for improvement
        current_metric = metrics.get("val_accuracy", 0.0)
        if current_metric > self.best_metric:
            self.best_metric = current_metric
            self.patience_counter = 0
            save_checkpoint = True
        else:
            self.patience_counter += 1
            save_checkpoint = False

        # Check quality gates
        gates_passed = self._check_quality_gates(metrics)

        # Early stopping
        early_stop = self.patience_counter >= self.config.get("patience", 15)

        return {
            "status": "early_stop" if early_stop else "continue",
            "save_checkpoint": save_checkpoint,
            "gates_passed": gates_passed
        }

    def _check_quality_gates(self, metrics):
        """Check if current metrics meet quality gates."""

        for gate_name, threshold in self.quality_gates.items():
            metric_name = gate_name.replace("min_", "").replace("max_", "")

            if metric_name in metrics:
                value = metrics[metric_name]
                if gate_name.startswith("min_") and value < threshold:
                    return False
                elif gate_name.startswith("max_") and value > threshold:
                    return False

        return True

class CheckpointManager:
    """Manage model checkpoints and saving."""

    def __init__(self, save_dir, model_name):
        self.save_dir = Path(save_dir)
        self.model_name = model_name
        self.save_dir.mkdir(parents=True, exist_ok=True)

    def save_checkpoint(self, model, optimizer, scheduler, epoch, metrics, is_best=False):
        """Save model checkpoint."""

        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict(),
            'metrics': metrics,
            'timestamp': datetime.now().isoformat()
        }

        # Save regular checkpoint
        checkpoint_path = self.save_dir / f"{self.model_name}_epoch_{epoch}.pth"
        torch.save(checkpoint, checkpoint_path)

        # Save best model
        if is_best:
            best_path = self.save_dir / f"{self.model_name}_best.pth"
            torch.save(checkpoint, best_path)

        # Cleanup old checkpoints (keep last 3)
        self._cleanup_old_checkpoints()

    def _cleanup_old_checkpoints(self, keep_last=3):
        """Remove old checkpoint files."""
        checkpoint_files = list(self.save_dir.glob(f"{self.model_name}_epoch_*.pth"))
        if len(checkpoint_files) > keep_last:
            checkpoint_files.sort(key=lambda x: int(x.stem.split('_')[-1]))
            for old_checkpoint in checkpoint_files[:-keep_last]:
                old_checkpoint.unlink()

def train_optimized_note_classifier(
    training_data: Dict,
    config: Optional[Dict] = None,
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
) -> Dict:
    """
    Train the optimized note classification model with RTX 3070 optimizations.

    Args:
        training_data: Dictionary with features and labels
        config: Training configuration dictionary
        device: Training device (cuda/cpu)

    Returns:
        Trained model and comprehensive training results
    """

    # Default RTX 3070 optimized configuration
    if config is None:
        config = {
            "model_params": {
                "dropout_rates": [0.3, 0.4, 0.2, 0.3]
            },
            "training_params": {
                "batch_size": 64,  # Optimized for RTX 3070
                "learning_rate": 0.001,
                "weight_decay": 1e-4,
                "epochs": 100,
                "patience": 15,
                "validation_split": 0.2
            },
            "optimization": {
                "mixed_precision": True,  # FP16 for memory efficiency
                "gradient_clip_val": 1.0,
                "label_smoothing": 0.1
            },
            "augmentation": {
                "mixup_alpha": 0.2,
                "cutmix_alpha": 1.0
            }
        }

    # Prepare data with enhanced preprocessing
    X = training_data["features"]
    y = training_data["labels"]

    # Split data with stratification
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, test_size=config["training_params"]["validation_split"],
        random_state=42, stratify=y
    )

    # Enhanced feature normalization
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)

    # Create enhanced datasets with augmentation
    train_dataset = EnhancedNoteClassificationDataset(
        X_train_scaled, y_train,
        mixup_alpha=config["augmentation"]["mixup_alpha"],
        cutmix_alpha=config["augmentation"]["cutmix_alpha"]
    )
    val_dataset = NoteClassificationDataset(X_val_scaled, y_val)

    # RTX 3070 optimized data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=config["training_params"]["batch_size"],
        shuffle=True,
        num_workers=4,  # Optimal for RTX 3070
        pin_memory=True,  # Faster GPU transfer
        prefetch_factor=2
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=config["training_params"]["batch_size"] * 2,  # Larger batch for validation
        shuffle=False,
        num_workers=4,
        pin_memory=True
    )

    # Initialize optimized model
    num_classes = len(np.unique(y))
    input_size = X.shape[1]
    model = OptimizedNoteClassifier(
        input_size,
        num_classes,
        dropout_rates=config["model_params"]["dropout_rates"]
    ).to(device)

    # Calculate class weights for imbalanced data
    from sklearn.utils.class_weight import compute_class_weight
    class_weights = compute_class_weight(
        'balanced', classes=np.unique(y_train), y=y_train
    )
    class_weights = torch.FloatTensor(class_weights).to(device)

    # Enhanced loss function with label smoothing
    criterion = LabelSmoothingCrossEntropy(
        num_classes=num_classes,
        smoothing=config["optimization"]["label_smoothing"],
        weight=class_weights
    )

    # Optimized optimizer and scheduler
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config["training_params"]["learning_rate"],
        weight_decay=config["training_params"]["weight_decay"]
    )

    # CosineAnnealingWarmRestarts for better convergence
    scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-6
    )

    # Mixed precision training for RTX 3070
    scaler = torch.cuda.amp.GradScaler() if config["optimization"]["mixed_precision"] else None
    
    # Enhanced training loop with quality gate monitoring
    training_monitor = TrainingMonitor("phase7", config["training_params"])
    checkpoint_manager = CheckpointManager("models\\phase7", "note_classifier")

    train_losses = []
    val_losses = []
    val_accuracies = []
    val_f1_scores = []
    best_val_acc = 0.0
    early_stopping_patience = 0

    for epoch in range(config["training_params"]["epochs"]):
        # Training phase with mixed precision
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0

        progress_bar = tqdm(train_loader, desc=f"Epoch {epoch+1}/{config['training_params']['epochs']}")

        for batch_features, batch_labels in progress_bar:
            batch_features, batch_labels = batch_features.to(device), batch_labels.to(device)

            optimizer.zero_grad()

            # Mixed precision forward pass
            if scaler is not None:
                with torch.cuda.amp.autocast():
                    outputs, attention_weights = model(batch_features)
                    loss = criterion(outputs, batch_labels)

                # Mixed precision backward pass
                scaler.scale(loss).backward()

                # Gradient clipping
                if config["optimization"]["gradient_clip_val"] > 0:
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(
                        model.parameters(),
                        config["optimization"]["gradient_clip_val"]
                    )

                scaler.step(optimizer)
                scaler.update()
            else:
                outputs, attention_weights = model(batch_features)
                loss = criterion(outputs, batch_labels)
                loss.backward()

                # Gradient clipping
                if config["optimization"]["gradient_clip_val"] > 0:
                    torch.nn.utils.clip_grad_norm_(
                        model.parameters(),
                        config["optimization"]["gradient_clip_val"]
                    )

                optimizer.step()

            # Update metrics
            train_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            train_total += batch_labels.size(0)
            train_correct += (predicted == batch_labels).sum().item()

            # Update progress bar
            progress_bar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100.*train_correct/train_total:.2f}%'
            })

        # Validation phase with comprehensive metrics
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        all_predictions = []
        all_labels = []

        with torch.no_grad():
            for batch_features, batch_labels in val_loader:
                batch_features, batch_labels = batch_features.to(device), batch_labels.to(device)

                if scaler is not None:
                    with torch.cuda.amp.autocast():
                        outputs, _ = model(batch_features)
                        loss = criterion(outputs, batch_labels)
                else:
                    outputs, _ = model(batch_features)
                    loss = criterion(outputs, batch_labels)

                val_loss += loss.item()

                _, predicted = torch.max(outputs.data, 1)
                val_total += batch_labels.size(0)
                val_correct += (predicted == batch_labels).sum().item()

                # Collect predictions for F1 score calculation
                all_predictions.extend(predicted.cpu().numpy())
                all_labels.extend(batch_labels.cpu().numpy())
        
        # Calculate comprehensive metrics
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        train_accuracy = train_correct / train_total
        val_accuracy = val_correct / val_total

        # Calculate F1 score
        from sklearn.metrics import f1_score
        val_f1 = f1_score(all_labels, all_predictions, average='weighted')

        # Store metrics
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        val_accuracies.append(val_accuracy)
        val_f1_scores.append(val_f1)

        # Prepare metrics for monitoring
        epoch_metrics = {
            "epoch": epoch + 1,
            "train_loss": avg_train_loss,
            "val_loss": avg_val_loss,
            "train_accuracy": train_accuracy,
            "val_accuracy": val_accuracy,
            "val_f1_score": val_f1,
            "learning_rate": optimizer.param_groups[0]['lr']
        }

        # Quality gate monitoring and early stopping
        monitor_result = training_monitor.log_metrics(epoch, epoch_metrics)

        # Learning rate scheduling
        scheduler.step()

        # Checkpoint management
        is_best = val_accuracy > best_val_acc
        if is_best:
            best_val_acc = val_accuracy
            best_model_state = model.state_dict().copy()

        # Save checkpoint if needed
        if monitor_result.get("save_checkpoint", False) or is_best:
            checkpoint_manager.save_checkpoint(
                model, optimizer, scheduler, epoch, epoch_metrics, is_best
            )

        # Early stopping check
        if monitor_result["status"] == "early_stop":
            print(f"Early stopping triggered at epoch {epoch + 1}")
            break

        # Print progress with enhanced metrics
        if (epoch + 1) % 5 == 0:
            print(f"Epoch [{epoch+1}/{config['training_params']['epochs']}]")
            print(f"  Train Loss: {avg_train_loss:.4f}, Train Acc: {train_accuracy:.4f}")
            print(f"  Val Loss: {avg_val_loss:.4f}, Val Acc: {val_accuracy:.4f}, Val F1: {val_f1:.4f}")
            print(f"  LR: {optimizer.param_groups[0]['lr']:.6f}")

    # Load best model
    model.load_state_dict(best_model_state)

    # Final evaluation on validation set
    model.eval()
    final_predictions = []
    final_labels = []

    with torch.no_grad():
        for batch_features, batch_labels in val_loader:
            batch_features, batch_labels = batch_features.to(device), batch_labels.to(device)
            outputs, _ = model(batch_features)
            _, predicted = torch.max(outputs.data, 1)
            final_predictions.extend(predicted.cpu().numpy())
            final_labels.extend(batch_labels.cpu().numpy())

    # Generate comprehensive classification report
    from sklearn.metrics import classification_report, confusion_matrix
    class_names = ['rest', 'don', 'ka', 'big_don', 'big_ka']
    classification_rep = classification_report(
        final_labels, final_predictions,
        target_names=class_names, output_dict=True
    )

    return {
        "model": model,
        "scaler": scaler,
        "training_history": {
            "train_losses": train_losses,
            "val_losses": val_losses,
            "val_accuracies": val_accuracies,
            "val_f1_scores": val_f1_scores
        },
        "final_metrics": {
            "best_val_accuracy": best_val_acc,
            "final_val_f1": val_f1_scores[-1] if val_f1_scores else 0.0,
            "classification_report": classification_rep
        },
        "model_artifacts": {
            "class_weights": class_weights.cpu().numpy(),
            "feature_scaler": scaler,
            "config_used": config
        },
        "quality_gates_passed": monitor_result.get("gates_passed", False)
    }
```

### **Batch Processing Pipeline**
```python
def process_note_classification(
    input_dir: Path = Path("data\\processed\\phase6_5"),
    output_dir: Path = Path("data\\processed\\phase7"),
    tja_data_dir: Path = Path("data\\raw\\ese"),
    validation_split: float = 0.2,
    batch_size: int = 64
) -> Dict:
    """Process note classification for entire dataset."""
    
    # Setup output directories
    (output_dir / "models").mkdir(parents=True, exist_ok=True)
    (output_dir / "predictions").mkdir(parents=True, exist_ok=True)
    (output_dir / "training_data").mkdir(parents=True, exist_ok=True)
    
    # 1. Load and prepare training data
    print("Loading candidate data...")
    training_data = prepare_training_data(input_dir, tja_data_dir)
    
    # Save training data
    np.save(output_dir / "training_data" / "features.npy", training_data["features"])
    np.save(output_dir / "training_data" / "labels.npy", training_data["labels"])
    
    # 2. Train classification model
    print("Training note classifier...")
    training_results = train_note_classifier(
        training_data, validation_split, batch_size
    )
    
    # 3. Save trained model
    model_path = output_dir / "models" / "note_classifier.pth"
    torch.save(training_results["model"].state_dict(), model_path)
    
    scaler_path = output_dir / "models" / "feature_scaler.pkl"
    with open(scaler_path, 'wb') as f:
        pickle.dump(training_results["scaler"], f)
    
    # 4. Generate predictions on all candidates
    print("Generating predictions...")
    predictions = generate_predictions(
        training_results["model"], 
        training_results["scaler"],
        input_dir
    )
    
    # 5. Save results
    results = {
        "training_samples": len(training_data["features"]),
        "validation_accuracy": training_results["best_val_accuracy"],
        "num_classes": len(np.unique(training_data["labels"])),
        "feature_dimensions": training_data["features"].shape[1],
        "prediction_count": len(predictions)
    }
    
    with open(output_dir / "classification_report.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    return results

def prepare_training_data(input_dir: Path, tja_data_dir: Path) -> Dict:
    """Prepare training data from candidates and TJA labels."""
    
    features_list = []
    labels_list = []
    
    # Load all advanced feature files
    feature_files = list((input_dir / "advanced_features").glob("*.json"))

    for feature_file in tqdm(feature_files, desc="Processing features"):
        song_name = feature_file.stem

        # Load advanced features
        with open(feature_file, 'r') as f:
            advanced_features = json.load(f)

        # Load corresponding TJA labels
        tja_labels = load_tja_labels(song_name, tja_data_dir)

        if not tja_labels:
            continue

        # Process each feature set
        for feature_data in advanced_features:
            # Use pre-computed feature vector
            feature_vector = np.array(feature_data['feature_vector'])
            
            if audio_file.exists():
                audio_snippet = np.load(audio_file)
                
                # Extract features
                features = extract_classification_features(audio_snippet, 22050, candidate)
                
                # Find matching label
                label = find_matching_label(candidate, tja_labels)
                
                if label is not None:
                    features_list.append(features)
                    labels_list.append(label)
    
    # Convert to arrays
    features_array = np.array(features_list)
    labels_array = np.array(labels_list)
    
    return {
        "features": features_array,
        "labels": labels_array
    }

def load_tja_labels(song_name: str, tja_data_dir: Path) -> List[Dict]:
    """Load note labels from TJA file."""
    # This would parse the TJA file and extract note timing and types
    # Implementation depends on TJA parser from references
    try:
        # Placeholder - implement based on TJA parser
        return []
    except Exception as e:
        logging.error(f"Error loading TJA labels for {song_name}: {e}")
        return []

def find_matching_label(candidate: Dict, tja_labels: List[Dict]) -> Optional[int]:
    """Find the matching TJA label for a candidate window."""
    candidate_time = candidate["center_time"]
    
    # Find closest label within reasonable time window
    best_match = None
    min_distance = float('inf')
    
    for label in tja_labels:
        distance = abs(label["time"] - candidate_time)
        if distance < 0.1 and distance < min_distance:  # Within 100ms
            min_distance = distance
            best_match = label
    
    if best_match:
        # Convert note type to class index
        note_type_mapping = {
            "rest": 0,
            "don": 1,
            "ka": 2,
            "big_don": 3,
            "big_ka": 4
        }
        return note_type_mapping.get(best_match["note_type"], 0)
    
    return None
```

---

## 4. **Best Practices**

### **Feature Engineering**
- Combine temporal, spectral, and contextual features
- Use domain knowledge (beat position, onset strength)
- Normalize features to prevent scale dominance
- Include feature selection/importance analysis

### **Class Imbalance Handling**
```python
# Handle imbalanced classes
class_weights = compute_class_weight(
    'balanced', 
    classes=np.unique(y_train), 
    y=y_train
)

# Use weighted loss function
criterion = nn.CrossEntropyLoss(weight=torch.FloatTensor(class_weights))

# Alternative: Use SMOTE for oversampling
from imblearn.over_sampling import SMOTE
smote = SMOTE(random_state=42)
X_resampled, y_resampled = smote.fit_resample(X_train, y_train)
```

### **Model Architecture**
- Start with simple fully connected networks
- Add dropout for regularization
- Use batch normalization for stable training
- Consider ensemble methods for improved accuracy

### **Training Optimization**
- Use learning rate scheduling
- Implement early stopping
- Monitor validation metrics
- Save best model checkpoints

---

## 5. **Challenges & Pitfalls**

### **Label Quality**
- **Issue**: TJA parsing may introduce labeling errors
- **Symptoms**: Poor model performance despite good features
- **Mitigation**: Manual validation of training labels
- **Solution**: Implement label confidence scoring and filtering

### **Class Imbalance**
- **Issue**: Rest notes vastly outnumber actual notes
- **Example**: 80% rest, 15% don, 4% ka, 1% big notes
- **Mitigation**: Use class weighting and resampling techniques
- **Solution**: Focus on precision/recall rather than accuracy

### **Feature Relevance**
- **Issue**: Some features may not be discriminative for note types
- **Symptoms**: Model overfitting or poor generalization
- **Mitigation**: Feature importance analysis and selection
- **Solution**: Use domain knowledge to guide feature engineering

### **Temporal Context**
- **Issue**: Note classification may depend on surrounding notes
- **Example**: Patterns like don-ka-don are common
- **Mitigation**: Include contextual features in current phase
- **Solution**: Address fully in Phase 8 (Sequence Patterns)

---

## 5.5. **Iterative Training Strategy**

### **Multi-Execution Framework for Note Classification**

Note classification benefits significantly from iterative training due to the need for handling edge cases, class imbalance, and improving performance on misclassified samples through targeted retraining.

#### **Iterative Classification Training Architecture**
```python
class IterativeNoteClassifier:
    """
    Iterative training framework for note type classification.

    Features:
    - Focal loss for hard example mining
    - Data augmentation with progressive complexity
    - Active learning for edge case collection
    - Multi-stage fine-tuning with class balancing
    """

    def __init__(self, config: Dict):
        self.config = config
        self.paths = TJAGenPaths()
        self.logger = TJAGenLogger(7)

        # Iteration management
        self.current_iteration = 0
        self.max_iterations = config.get("max_iterations", 4)
        self.improvement_threshold = config.get("improvement_threshold", 0.01)

        # Model and training state
        self.model = None
        self.best_model_state = None
        self.optimizer = None
        self.scheduler = None

        # Training data management
        self.training_data = {
            "original": [],           # Original training data
            "augmented": [],          # Augmented training data
            "hard_examples": [],      # Misclassified samples for focused training
            "edge_cases": [],         # Edge cases (rapid hits, overlapping notes)
            "balanced_samples": []    # Class-balanced additional samples
        }

        # Class-specific tracking
        self.class_performance = {
            "don": {"accuracy": 0.0, "f1": 0.0, "samples": 0},
            "ka": {"accuracy": 0.0, "f1": 0.0, "samples": 0},
            "rest": {"accuracy": 0.0, "f1": 0.0, "samples": 0},
            "big_don": {"accuracy": 0.0, "f1": 0.0, "samples": 0},
            "big_ka": {"accuracy": 0.0, "f1": 0.0, "samples": 0}
        }

        # Performance tracking
        self.iteration_history = []
        self.best_overall_accuracy = 0.0
        self.best_f1_score = 0.0

        # Setup iteration directories
        self._setup_iteration_directories()

    def _setup_iteration_directories(self):
        """Create directories for iterative training artifacts."""
        base_dir = self.paths.get_phase_output_dir(7)

        directories = [
            base_dir / "iterations",
            base_dir / "iterations" / "models",
            base_dir / "iterations" / "augmented_data",
            base_dir / "iterations" / "hard_examples",
            base_dir / "iterations" / "edge_cases",
            base_dir / "iterations" / "metrics",
            base_dir / "iterations" / "confusion_matrices"
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

    def execute_iterative_training(self, initial_data: Dict) -> Dict:
        """
        Execute complete iterative training pipeline for note classification.

        Args:
            initial_data: Initial training data with features and labels

        Returns:
            Final training results with iteration history
        """

        self.logger.info("🔄 Starting iterative note classification training")

        # Initialize with original data
        self.training_data["original"] = initial_data

        iteration_results = []

        for iteration in range(self.max_iterations):
            self.current_iteration = iteration
            self.logger.info(f"🔄 Starting iteration {iteration + 1}/{self.max_iterations}")

            # Execute single iteration
            iteration_result = self._execute_classification_iteration()
            iteration_results.append(iteration_result)

            # Update iteration history
            self.iteration_history.append(iteration_result)

            # Check for convergence
            if self._check_classification_convergence(iteration_result):
                self.logger.info(f"✅ Classification converged after {iteration + 1} iterations")
                break

            # Prepare data for next iteration
            self._prepare_next_classification_iteration(iteration_result)

        # Generate final results
        final_results = self._generate_final_classification_results(iteration_results)

        self.logger.info("🎉 Iterative classification training completed")
        return final_results

    def _execute_classification_iteration(self) -> Dict:
        """Execute a single classification training iteration."""

        iteration_start_time = time.time()

        # 1. Apply progressive data augmentation
        augmented_data = self._apply_progressive_augmentation()

        # 2. Prepare training data with class balancing
        balanced_data = self._prepare_balanced_training_data(augmented_data)

        # 3. Train model with focal loss for hard examples
        training_results = self._train_with_focal_loss(balanced_data)

        # 4. Identify misclassified samples and edge cases
        hard_examples = self._identify_misclassified_samples(training_results)
        edge_cases = self._collect_edge_cases(training_results)

        # 5. Evaluate per-class performance
        class_metrics = self._evaluate_per_class_performance()

        # 6. Update class-specific performance tracking
        self._update_class_performance_tracking(class_metrics)

        # 7. Save iteration artifacts
        self._save_classification_iteration_artifacts(
            training_results, hard_examples, edge_cases, class_metrics
        )

        iteration_time = time.time() - iteration_start_time

        return {
            "iteration": self.current_iteration,
            "training_time": iteration_time,
            "training_results": training_results,
            "hard_examples_found": len(hard_examples),
            "edge_cases_collected": len(edge_cases),
            "class_metrics": class_metrics,
            "overall_accuracy": training_results["best_val_accuracy"],
            "overall_f1": training_results["final_metrics"]["final_val_f1"],
            "data_statistics": self._get_classification_data_statistics()
        }

    def _apply_progressive_augmentation(self) -> Dict:
        """Apply progressive data augmentation based on iteration."""

        # Define augmentation strategies by iteration
        augmentation_strategies = {
            0: {  # Iteration 0: Basic augmentation
                "pitch_shift": {"range": [-1, 1], "probability": 0.3},
                "tempo_change": {"range": [0.95, 1.05], "probability": 0.2},
                "noise_injection": {"snr_range": [20, 40], "probability": 0.1}
            },
            1: {  # Iteration 1: Moderate augmentation
                "pitch_shift": {"range": [-2, 2], "probability": 0.4},
                "tempo_change": {"range": [0.9, 1.1], "probability": 0.3},
                "noise_injection": {"snr_range": [15, 35], "probability": 0.2},
                "time_stretch": {"range": [0.95, 1.05], "probability": 0.2}
            },
            2: {  # Iteration 2: Aggressive augmentation
                "pitch_shift": {"range": [-3, 3], "probability": 0.5},
                "tempo_change": {"range": [0.85, 1.15], "probability": 0.4},
                "noise_injection": {"snr_range": [10, 30], "probability": 0.3},
                "time_stretch": {"range": [0.9, 1.1], "probability": 0.3},
                "frequency_masking": {"max_mask_pct": 0.1, "probability": 0.2}
            },
            3: {  # Iteration 3: Extreme augmentation for robustness
                "pitch_shift": {"range": [-4, 4], "probability": 0.6},
                "tempo_change": {"range": [0.8, 1.2], "probability": 0.5},
                "noise_injection": {"snr_range": [5, 25], "probability": 0.4},
                "time_stretch": {"range": [0.85, 1.15], "probability": 0.4},
                "frequency_masking": {"max_mask_pct": 0.15, "probability": 0.3},
                "mixup": {"alpha": 0.2, "probability": 0.2}
            }
        }

        current_strategy = augmentation_strategies.get(
            self.current_iteration,
            augmentation_strategies[3]  # Use most aggressive for iterations > 3
        )

        # Apply augmentation to original data
        augmented_samples = []

        for sample in self.training_data["original"]["features"]:
            # Apply each augmentation with specified probability
            augmented_sample = self._apply_augmentation_pipeline(sample, current_strategy)
            augmented_samples.append(augmented_sample)

        self.logger.info(f"🎵 Applied {len(current_strategy)} augmentation types to {len(augmented_samples)} samples")

        return {
            "strategy_used": current_strategy,
            "augmented_samples": augmented_samples,
            "augmentation_count": len(augmented_samples)
        }

    def _train_with_focal_loss(self, training_data: Dict) -> Dict:
        """Train model using focal loss to focus on hard examples."""

        # Implement focal loss for addressing class imbalance and hard examples
        class FocalLoss(nn.Module):
            def __init__(self, alpha=1, gamma=2, num_classes=5):
                super(FocalLoss, self).__init__()
                self.alpha = alpha
                self.gamma = gamma
                self.num_classes = num_classes

            def forward(self, inputs, targets):
                ce_loss = F.cross_entropy(inputs, targets, reduction='none')
                pt = torch.exp(-ce_loss)
                focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
                return focal_loss.mean()

        # Initialize model if first iteration
        if self.model is None:
            self.model = OptimizedNoteClassifier(
                input_size=training_data["features"].shape[1],
                num_classes=5,
                dropout_rates=self.config["model_params"]["dropout_rates"]
            ).to(self.device)

        # Setup focal loss with class weights
        criterion = FocalLoss(alpha=1, gamma=2, num_classes=5)

        # Setup optimizer with iteration-specific learning rate
        base_lr = self.config["training_params"]["learning_rate"]
        iteration_lr = base_lr * (0.5 ** self.current_iteration)  # Decay LR each iteration

        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=iteration_lr,
            weight_decay=self.config["training_params"]["weight_decay"]
        )

        # Training loop with focal loss
        training_results = self._execute_focal_loss_training(
            training_data, criterion, self.optimizer
        )

        return training_results

    def _identify_misclassified_samples(self, training_results: Dict) -> List[Dict]:
        """Identify misclassified samples for focused retraining."""

        hard_examples = []

        # Get validation predictions
        val_predictions = training_results.get("validation_predictions", [])
        val_labels = training_results.get("validation_labels", [])
        val_features = training_results.get("validation_features", [])

        for i, (pred, true_label, features) in enumerate(zip(val_predictions, val_labels, val_features)):
            if pred != true_label:
                # Calculate prediction confidence
                pred_probs = training_results.get("validation_probabilities", [])[i]
                confidence = np.max(pred_probs)

                # Identify as hard example if low confidence or specific error patterns
                if confidence < 0.7 or self._is_systematic_error(pred, true_label):
                    hard_example = {
                        "features": features,
                        "true_label": true_label,
                        "predicted_label": pred,
                        "confidence": confidence,
                        "error_type": self._classify_error_type(pred, true_label),
                        "iteration_identified": self.current_iteration
                    }
                    hard_examples.append(hard_example)

        # Sort by confidence (lowest first) and limit count
        hard_examples.sort(key=lambda x: x["confidence"])
        max_hard_examples = self.config.get("max_hard_examples_per_iteration", 200)
        hard_examples = hard_examples[:max_hard_examples]

        self.logger.info(f"🎯 Identified {len(hard_examples)} hard examples")
        return hard_examples

    def _collect_edge_cases(self, training_results: Dict) -> List[Dict]:
        """Collect edge cases for specialized training."""

        edge_cases = []

        # Define edge case patterns
        edge_case_patterns = {
            "rapid_consecutive": {
                "description": "Rapid consecutive hits (>8 notes/second)",
                "detection_method": self._detect_rapid_consecutive
            },
            "overlapping_notes": {
                "description": "Overlapping note boundaries",
                "detection_method": self._detect_overlapping_notes
            },
            "low_amplitude": {
                "description": "Very quiet notes near silence threshold",
                "detection_method": self._detect_low_amplitude_notes
            },
            "cross_class_confusion": {
                "description": "Systematic confusion between specific classes",
                "detection_method": self._detect_cross_class_confusion
            }
        }

        # Apply each edge case detection method
        for pattern_name, pattern_config in edge_case_patterns.items():
            detected_cases = pattern_config["detection_method"](training_results)

            for case in detected_cases:
                edge_case = {
                    "pattern_type": pattern_name,
                    "description": pattern_config["description"],
                    "features": case["features"],
                    "label": case["label"],
                    "metadata": case.get("metadata", {}),
                    "iteration_collected": self.current_iteration
                }
                edge_cases.append(edge_case)

        self.logger.info(f"🔍 Collected {len(edge_cases)} edge cases")
        return edge_cases

# Iterative training configuration for note classification
ITERATIVE_CLASSIFICATION_CONFIG = {
    "max_iterations": 4,
    "improvement_threshold": 0.01,  # 1% improvement threshold
    "target_accuracy": 0.92,        # 92% target accuracy
    "target_f1_score": 0.90,        # 90% target F1 score

    # Class-specific targets
    "class_targets": {
        "don": {"min_accuracy": 0.95, "min_f1": 0.93},
        "ka": {"min_accuracy": 0.95, "min_f1": 0.93},
        "rest": {"min_accuracy": 0.90, "min_f1": 0.88},
        "big_don": {"min_accuracy": 0.88, "min_f1": 0.85},
        "big_ka": {"min_accuracy": 0.88, "min_f1": 0.85}
    },

    # Retraining triggers
    "retraining_triggers": {
        "accuracy_drop": 0.03,          # Retrain if accuracy drops by 3%
        "class_imbalance_threshold": 0.1, # Retrain if class F1 differs by >10%
        "new_hard_examples": 500,       # Retrain when 500 new hard examples found
        "edge_case_threshold": 100      # Retrain when 100 edge cases collected
    },

    # Focal loss configuration
    "focal_loss": {
        "alpha": 1.0,                   # Weighting factor
        "gamma": 2.0,                   # Focusing parameter
        "class_weights": "balanced"     # Use balanced class weights
    },

    # Data augmentation progression
    "augmentation_progression": {
        "iteration_0": "basic",         # Basic augmentation
        "iteration_1": "moderate",      # Moderate augmentation
        "iteration_2": "aggressive",    # Aggressive augmentation
        "iteration_3": "extreme"        # Extreme augmentation for robustness
    }
}
```

#### **Multi-Execution Workflow for Classification**

```mermaid
graph TD
    A[Initial Training Data] --> B[Iteration 0: Basic Augmentation]
    B --> C[Train with Focal Loss]
    C --> D[Identify Hard Examples]
    D --> E[Collect Edge Cases]
    E --> F[Evaluate Per-Class Performance]
    F --> G{Convergence Check}
    G -->|No| H[Iteration N+1: Enhanced Augmentation]
    G -->|Yes| I[Final Model Selection]
    H --> J[Update Training Data]
    J --> K[Apply Progressive Augmentation]
    K --> C
    I --> L[Deploy Best Model]

    style A fill:#e1f5fe
    style I fill:#c8e6c9
    style L fill:#c8e6c9
```

#### **Classification Performance Improvement Tracking**

```python
# Expected improvement targets per iteration
CLASSIFICATION_ITERATION_TARGETS = {
    "iteration_0": {
        "baseline_accuracy": 0.82,
        "target_accuracy": 0.87,
        "focus": "Basic note type distinction, simple patterns"
    },
    "iteration_1": {
        "baseline_accuracy": 0.87,
        "target_accuracy": 0.90,
        "focus": "Hard example mining, class balance improvement"
    },
    "iteration_2": {
        "baseline_accuracy": 0.90,
        "target_accuracy": 0.92,
        "focus": "Edge case handling, rapid consecutive notes"
    },
    "iteration_3": {
        "baseline_accuracy": 0.92,
        "target_accuracy": 0.94,
        "focus": "Robustness to noise, extreme augmentation"
    }
}

# Quality gates for iterative classification training
CLASSIFICATION_QUALITY_GATES = {
    "min_overall_accuracy": 0.92,      # 92% minimum overall accuracy
    "min_per_class_f1": 0.85,          # 85% minimum F1 for each class
    "max_class_imbalance": 0.1,        # Max 10% F1 difference between classes
    "min_hard_example_improvement": 0.2, # 20% improvement on hard examples
    "max_edge_case_error_rate": 0.15   # Max 15% error rate on edge cases
}
```

---

## 6. **Dependencies & Unlocks**

### **Prerequisites**
- **Phase 6.5 Complete**: Advanced features extracted from note candidates
- **Required Files**:
  - `data\\processed\\phase6_5\\advanced_features\\*.json`
  - `data\\processed\\phase6_5\\feature_matrices\\*_features.npy`
  - `data\\raw\\ese\\**\\*.tja` (for training labels)
- **Libraries**: `torch`, `sklearn`, `librosa` for ML training

### **What This Phase Unlocks**
- **Phase 8**: Classified notes enable sequence pattern learning
- **Phase 9**: Note types are essential for difficulty modeling
- **Phase 13**: Classified notes provide content for chart assembly
- **All Downstream Phases**: Note classification is the foundation for all chart generation

### **Output Dependencies**
Subsequent phases depend on these Phase 7 outputs:
- `data\\processed\\phase7\\models\\note_classifier.pth` - Trained classification model
- `data\\processed\\phase7\\predictions\\*.json` - Note type predictions
- `data\\processed\\phase7\\models\\feature_scaler.pkl` - Feature normalization

---

## 7. **Validation Strategy**

### **Unit Tests**
```python
def test_feature_extraction():
    """Test feature extraction functionality."""
    # Create synthetic audio
    sr = 22050
    duration = 0.2  # 200ms
    audio = 0.5 * np.sin(2 * np.pi * 440 * np.linspace(0, duration, int(sr * duration)))
    
    features = extract_classification_features(audio, sr)
    
    assert len(features) == 50  # Expected feature count
    assert not np.any(np.isnan(features))
    assert not np.any(np.isinf(features))

def test_model_training():
    """Test model training with synthetic data."""
    # Create synthetic training data
    n_samples = 1000
    n_features = 50
    n_classes = 5
    
    X = np.random.randn(n_samples, n_features).astype(np.float32)
    y = np.random.randint(0, n_classes, n_samples)
    
    training_data = {"features": X, "labels": y}
    
    # Train model
    results = train_note_classifier(training_data, num_epochs=10)
    
    assert results["best_val_accuracy"] > 0.0
    assert results["model"] is not None
    assert results["scaler"] is not None
```

### **Quality Metrics**
- **Classification Accuracy**: >85% on validation set
- **Precision/Recall**: >80% for each note type (except rare types)
- **F1-Score**: >0.8 for common note types (don, ka, rest)
- **Inference Speed**: <10ms per candidate on RTX 3070

### **Cross-Validation**
```python
def evaluate_model_performance(model, test_data, label_encoder):
    """Comprehensive model evaluation."""
    
    # Generate predictions
    predictions = model.predict(test_data["features"])
    true_labels = test_data["labels"]
    
    # Classification report
    report = classification_report(
        true_labels, predictions, 
        target_names=label_encoder.classes_,
        output_dict=True
    )
    
    # Confusion matrix
    cm = confusion_matrix(true_labels, predictions)
    
    # Plot confusion matrix
    plt.figure(figsize=(8, 6))
    sns.heatmap(cm, annot=True, fmt='d', 
                xticklabels=label_encoder.classes_,
                yticklabels=label_encoder.classes_)
    plt.title('Note Classification Confusion Matrix')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.show()
    
    return report, cm
```

### **Example Success Case**
```python
# Expected classified_notes format (for Phase 8 compatibility)
classified_notes = [
    {
        "note_id": 0,
        "candidate_id": 0,
        "time_position": 1.234,
        "predicted_note_type": "don",
        "prediction_confidence": 0.92,
        "beat_alignment": 0.98,
        "measure_position": 0.25,
        "context_features": {
            "spectral_centroid": 2150.5,
            "onset_strength": 0.87,
            "harmonic_ratio": 0.65
        }
    },
    {
        "note_id": 1,
        "candidate_id": 1,
        "time_position": 1.702,
        "predicted_note_type": "rest",
        "prediction_confidence": 0.87,
        "beat_alignment": 0.12,
        "measure_position": 0.50,
        "context_features": {
            "spectral_centroid": 890.2,
            "onset_strength": 0.23,
            "harmonic_ratio": 0.31
        }
    }
]

# Expected note_predictions format (for Phase 8 compatibility)
note_predictions = [
    {
        "note_id": 0,
        "candidate_id": 0,
        "class_probabilities": {
            "rest": 0.02, "don": 0.92, "ka": 0.04, "big_don": 0.01, "big_ka": 0.01
        },
        "feature_importance": {
            "spectral_centroid": 0.35, "onset_strength": 0.42, "harmonic_ratio": 0.23
        },
        "prediction_time": 2.3,
        "model_version": "v1.0",
        "preprocessing_applied": ["normalization", "feature_scaling"]
    },
    {
        "note_id": 1,
        "candidate_id": 1,
        "class_probabilities": {
            "rest": 0.87, "don": 0.08, "ka": 0.03, "big_don": 0.01, "big_ka": 0.01
        },
        "feature_importance": {
            "spectral_centroid": 0.28, "onset_strength": 0.15, "harmonic_ratio": 0.57
        },
        "prediction_time": 2.1,
        "model_version": "v1.0",
        "preprocessing_applied": ["normalization", "feature_scaling"]
    }
]

# Expected file outputs:
# data\\processed\\phase7\\classified_notes\\Lemon.json - List of classified_notes Dicts
# data\\processed\\phase7\\predictions\\Lemon.json - List of note_predictions Dicts

# Expected training results
training_results = {
    "training_samples": 15420,
    "validation_accuracy": 0.89,
    "num_classes": 5,
    "feature_dimensions": 50,
    "class_distribution": {
        "rest": 0.65, "don": 0.20, "ka": 0.12, "big_don": 0.02, "big_ka": 0.01
    }
}
```

---

**Phase 7 Complete**. This phase establishes the foundation for note-level machine learning by training models to classify audio candidates into basic TJA note types.

**Next**: [Phase 8: Note Sequence Pattern Learning](phase_08_sequence_patterns.md)
