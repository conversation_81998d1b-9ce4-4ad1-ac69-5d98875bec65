# 🧩 Phase 13: Chart Assembly & Formatting

**Status**: 📋 Planned  
**Estimated Duration**: 3 days  
**Dependencies**: [Phase 9: Difficulty Patterns](phase_09_difficulty_patterns.md), [Phase 10: Measure Segmentation](phase_10_measure_segmentation.md), [Phase 11: Special Sections](phase_11_special_sections.md), [Phase 12: TJA Headers](phase_12_tja_headers.md)  
**Next Phase**: [Phase 14: Validation & Quality Control](phase_14_validation.md)

---

## 1. **Phase Purpose**

This phase assembles all generated components into complete, valid TJA chart files. This step is isolated because:

- **Chart assembly** requires combining outputs from multiple previous phases
- **TJA formatting** has strict syntax requirements that must be validated
- **Temporal synchronization** ensures all components align correctly
- **Quality control** validates the assembled chart before output

**Why Isolated**: Chart assembly is the critical integration point where all previous work comes together. It requires different expertise than individual component generation and must handle complex formatting and validation requirements.

---

## 2. **Inputs & Outputs**

### **Inputs**
```python
# From Phase 9: Difficulty Patterns (exact format match)
difficulty_patterns: Dict = {       # Generated note patterns with difficulty info
    "patterns": List[Dict],             # List of generated patterns
    "metadata": {
        "total_patterns": int,          # Total number of patterns generated
        "target_difficulty": int,       # Target difficulty level (8-10)
        "generation_timestamp": str,    # When patterns were generated
        "model_version": str,           # Model version used
        "quality_summary": {
            "average_quality_score": float,
            "patterns_above_threshold": int,
            "generation_success_rate": float
        }
    }
}

note_sequences: List[Dict] = [      # Classified and sequenced notes (from Phase 9)
    {
        "sequence_id": str,
        "notes": List[Dict],            # Sequential note data with timing
        "difficulty_level": int,        # Difficulty level for this sequence
        "total_duration": float,        # Sequence duration in seconds
        "note_count": int,              # Total notes in sequence
        "sequence_quality": float       # Overall sequence quality score
    }
]

# From Phase 10: Measure Segmentation (exact format match)
measure_segments: List[Dict] = [    # Segmented measures with timing
    {
        "measure_id": int,              # Sequential measure number
        "start_time": float,            # Measure start time (seconds)
        "end_time": float,              # Measure end time (seconds)
        "duration": float,              # Measure duration
        "time_signature": str,          # "4/4", "3/4", etc.
        "beat_count": int,              # Number of beats in measure
        "subdivision_count": int,       # Number of subdivisions (typically 16)
        "notes_in_measure": List[Dict], # Notes within this measure
        "bar_line_position": float,     # Bar line time position
        "measure_complexity": float,    # Complexity score for this measure
        "is_complete": bool            # Whether measure is fully filled
    }
]

bar_lines: List[Dict] = [           # Bar line positions (exact format match)
    {
        "bar_id": int,                  # Sequential bar line number
        "time_position": float,         # Time position (seconds)
        "measure_boundary": bool,       # True if major measure boundary
        "bar_type": str,               # "measure", "phrase", "section"
        "visual_emphasis": int         # Visual emphasis level (1-3)
    }
]

# From Phase 11: Special Sections (exact format match)
gogo_sections: List[Dict] = [       # Go-Go Time sections
    {
        "gogo_id": int,                 # Sequential Go-Go section ID
        "start_time": float,            # Section start time (seconds)
        "end_time": float,              # Section end time (seconds)
        "start_measure": int,           # Starting measure number
        "end_measure": int,             # Ending measure number
        "duration": float,              # Section duration
        "intensity_score": float,       # Musical intensity (0-1)
        "complexity_score": float,      # Pattern complexity (0-1)
        "placement_confidence": float,  # Confidence in placement (0-1)
        "gogo_type": str,              # "climax", "chorus", "bridge"
        "visual_effects": Dict          # Additional visual effects
    }
]

scroll_changes: List[Dict] = [      # Scroll speed changes
    {
        "change_id": int,               # Sequential change ID
        "time_position": float,         # Change time (seconds)
        "measure_position": int,        # Measure number
        "old_scroll_speed": float,      # Previous scroll speed
        "new_scroll_speed": float,      # New scroll speed
        "change_reason": str,           # "difficulty", "musical", "visual"
        "transition_type": str          # "instant", "gradual"
    }
]

# From Phase 12: TJA Headers
tja_headers: Dict                   # Complete TJA header information

# Input directory structure (matching exact outputs from previous phases)
data\\processed\\phase9\\
├── difficulty_patterns\\*.json     # Generated note patterns (Dict format)
├── note_sequences\\*.json          # Note sequences (List[Dict] format)
data\\processed\\phase10\\
├── measure_segments\\*.json        # Measure segments (List[Dict] format)
├── bar_lines\\*.json               # Bar line positions (List[Dict] format)
data\\processed\\phase11\\
├── gogo_sections\\*.json           # Go-Go Time sections (List[Dict] format)
├── scroll_changes\\*.json          # Scroll speed changes (List[Dict] format)
data\\processed\\phase12\\
├── tja_headers\\*.json             # TJA header information (Dict format)
```

### **Outputs**
```python
# Complete TJA chart
tja_chart: Dict = {
    "headers": Dict,                # TJA header section
    "course_data": Dict,            # Course-specific data
    "chart_content": str,           # Complete TJA chart content
    "metadata": {
        "total_notes": int,         # Total note count
        "chart_duration": float,    # Chart duration in seconds
        "difficulty_level": int,    # Target difficulty (8-10)
        "note_density": float,      # Notes per second
        "special_sections_count": int,
        "validation_status": str    # "valid", "warning", "error"
    }
}

# Assembled charts (for Phase 14 compatibility)
assembled_charts: List[str] = [     # Complete TJA file content strings
    """TITLE:Song Title
SUBTITLE:Artist Name
BPM:128.0
WAVE:song.ogg
OFFSET:-1.5
DEMOSTART:30.0

COURSE:Oni
LEVEL:9
BALLOON:
SCOREINIT:450
SCOREDIFF:110

#START
1010201020102010,
2020101010202020,
#GOGOSTART
1122112211221122,
#GOGOEND
1010201020102010,
#END
"""
]

# Chart metadata (for Phase 14 compatibility)
chart_metadata: List[Dict] = [
    {
        "chart_id": str,                # Unique chart identifier
        "source_audio": str,            # Original audio file path
        "total_notes": int,             # Total note count
        "chart_duration": float,        # Chart duration in seconds
        "difficulty_level": int,        # Target difficulty (8-10)
        "note_density": float,          # Notes per second
        "special_sections_count": int,  # Number of special sections
        "validation_status": str,       # "valid", "warning", "error"
        "assembly_timestamp": str,      # When chart was assembled
        "processing_time": float,       # Assembly processing time
        "quality_score": float          # Preliminary quality assessment
    }
]

# Output directory structure (Windows-compatible paths)
data\\processed\\phase13\\
├── assembled_charts\\              # Complete TJA files
│   ├── {filename}.tja             # Individual TJA chart files
│   └── ...
├── chart_metadata\\                # Chart assembly metadata
│   ├── {filename}.json            # Individual chart metadata
│   └── ...
├── validation_reports\\            # Assembly validation results
│   ├── {filename}.json            # Individual validation reports
│   └── ...
└── assembly_report.json            # Overall assembly statistics
```

---

## 3. **Implementation Plan**

### **Recommended Tools**
```python
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import re
from datetime import datetime
```

### **Core Chart Assembly Function**
```python
def assemble_tja_chart(
    tja_headers: Dict,
    note_sequences: List[Dict],
    measure_boundaries: List[Dict],
    special_sections: Dict,
    target_difficulty: int = 9
) -> Dict:
    """
    Assemble complete TJA chart from component parts.
    
    Args:
        tja_headers: Header information (title, BPM, etc.)
        note_sequences: Generated note sequences with timing
        measure_boundaries: Measure segmentation information
        special_sections: Go-Go Time and other special sections
        target_difficulty: Target difficulty level (8-10)
        
    Returns:
        Complete TJA chart dictionary
    """
    
    # 1. Validate inputs
    validation_result = validate_assembly_inputs(
        tja_headers, note_sequences, measure_boundaries, special_sections
    )
    
    if validation_result["status"] == "error":
        return create_error_chart(validation_result["message"])
    
    # 2. Sort and organize notes by time
    sorted_notes = sort_notes_by_time(note_sequences)
    
    # 3. Generate TJA header section
    header_content = generate_tja_headers(tja_headers, target_difficulty)
    
    # 4. Generate course data section
    course_content = generate_course_section(target_difficulty)
    
    # 5. Generate chart content with measures
    chart_content = generate_chart_content(
        sorted_notes, measure_boundaries, special_sections
    )
    
    # 6. Combine all sections
    complete_tja = combine_tja_sections(header_content, course_content, chart_content)
    
    # 7. Calculate metadata
    metadata = calculate_chart_metadata(sorted_notes, measure_boundaries, target_difficulty)
    
    # 8. Final validation
    final_validation = validate_complete_chart(complete_tja)
    metadata["validation_status"] = final_validation["status"]
    
    return {
        "headers": tja_headers,
        "course_data": {"difficulty": target_difficulty},
        "chart_content": complete_tja,
        "metadata": metadata
    }

def sort_notes_by_time(note_sequences: List[Dict]) -> List[Dict]:
    """Sort notes by their timing position."""
    all_notes = []
    
    for sequence in note_sequences:
        if "notes" in sequence:
            for note in sequence["notes"]:
                # Ensure each note has required timing information
                if "time" in note and "note_type" in note:
                    all_notes.append(note)
    
    # Sort by time
    return sorted(all_notes, key=lambda n: n["time"])

def generate_tja_headers(tja_headers: Dict, target_difficulty: int) -> str:
    """Generate TJA header section."""
    
    # Required headers
    title = tja_headers.get("title", "Generated Chart")
    subtitle = tja_headers.get("subtitle", "AI Generated")
    bpm = tja_headers.get("bpm", 120.0)
    wave = tja_headers.get("wave", "song.ogg")
    offset = tja_headers.get("offset", 0.0)
    demostart = tja_headers.get("demostart", 30.0)
    
    header_lines = [
        f"TITLE:{title}",
        f"SUBTITLE:{subtitle}",
        f"BPM:{bmp}",
        f"WAVE:{wave}",
        f"OFFSET:{offset}",
        f"DEMOSTART:{demostart}",
        ""  # Empty line separator
    ]
    
    return "\n".join(header_lines)

def generate_course_section(target_difficulty: int) -> str:
    """Generate course-specific section."""
    
    # Map difficulty to course type
    course_mapping = {
        8: ("Oni", 8),
        9: ("Oni", 9), 
        10: ("Edit", 10)
    }
    
    course, level = course_mapping.get(target_difficulty, ("Oni", 9))
    
    # Calculate score parameters based on difficulty
    score_params = calculate_score_parameters(target_difficulty)
    
    course_lines = [
        f"COURSE:{course}",
        f"LEVEL:{level}",
        f"BALLOON:",  # Empty for now - could be populated if balloon notes are generated
        f"SCOREINIT:{score_params['init']}",
        f"SCOREDIFF:{score_params['diff']}",
        ""  # Empty line separator
    ]
    
    return "\n".join(course_lines)

def calculate_score_parameters(difficulty: int) -> Dict:
    """Calculate scoring parameters based on difficulty."""
    # Standard scoring parameters for different difficulties
    score_mapping = {
        8: {"init": 400, "diff": 100},
        9: {"init": 450, "diff": 110}, 
        10: {"init": 500, "diff": 120}
    }
    
    return score_mapping.get(difficulty, {"init": 450, "diff": 110})

def generate_chart_content(
    sorted_notes: List[Dict],
    measure_boundaries: List[Dict],
    special_sections: Dict
) -> str:
    """Generate the main chart content with notes and commands."""
    
    content_lines = ["#START", ""]
    
    if not sorted_notes or not measure_boundaries:
        content_lines.extend(["1000,", "#END"])
        return "\n".join(content_lines)
    
    # Process each measure
    for i, measure in enumerate(measure_boundaries):
        measure_start = measure["start_time"]
        measure_end = measure["end_time"]
        
        # Check for special section commands at measure start
        special_commands = get_special_commands_at_time(special_sections, measure_start)
        content_lines.extend(special_commands)
        
        # Generate notes for this measure
        measure_notes = get_notes_in_measure(sorted_notes, measure_start, measure_end)
        measure_content = format_measure_notes(measure_notes, measure)
        
        content_lines.append(measure_content)
        
        # Check for special section end commands
        end_commands = get_special_end_commands_at_time(special_sections, measure_end)
        content_lines.extend(end_commands)
    
    content_lines.extend(["", "#END"])
    return "\n".join(content_lines)

def get_special_commands_at_time(special_sections: Dict, time: float) -> List[str]:
    """Get special commands that should be inserted at a specific time."""
    commands = []
    
    # Check for Go-Go Time start
    for gogo in special_sections.get("gogo_sections", []):
        if abs(gogo["start_time"] - time) < 0.1:  # Within 100ms
            commands.append("#GOGOSTART")
    
    # Check for BPM changes
    for bpm_change in special_sections.get("bpm_changes", []):
        if abs(bpm_change["time"] - time) < 0.1:
            commands.append(f"#BPMCHANGE {bmp_change['new_bpm']}")
    
    # Check for scroll changes
    for scroll_change in special_sections.get("scroll_changes", []):
        if abs(scroll_change["time"] - time) < 0.1:
            commands.append(f"#SCROLL {scroll_change['scroll_speed']}")
    
    return commands

def get_special_end_commands_at_time(special_sections: Dict, time: float) -> List[str]:
    """Get special end commands at a specific time."""
    commands = []
    
    # Check for Go-Go Time end
    for gogo in special_sections.get("gogo_sections", []):
        if abs(gogo["end_time"] - time) < 0.1:  # Within 100ms
            commands.append("#GOGOEND")
    
    return commands

def get_notes_in_measure(sorted_notes: List[Dict], start_time: float, end_time: float) -> List[Dict]:
    """Get all notes within a specific measure."""
    measure_notes = []
    
    for note in sorted_notes:
        note_time = note["time"]
        if start_time <= note_time < end_time:
            measure_notes.append(note)
    
    return measure_notes

def format_measure_notes(measure_notes: List[Dict], measure_info: Dict) -> str:
    """Format notes within a measure into TJA notation."""
    
    if not measure_notes:
        # Empty measure - use appropriate rest pattern
        return "0000000000000000,"  # 16 rests
    
    # Determine measure length and subdivision
    measure_duration = measure_info["end_time"] - measure_info["start_time"]
    time_signature = measure_info.get("time_signature", "4/4")
    
    # Calculate number of subdivisions (typically 16 for 4/4 time)
    subdivisions = calculate_subdivisions(time_signature)
    
    # Create note grid
    note_grid = ["0"] * subdivisions
    
    # Place notes in grid
    for note in measure_notes:
        # Calculate position within measure
        relative_time = note["time"] - measure_info["start_time"]
        position = int((relative_time / measure_duration) * subdivisions)
        position = min(position, subdivisions - 1)  # Clamp to valid range
        
        # Convert note type to TJA notation
        tja_note = convert_note_to_tja(note)
        note_grid[position] = tja_note
    
    return "".join(note_grid) + ","

def calculate_subdivisions(time_signature: str) -> int:
    """Calculate number of subdivisions for a time signature."""
    # Standard subdivision mapping
    subdivision_mapping = {
        "4/4": 16,  # 16th note subdivisions
        "3/4": 12,  # 12 subdivisions for 3/4
        "2/4": 8,   # 8 subdivisions for 2/4
        "6/8": 12   # 12 subdivisions for 6/8
    }
    
    return subdivision_mapping.get(time_signature, 16)

def convert_note_to_tja(note: Dict) -> str:
    """Convert internal note representation to TJA notation."""
    
    note_type = note.get("note_type", "rest")
    
    # TJA note mapping
    tja_mapping = {
        "rest": "0",
        "don": "1",      # Red note (don)
        "ka": "2",       # Blue note (ka)  
        "big_don": "3",  # Big red note
        "big_ka": "4",   # Big blue note
        "drumroll": "5", # Drumroll start
        "big_drumroll": "6", # Big drumroll start
        "balloon": "7",  # Balloon note
        "drumroll_end": "8"  # Drumroll end
    }
    
    return tja_mapping.get(note_type, "0")

def combine_tja_sections(header_content: str, course_content: str, chart_content: str) -> str:
    """Combine all TJA sections into complete file content."""
    
    sections = [
        header_content,
        course_content, 
        chart_content
    ]
    
    return "\n".join(sections)

def calculate_chart_metadata(sorted_notes: List[Dict], measure_boundaries: List[Dict], difficulty: int) -> Dict:
    """Calculate metadata for the assembled chart."""
    
    total_notes = len([n for n in sorted_notes if n.get("note_type", "rest") != "rest"])
    
    if measure_boundaries:
        chart_duration = measure_boundaries[-1]["end_time"] - measure_boundaries[0]["start_time"]
    else:
        chart_duration = 0.0
    
    note_density = total_notes / chart_duration if chart_duration > 0 else 0.0
    
    return {
        "total_notes": total_notes,
        "chart_duration": float(chart_duration),
        "difficulty_level": difficulty,
        "note_density": float(note_density),
        "special_sections_count": 0,  # Could be calculated from special_sections
        "generation_timestamp": datetime.now().isoformat()
    }

def validate_complete_chart(tja_content: str) -> Dict:
    """Validate the complete TJA chart for syntax and structure."""
    
    validation_issues = []
    
    # Check for required sections
    if "#START" not in tja_content:
        validation_issues.append("Missing #START command")
    
    if "#END" not in tja_content:
        validation_issues.append("Missing #END command")
    
    # Check for required headers
    required_headers = ["TITLE:", "BPM:", "WAVE:"]
    for header in required_headers:
        if header not in tja_content:
            validation_issues.append(f"Missing required header: {header}")
    
    # Check for valid note characters
    valid_notes = set("0123456789,")
    chart_section = extract_chart_section(tja_content)
    
    for char in chart_section:
        if char not in valid_notes and not char.isspace() and char != '#':
            validation_issues.append(f"Invalid character in chart: '{char}'")
    
    # Determine validation status
    if not validation_issues:
        status = "valid"
    elif len(validation_issues) <= 2:
        status = "warning"
    else:
        status = "error"
    
    return {
        "status": status,
        "issues": validation_issues,
        "issue_count": len(validation_issues)
    }

def extract_chart_section(tja_content: str) -> str:
    """Extract just the chart section (between #START and #END)."""
    
    start_idx = tja_content.find("#START")
    end_idx = tja_content.find("#END")
    
    if start_idx == -1 or end_idx == -1:
        return ""
    
    return tja_content[start_idx:end_idx]
```

---

## 4. **Best Practices**

### **Modular Assembly**
- Separate header, course, and chart generation
- Validate each section independently before combining
- Use consistent formatting and spacing
- Handle edge cases gracefully (empty measures, missing data)

### **TJA Compliance**
- Follow official TJA format specifications exactly
- Validate syntax using reference parser
- Handle special characters and encoding properly
- Ensure proper line endings and file structure

### **Quality Assurance**
```python
# Comprehensive validation pipeline
def comprehensive_validation(tja_content: str) -> Dict:
    """Run multiple validation checks on TJA content."""
    results = {
        "syntax_validation": validate_tja_syntax(tja_content),
        "structure_validation": validate_tja_structure(tja_content),
        "timing_validation": validate_timing_consistency(tja_content),
        "difficulty_validation": validate_difficulty_appropriateness(tja_content)
    }
    return results
```

### **Error Recovery**
- Implement fallback strategies for missing components
- Generate minimal valid charts when inputs are incomplete
- Log all assembly decisions for debugging
- Provide detailed error messages for troubleshooting

---

## 5. **Challenges & Pitfalls**

### **Timing Synchronization**
- **Issue**: Components from different phases may have slight timing misalignments
- **Symptoms**: Notes not aligned with measures, special sections at wrong times
- **Mitigation**: Use consistent timing reference across all phases
- **Solution**: Implement timing reconciliation and snapping to grid

### **TJA Syntax Compliance**
- **Issue**: Generated content may not conform to strict TJA syntax rules
- **Example**: Invalid characters, missing commas, incorrect command format
- **Mitigation**: Use reference parser for validation
- **Solution**: Implement comprehensive syntax checking and correction

### **Measure Boundary Handling**
- **Issue**: Notes may fall exactly on measure boundaries causing ambiguity
- **Symptoms**: Notes duplicated or missing at measure transitions
- **Mitigation**: Use consistent boundary handling rules
- **Solution**: Implement precise boundary detection and note assignment

### **Special Section Conflicts**
- **Issue**: Multiple special sections may overlap or conflict
- **Example**: Go-Go Time and BPM change at same time
- **Mitigation**: Define precedence rules for conflicting commands
- **Solution**: Implement conflict resolution and command ordering

---

## 6. **Dependencies & Unlocks**

### **Prerequisites**
- **Phases 9-12 Complete**: All component generation phases
- **Required Files**:
  - `data\\processed\\phase9\\difficulty_patterns\\*.json`
  - `data\\processed\\phase10\\measure_segments\\*.json`
  - `data\\processed\\phase11\\special_sections\\*.json`
  - `data\\processed\\phase12\\tja_headers\\*.json`
- **Reference**: TJA format specification and parser

### **What This Phase Unlocks**
- **Phase 14**: Complete charts enable comprehensive validation
- **Phase 15**: Assembled charts ready for deployment pipeline
- **End-to-End Testing**: Complete charts allow full system validation
- **User Testing**: Playable charts enable user feedback and iteration

### **Output Dependencies**
Final phases depend on these Phase 13 outputs:
- `data\\processed\\phase13\\assembled_charts\\*.tja` - Complete playable TJA files
- `data\\processed\\phase13\\chart_metadata\\*.json` - Chart statistics and metadata
- `data\\processed\\phase13\\validation_reports\\*.json` - Assembly validation results

---

## 7. **Validation Strategy**

### **Unit Tests**
```python
def test_chart_assembly():
    """Test basic chart assembly functionality."""
    # Create minimal test inputs
    headers = {"title": "Test Song", "bpm": 120, "wave": "test.ogg", "offset": 0}
    notes = [
        {"time": 0.5, "note_type": "don"},
        {"time": 1.0, "note_type": "ka"},
        {"time": 1.5, "note_type": "don"}
    ]
    measures = [
        {"start_time": 0.0, "end_time": 2.0, "time_signature": "4/4"}
    ]
    special_sections = {"gogo_sections": [], "bpm_changes": [], "scroll_changes": []}
    
    result = assemble_tja_chart(headers, [{"notes": notes}], measures, special_sections)
    
    assert result["metadata"]["total_notes"] == 3
    assert "#START" in result["chart_content"]
    assert "#END" in result["chart_content"]
    assert result["metadata"]["validation_status"] in ["valid", "warning"]

def test_tja_syntax_validation():
    """Test TJA syntax validation."""
    valid_tja = """TITLE:Test
BPM:120
WAVE:test.ogg
COURSE:Oni
LEVEL:9
#START
1020102010201020,
#END"""
    
    validation = validate_complete_chart(valid_tja)
    assert validation["status"] == "valid"
    assert len(validation["issues"]) == 0
```

### **Quality Metrics**
- **Assembly Success Rate**: >95% of inputs produce valid TJA files
- **Syntax Compliance**: 100% of generated TJA files pass parser validation
- **Timing Accuracy**: Note positions within ±50ms of intended timing
- **Playability**: Generated charts load and play correctly in TJA simulators

### **Integration Testing**
```python
def test_end_to_end_assembly():
    """Test complete assembly pipeline with real data."""
    # Load actual phase outputs
    song_name = "test_song"
    
    # Load all required inputs
    headers = load_phase_output(12, song_name, "tja_headers")
    patterns = load_phase_output(9, song_name, "difficulty_patterns") 
    measures = load_phase_output(10, song_name, "measure_boundaries")
    special = load_phase_output(11, song_name, "special_sections")
    
    # Assemble chart
    result = assemble_tja_chart(headers, patterns, measures, special)
    
    # Validate result
    assert result["metadata"]["validation_status"] != "error"
    
    # Test with reference parser
    parser_result = validate_with_reference_parser(result["chart_content"])
    assert parser_result["valid"] == True
```

### **Example Success Case**
```python
# Expected assembled_charts format (for Phase 14 compatibility)
assembled_charts = [
    """TITLE:Test Song
SUBTITLE:AI Generated
BPM:128.0
WAVE:test_song.ogg
OFFSET:-1.5
DEMOSTART:30.0

COURSE:Oni
LEVEL:9
BALLOON:
SCOREINIT:450
SCOREDIFF:110

#START
1020102010201020,
2010201020102010,
#GOGOSTART
1122112211221122,
2211221122112211,
#GOGOEND
1020102010201020,
#END"""
]

# Expected chart_metadata format (for Phase 14 compatibility)
chart_metadata = [
    {
        "chart_id": "test_song",
        "source_audio": "data\\raw\\test_song.wav",
        "total_notes": 45,
        "chart_duration": 120.5,
        "difficulty_level": 9,
        "note_density": 0.37,
        "special_sections_count": 1,
        "validation_status": "valid",
        "assembly_timestamp": "2024-01-15T10:30:00Z",
        "processing_time": 2.3,
        "quality_score": 0.87
    }
]

# Expected file outputs:
# data\\processed\\phase13\\assembled_charts\\test_song.tja - Complete TJA file
# data\\processed\\phase13\\chart_metadata\\test_song.json - Chart metadata Dict
```

---

**Phase 13 Complete**. This phase combines all generated components into complete, playable TJA chart files with proper formatting and validation.

**Next**: [Phase 14: Validation & Quality Control](phase_14_validation.md)
