"""
Memory test script for Phase 2: Audio Quality Assessment & Filtering
Tests memory usage patterns with a subset to ensure no memory leaks.
"""

import os
import sys
import time
import psutil
from pathlib import Path

def test_memory_usage():
    """Test Phase 2 memory usage with a subset of files."""
    print("🧪 Phase 2 Memory Management Test")
    print("=" * 50)
    
    # Add src to Python path
    src_path = Path(__file__).parent.parent / "src"
    sys.path.insert(0, str(src_path))
    
    try:
        from phases.phase_02_quality_assessment import Phase2QualityProcessor
        
        # Initialize processor
        processor = Phase2QualityProcessor()
        
        # Get memory baseline
        process = psutil.Process()
        baseline_memory = process.memory_info().rss / (1024 * 1024)  # MB
        print(f"Baseline memory usage: {baseline_memory:.1f} MB")
        
        # Test with increasing batch sizes
        test_sizes = [10, 25, 50, 100]
        
        for test_size in test_sizes:
            print(f"\n🔍 Testing with {test_size} files...")
            
            # Get file pairs for testing
            file_pairs = processor.get_input_files_list(validation_mode=False)[:test_size]
            
            if not file_pairs:
                print("❌ No files found for testing")
                continue
            
            # Record memory before processing
            memory_before = process.memory_info().rss / (1024 * 1024)
            print(f"Memory before processing: {memory_before:.1f} MB")
            
            # Process files
            start_time = time.time()
            successful = 0
            
            for i, (audio_path, metadata_path) in enumerate(file_pairs, 1):
                try:
                    success, metadata = processor.process_single_file(audio_path, metadata_path)
                    if success:
                        successful += 1
                    
                    # Check memory every 10 files
                    if i % 10 == 0:
                        current_memory = process.memory_info().rss / (1024 * 1024)
                        print(f"  [{i}/{test_size}] Memory: {current_memory:.1f} MB")
                        
                        # Force garbage collection if memory is high
                        if current_memory > baseline_memory + 1000:  # 1GB increase
                            processor.memory_manager.force_garbage_collection()
                            gc_memory = process.memory_info().rss / (1024 * 1024)
                            print(f"  After GC: {gc_memory:.1f} MB")
                    
                except Exception as e:
                    print(f"  Error processing {audio_path.name}: {e}")
            
            # Record memory after processing
            memory_after = process.memory_info().rss / (1024 * 1024)
            processing_time = time.time() - start_time
            
            # Force final garbage collection
            processor.memory_manager.force_garbage_collection()
            memory_final = process.memory_info().rss / (1024 * 1024)
            
            # Results
            print(f"Results for {test_size} files:")
            print(f"  Successful: {successful}/{test_size} ({successful/test_size:.1%})")
            print(f"  Processing time: {processing_time:.1f}s ({processing_time/test_size:.2f}s per file)")
            print(f"  Memory before: {memory_before:.1f} MB")
            print(f"  Memory after: {memory_after:.1f} MB")
            print(f"  Memory final (after GC): {memory_final:.1f} MB")
            print(f"  Memory increase: {memory_final - baseline_memory:.1f} MB")
            
            # Check for memory leaks
            memory_leak = memory_final - baseline_memory
            if memory_leak > 500:  # More than 500MB increase
                print(f"  ⚠️  Potential memory leak detected: {memory_leak:.1f} MB")
            else:
                print(f"  ✅ Memory usage acceptable: {memory_leak:.1f} MB increase")
            
            # Brief pause between tests
            time.sleep(2)
        
        # Final memory check
        final_memory = process.memory_info().rss / (1024 * 1024)
        total_increase = final_memory - baseline_memory
        
        print(f"\n📊 Final Memory Assessment:")
        print(f"  Baseline: {baseline_memory:.1f} MB")
        print(f"  Final: {final_memory:.1f} MB")
        print(f"  Total increase: {total_increase:.1f} MB")
        
        # Memory management assessment
        max_memory_gb = processor.config['memory']['max_ram_usage_gb']
        max_memory_mb = max_memory_gb * 1024
        
        if final_memory < max_memory_mb:
            print(f"  ✅ Within memory limits: {final_memory:.1f} MB < {max_memory_mb:.1f} MB")
            return True
        else:
            print(f"  ❌ Exceeds memory limits: {final_memory:.1f} MB > {max_memory_mb:.1f} MB")
            return False
            
    except Exception as e:
        print(f"❌ Memory test failed: {e}")
        return False

def estimate_full_dataset_memory():
    """Estimate memory usage for full dataset."""
    print("\n🔮 Full Dataset Memory Estimation")
    print("=" * 40)
    
    # Estimate based on test results
    files_per_mb = 10  # Conservative estimate
    total_files = 2878
    estimated_memory_mb = total_files / files_per_mb
    
    print(f"Total files: {total_files}")
    print(f"Estimated memory per file: {1/files_per_mb:.2f} MB")
    print(f"Estimated total memory: {estimated_memory_mb:.1f} MB ({estimated_memory_mb/1024:.1f} GB)")
    
    # Check against limits
    max_memory_gb = 6.5  # From config
    max_memory_mb = max_memory_gb * 1024
    
    if estimated_memory_mb < max_memory_mb:
        print(f"✅ Estimated usage within limits: {estimated_memory_mb:.1f} MB < {max_memory_mb:.1f} MB")
        return True
    else:
        print(f"⚠️  Estimated usage may exceed limits: {estimated_memory_mb:.1f} MB > {max_memory_mb:.1f} MB")
        print("Consider reducing batch size or increasing garbage collection frequency")
        return False

def main():
    """Main test execution."""
    print("🎼 TJA Chart Generation - Phase 2 Memory Test")
    print("=" * 60)
    
    # Run memory usage test
    memory_test_passed = test_memory_usage()
    
    # Estimate full dataset requirements
    estimation_ok = estimate_full_dataset_memory()
    
    # Final assessment
    print(f"\n🎯 Memory Management Assessment:")
    if memory_test_passed and estimation_ok:
        print("✅ Phase 2 is ready for full dataset processing")
        print("✅ Memory management is properly configured")
        return True
    else:
        print("❌ Memory management needs optimization")
        print("Consider reducing batch sizes or increasing GC frequency")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
