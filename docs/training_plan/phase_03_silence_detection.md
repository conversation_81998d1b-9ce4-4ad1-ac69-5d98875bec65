# 🧩 Phase 3: Silence Detection & Audio Segmentation

**Status**: 📋 Planned  
**Estimated Duration**: 2 days  
**Dependencies**: [Phase 1: Audio Loading](phase_01_audio_loading.md), [Phase 2: Quality Assessment](phase_02_quality_assessment.md)  
**Next Phase**: [Phase 4: Beat Position Estimation](phase_04_beat_estimation.md)

---

## 1. **Phase Purpose**

This phase identifies silence regions and segments audio into meaningful musical sections. This step is isolated because:

- **Silence regions** provide natural boundaries for rhythm analysis
- **Audio segmentation** enables focused processing on musical content
- **Temporal landmarks** are needed for accurate beat alignment
- **Noise reduction** by excluding non-musical segments improves downstream accuracy

**Why Isolated**: Silence detection requires different algorithms than quality assessment, and the segmentation results are used by multiple downstream phases for temporal alignment and feature extraction.

---

## 2. **Inputs & Outputs**

### **Inputs**
```python
# From Phase 2 (exact format match)
audio_data: np.ndarray          # Shape: (n_samples,) - high-quality audio
sample_rate: int = 22050        # Standardized sample rate

# Quality metrics (matching Phase 2 output format)
quality_metrics: dict = {       # Phase 2 quality assessment results
    "snr_db": float,              # Signal-to-noise ratio
    "dynamic_range_db": float,    # Dynamic range
    "clipping_percentage": float, # Percentage of clipped samples
    "silence_percentage": float,  # Percentage of silent samples
    "spectral_centroid_mean": float,  # Average spectral centroid
    "spectral_rolloff_mean": float,   # Average spectral rolloff
    "zero_crossing_rate": float,  # Average zero crossing rate
    "quality_score": float,       # Overall quality score (0-1)
    "quality_pass": bool,         # Passes quality threshold
    "quality_issues": list        # List of detected issues
}

# Input directory structure
data\\processed\\phase2\\
├── filtered_audio\\*.npy       # High-quality audio files
├── filtered_metadata\\*.json   # Enhanced metadata
└── quality_report.json         # Quality statistics
```

### **Outputs**
```python
# Silence detection results
silence_map: dict = {
    "silence_regions": [           # List of silence intervals
        {"start": float, "end": float, "duration": float, "confidence": float}
    ],
    "music_regions": [             # List of musical intervals
        {"start": float, "end": float, "duration": float, "energy": float}
    ],
    "silence_percentage": float,   # Total silence percentage
    "music_percentage": float,     # Total music percentage
    "segment_count": int,          # Number of musical segments
    "detection_params": dict       # Parameters used for detection
}

# Audio segments
audio_segments: List[dict] = [
    {
        "segment_id": int,
        "start_time": float,
        "end_time": float,
        "duration": float,
        "audio_data": np.ndarray,  # Segmented audio
        "energy_profile": np.ndarray,  # RMS energy over time
        "is_musical": bool
    }
]

# Output directory structure
data\\processed\\phase3\\
├── silence_maps\\*.json        # Silence detection results
├── audio_segments\\            # Segmented audio files
│   ├── [song]_segment_[N].npy  # Individual segments
│   └── [song]_segments.json    # Segment metadata
├── energy_profiles\\*.npy      # RMS energy profiles
└── segmentation_report.json    # Overall segmentation statistics
```

---

## 3. **Implementation Plan**

### **Recommended Tools**
```python
import librosa
import numpy as np
import scipy.signal
from pathlib import Path
import json
import logging
from typing import Dict, List, Tuple
from tqdm import tqdm
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
```

### **Core Silence Detection Function**
```python
def detect_silence_regions(
    audio: np.ndarray, 
    sr: int = 22050,
    frame_length: int = 2048,
    hop_length: int = 512,
    silence_threshold: float = -40,  # dB
    min_silence_duration: float = 0.5,  # seconds
    min_music_duration: float = 1.0     # seconds
) -> Dict:
    """
    Detect silence and music regions using multi-band energy analysis.
    
    Args:
        audio: Input audio signal
        sr: Sample rate
        frame_length: Frame size for analysis
        hop_length: Hop size for analysis
        silence_threshold: Silence threshold in dB
        min_silence_duration: Minimum silence duration to consider
        min_music_duration: Minimum music duration to consider
        
    Returns:
        Dictionary with silence and music regions
    """
    
    # 1. Calculate RMS energy
    rms_energy = librosa.feature.rms(
        y=audio, 
        frame_length=frame_length, 
        hop_length=hop_length
    )[0]
    
    # Convert to dB
    rms_db = librosa.amplitude_to_db(rms_energy, ref=np.max)
    
    # 2. Multi-band analysis for better detection
    # Split into frequency bands
    stft = librosa.stft(audio, hop_length=hop_length, n_fft=frame_length)
    magnitude = np.abs(stft)
    
    # Low, mid, high frequency bands
    n_bins = magnitude.shape[0]
    low_band = magnitude[:n_bins//3].mean(axis=0)
    mid_band = magnitude[n_bins//3:2*n_bins//3].mean(axis=0)
    high_band = magnitude[2*n_bins//3:].mean(axis=0)
    
    # Convert to dB
    low_db = librosa.amplitude_to_db(low_band, ref=np.max)
    mid_db = librosa.amplitude_to_db(mid_band, ref=np.max)
    high_db = librosa.amplitude_to_db(high_band, ref=np.max)
    
    # 3. Adaptive threshold based on signal statistics
    # Use percentile-based thresholding
    adaptive_threshold = np.percentile(rms_db, 20)  # 20th percentile
    final_threshold = max(silence_threshold, adaptive_threshold - 10)
    
    # 4. Combine multi-band information
    # A frame is silent if ALL bands are below threshold
    silence_mask = (
        (rms_db < final_threshold) &
        (low_db < final_threshold + 5) &
        (mid_db < final_threshold + 5) &
        (high_db < final_threshold + 5)
    )
    
    # 5. Apply morphological operations to clean up detection
    # Remove short silence/music regions
    from scipy.ndimage import binary_opening, binary_closing
    
    # Smooth the mask
    kernel_size = max(1, int(min_silence_duration * sr / hop_length))
    silence_mask = binary_opening(silence_mask, structure=np.ones(kernel_size))
    silence_mask = binary_closing(silence_mask, structure=np.ones(kernel_size))
    
    # 6. Convert frame indices to time regions
    frame_times = librosa.frames_to_time(
        np.arange(len(silence_mask)), 
        sr=sr, 
        hop_length=hop_length
    )
    
    silence_regions = []
    music_regions = []
    
    # Find contiguous regions
    silence_starts = np.where(np.diff(np.concatenate(([False], silence_mask))))[0]
    silence_ends = np.where(np.diff(np.concatenate((silence_mask, [False]))))[0]
    
    for start_idx, end_idx in zip(silence_starts, silence_ends):
        start_time = frame_times[start_idx]
        end_time = frame_times[min(end_idx, len(frame_times)-1)]
        duration = end_time - start_time
        
        if duration >= min_silence_duration:
            # Calculate confidence based on how far below threshold
            region_energy = rms_db[start_idx:end_idx+1]
            confidence = max(0, (final_threshold - np.mean(region_energy)) / 10)
            
            silence_regions.append({
                "start": float(start_time),
                "end": float(end_time),
                "duration": float(duration),
                "confidence": float(min(1.0, confidence))
            })
    
    # Find music regions (inverse of silence)
    music_mask = ~silence_mask
    music_starts = np.where(np.diff(np.concatenate(([False], music_mask))))[0]
    music_ends = np.where(np.diff(np.concatenate((music_mask, [False]))))[0]
    
    for start_idx, end_idx in zip(music_starts, music_ends):
        start_time = frame_times[start_idx]
        end_time = frame_times[min(end_idx, len(frame_times)-1)]
        duration = end_time - start_time
        
        if duration >= min_music_duration:
            # Calculate average energy for this region
            region_energy = rms_energy[start_idx:end_idx+1]
            avg_energy = float(np.mean(region_energy))
            
            music_regions.append({
                "start": float(start_time),
                "end": float(end_time),
                "duration": float(duration),
                "energy": avg_energy
            })
    
    # 7. Calculate statistics
    total_duration = len(audio) / sr
    silence_duration = sum(r["duration"] for r in silence_regions)
    music_duration = sum(r["duration"] for r in music_regions)
    
    return {
        "silence_regions": silence_regions,
        "music_regions": music_regions,
        "silence_percentage": float(silence_duration / total_duration * 100),
        "music_percentage": float(music_duration / total_duration * 100),
        "segment_count": len(music_regions),
        "detection_params": {
            "silence_threshold": final_threshold,
            "min_silence_duration": min_silence_duration,
            "min_music_duration": min_music_duration,
            "frame_length": frame_length,
            "hop_length": hop_length
        }
    }
```

### **Audio Segmentation Function**
```python
def segment_audio(
    audio: np.ndarray,
    sr: int,
    silence_map: Dict,
    padding: float = 0.1  # seconds of padding around segments
) -> List[Dict]:
    """
    Segment audio based on silence detection results.
    
    Args:
        audio: Input audio signal
        sr: Sample rate
        silence_map: Results from detect_silence_regions
        padding: Padding around segments in seconds
        
    Returns:
        List of audio segments with metadata
    """
    segments = []
    
    for i, music_region in enumerate(silence_map["music_regions"]):
        # Add padding
        start_time = max(0, music_region["start"] - padding)
        end_time = min(len(audio) / sr, music_region["end"] + padding)
        
        # Convert to sample indices
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        
        # Extract segment
        segment_audio = audio[start_sample:end_sample]
        
        # Calculate energy profile for segment
        segment_rms = librosa.feature.rms(
            y=segment_audio, 
            frame_length=2048, 
            hop_length=512
        )[0]
        
        segment_info = {
            "segment_id": i,
            "start_time": float(start_time),
            "end_time": float(end_time),
            "duration": float(end_time - start_time),
            "audio_data": segment_audio,
            "energy_profile": segment_rms,
            "is_musical": True,
            "original_start": music_region["start"],
            "original_end": music_region["end"],
            "padding_applied": padding
        }
        
        segments.append(segment_info)
    
    return segments
```

### **Batch Processing Pipeline**
```python
def process_silence_detection(
    input_dir: Path = Path("data\\processed\\phase2"),
    output_dir: Path = Path("data\\processed\\phase3"),
    silence_threshold: float = -40,
    min_silence_duration: float = 0.5,
    min_music_duration: float = 1.0
) -> Dict:
    """Process silence detection for entire dataset."""
    
    # Setup output directories
    (output_dir / "silence_maps").mkdir(parents=True, exist_ok=True)
    (output_dir / "audio_segments").mkdir(parents=True, exist_ok=True)
    (output_dir / "energy_profiles").mkdir(parents=True, exist_ok=True)
    
    # Load filtered audio files
    audio_files = list((input_dir / "filtered_audio").glob("*.npy"))
    
    results = {
        "total_files": len(audio_files),
        "processed_files": 0,
        "total_segments": 0,
        "avg_silence_percentage": 0.0,
        "avg_segments_per_file": 0.0,
        "processing_errors": []
    }
    
    all_silence_percentages = []
    all_segment_counts = []
    
    for audio_file in tqdm(audio_files, desc="Detecting silence regions"):
        try:
            # Load audio
            audio = np.load(audio_file)
            
            # Detect silence regions
            silence_map = detect_silence_regions(
                audio, 
                sr=22050,
                silence_threshold=silence_threshold,
                min_silence_duration=min_silence_duration,
                min_music_duration=min_music_duration
            )
            
            # Save silence map
            silence_map_file = output_dir / "silence_maps" / f"{audio_file.stem}.json"
            with open(silence_map_file, 'w') as f:
                json.dump(silence_map, f, indent=2)
            
            # Segment audio
            segments = segment_audio(audio, 22050, silence_map)
            
            # Save segments
            segment_metadata = []
            for segment in segments:
                # Save segment audio
                segment_file = output_dir / "audio_segments" / f"{audio_file.stem}_segment_{segment['segment_id']}.npy"
                np.save(segment_file, segment["audio_data"])
                
                # Save energy profile
                energy_file = output_dir / "energy_profiles" / f"{audio_file.stem}_segment_{segment['segment_id']}_energy.npy"
                np.save(energy_file, segment["energy_profile"])
                
                # Prepare metadata (without audio_data for JSON serialization)
                segment_meta = {k: v for k, v in segment.items() if k != "audio_data" and k != "energy_profile"}
                segment_metadata.append(segment_meta)
            
            # Save segment metadata
            segments_file = output_dir / "audio_segments" / f"{audio_file.stem}_segments.json"
            with open(segments_file, 'w') as f:
                json.dump(segment_metadata, f, indent=2)
            
            # Update statistics
            results["processed_files"] += 1
            results["total_segments"] += len(segments)
            all_silence_percentages.append(silence_map["silence_percentage"])
            all_segment_counts.append(len(segments))
            
        except Exception as e:
            error_info = {"file": str(audio_file), "error": str(e)}
            results["processing_errors"].append(error_info)
            logging.error(f"Error processing {audio_file}: {e}")
    
    # Calculate final statistics
    if all_silence_percentages:
        results["avg_silence_percentage"] = float(np.mean(all_silence_percentages))
        results["avg_segments_per_file"] = float(np.mean(all_segment_counts))
    
    # Save results
    with open(output_dir / "segmentation_report.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    return results
```

---

## 4. **Best Practices**

### **Adaptive Thresholding**
- Use percentile-based thresholds rather than fixed values
- Adjust thresholds based on overall signal statistics
- Consider genre-specific threshold adjustments
- Validate thresholds on representative samples

### **Multi-band Analysis**
- Analyze different frequency bands separately
- Combine band information for robust detection
- Weight bands based on musical content (emphasize mid-range)
- Use spectral features to distinguish silence from quiet music

### **Temporal Smoothing**
```python
# Apply morphological operations to clean detection
from scipy.ndimage import binary_opening, binary_closing

# Remove short spurious detections
kernel_size = int(min_duration * sr / hop_length)
cleaned_mask = binary_opening(mask, structure=np.ones(kernel_size))
cleaned_mask = binary_closing(cleaned_mask, structure=np.ones(kernel_size))
```

### **Memory-Efficient Processing**
- Process audio in overlapping windows for long files
- Use streaming analysis for very large datasets
- Cache intermediate results (STFT, energy profiles)
- Clean up temporary arrays after processing

---

## 5. **Challenges & Pitfalls**

### **Quiet Musical Passages**
- **Issue**: Soft instrumental sections misclassified as silence
- **Example**: Piano ballads, ambient music, fade-ins/fade-outs
- **Mitigation**: Use spectral complexity in addition to energy
- **Solution**: Multi-feature detection (spectral centroid, zero-crossing rate)

### **Background Noise vs Silence**
- **Issue**: Low-level noise prevents true silence detection
- **Symptoms**: No silence regions detected in noisy recordings
- **Mitigation**: Adaptive noise floor estimation
- **Solution**: Use spectral subtraction or noise profiling

### **Genre-Specific Characteristics**
- **Issue**: Different genres have different silence patterns
- **Example**: Classical music has more dynamic range, electronic music has fewer pauses
- **Mitigation**: Genre-aware parameter tuning
- **Solution**: Train genre-specific detection models

### **Boundary Accuracy**
- **Issue**: Imprecise silence/music boundaries affect downstream timing
- **Impact**: Beat detection and note alignment errors
- **Mitigation**: Use higher temporal resolution for boundary detection
- **Solution**: Refine boundaries using onset detection

### **Short Segments**
- **Issue**: Very short musical segments may not contain useful information
- **Threshold**: Segments <1 second often lack rhythmic content
- **Mitigation**: Merge adjacent short segments
- **Solution**: Implement segment merging logic with gap tolerance

---

## 6. **Dependencies & Unlocks**

### **Prerequisites**
- **Phase 1 & 2 Complete**: High-quality, standardized audio files
- **Required Files**:
  - `data\\processed\\phase2\\filtered_audio\\*.npy`
  - `data\\processed\\phase2\\filtered_metadata\\*.json`
- **Libraries**: `librosa`, `scipy`, `sklearn` for signal processing

### **What This Phase Unlocks**
- **Phase 4**: Clean audio segments enable accurate beat detection
- **Phase 5**: Silence boundaries provide tempo alignment reference points
- **Phase 6**: Segmented audio focuses note detection on musical content
- **All Training Phases**: Temporal segmentation improves training efficiency

### **Output Dependencies**
Subsequent phases depend on these Phase 3 outputs:
- `data\\processed\\phase3\\audio_segments\\*_segment_*.npy` - Clean musical segments
- `data\\processed\\phase3\\silence_maps\\*.json` - Temporal boundary information
- `data\\processed\\phase3\\energy_profiles\\*.npy` - Energy-based features

---

## 7. **Validation Strategy**

### **Unit Tests**
```python
def test_silence_detection():
    """Test silence detection on synthetic audio."""
    sr = 22050
    # Create audio with known silence pattern
    music = 0.5 * np.sin(2 * np.pi * 440 * np.linspace(0, 2, 2*sr))  # 2s music
    silence = np.zeros(int(1*sr))  # 1s silence
    test_audio = np.concatenate([music, silence, music])
    
    silence_map = detect_silence_regions(test_audio, sr)
    
    # Should detect one silence region around 2-3 seconds
    assert len(silence_map["silence_regions"]) >= 1
    silence_region = silence_map["silence_regions"][0]
    assert 1.5 <= silence_region["start"] <= 2.5
    assert 2.5 <= silence_region["end"] <= 3.5

def test_segmentation():
    """Test audio segmentation functionality."""
    # Create test audio and silence map
    test_audio = np.random.randn(5 * 22050)  # 5 seconds
    silence_map = {
        "music_regions": [
            {"start": 0.5, "end": 2.0},
            {"start": 3.0, "end": 4.5}
        ]
    }
    
    segments = segment_audio(test_audio, 22050, silence_map)
    assert len(segments) == 2
    assert segments[0]["duration"] > 1.0  # Should include padding
```

### **Quality Metrics**
- **Detection Accuracy**: >85% of silence regions correctly identified
- **Boundary Precision**: Silence boundaries within ±0.2 seconds of ground truth
- **Segment Count**: 2-8 segments per song (reasonable for typical music)
- **Processing Speed**: >50 files per minute on RTX 3070

### **Visual Validation**
```python
def visualize_silence_detection(audio: np.ndarray, sr: int, silence_map: Dict):
    """Visualize silence detection results."""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 8))
    
    # Plot waveform
    time = np.linspace(0, len(audio)/sr, len(audio))
    ax1.plot(time, audio, alpha=0.7, color='blue')
    ax1.set_title('Audio Waveform with Detected Regions')
    ax1.set_xlabel('Time (seconds)')
    ax1.set_ylabel('Amplitude')
    
    # Highlight silence regions
    for region in silence_map["silence_regions"]:
        ax1.axvspan(region["start"], region["end"], alpha=0.3, color='red', label='Silence')
    
    # Highlight music regions
    for region in silence_map["music_regions"]:
        ax1.axvspan(region["start"], region["end"], alpha=0.3, color='green', label='Music')
    
    # Plot energy profile
    rms = librosa.feature.rms(y=audio, frame_length=2048, hop_length=512)[0]
    rms_times = librosa.frames_to_time(np.arange(len(rms)), sr=sr, hop_length=512)
    rms_db = librosa.amplitude_to_db(rms, ref=np.max)
    
    ax2.plot(rms_times, rms_db, color='orange', linewidth=2)
    ax2.axhline(silence_map["detection_params"]["silence_threshold"], 
                color='red', linestyle='--', label='Silence Threshold')
    ax2.set_title('RMS Energy Profile')
    ax2.set_xlabel('Time (seconds)')
    ax2.set_ylabel('RMS Energy (dB)')
    ax2.legend()
    
    plt.tight_layout()
    plt.show()
```

### **Example Success Case**
```python
# Expected output for typical song with intro/outro silence
silence_map = detect_silence_regions(typical_song_audio, 22050)

# Expected results:
# {
#     "silence_regions": [
#         {"start": 0.0, "end": 0.8, "duration": 0.8, "confidence": 0.95},
#         {"start": 180.2, "end": 182.0, "duration": 1.8, "confidence": 0.87}
#     ],
#     "music_regions": [
#         {"start": 0.8, "end": 180.2, "duration": 179.4, "energy": 0.15}
#     ],
#     "silence_percentage": 1.4,
#     "music_percentage": 98.6,
#     "segment_count": 1,
#     "detection_params": {...}
# }
```

---

**Phase 3 Complete**. This phase provides clean audio segmentation and temporal landmarks that enable accurate rhythm analysis in subsequent phases.

**Next**: [Phase 4: Beat Position Estimation](phase_04_beat_estimation.md)
