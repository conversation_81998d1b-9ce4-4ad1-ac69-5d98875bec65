"""
Test script for Phase 3 model architecture.
Validates model creation and forward pass with sample data.
"""

import sys
import os
import torch
import numpy as np
import yaml
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.phases.phase_03_model_architecture import TJ<PERSON>eneratorModel
from src.utils.model_utils import create_model_summary


def test_model_forward_pass():
    """Test model forward pass with sample data."""
    
    print("=" * 80)
    print("TESTING PHASE 3 MODEL ARCHITECTURE")
    print("=" * 80)
    
    # Load configuration
    config_path = os.path.join(project_root, "configs", "phase_03_config.yaml")
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # Test each model variant
    variants = config.get('variants', {})
    
    for variant_name, variant_config in variants.items():
        print(f"\nTesting {variant_name} model variant...")
        print("-" * 60)
        
        try:
            # Update configuration with variant parameters
            model_config = config.copy()
            arch_config = model_config['model_architecture']
            
            # Apply variant-specific changes
            if 'encoder_channels' in variant_config:
                arch_config['encoder']['channels'] = variant_config['encoder_channels']
            if 'decoder_channels' in variant_config:
                arch_config['decoder']['channels'] = variant_config['decoder_channels']
            if 'attention_heads' in variant_config:
                arch_config['attention']['num_heads'] = variant_config['attention_heads']
            if 'embed_dim' in variant_config:
                arch_config['attention']['embed_dim'] = variant_config['embed_dim']
            
            # Create model
            model = TJAGeneratorModel(model_config)
            model.eval()
            
            # Get input dimensions
            input_config = arch_config['input_features']
            batch_size = model_config['memory']['batch_size']
            sequence_length = input_config['sequence_length']
            feature_dim = input_config['feature_dim']
            
            # Create sample input data
            sample_input = torch.randn(batch_size, sequence_length, feature_dim)
            
            print(f"Input shape: {sample_input.shape}")
            
            # Forward pass
            with torch.no_grad():
                output = model(sample_input)
            
            print(f"Output shape: {output.shape}")
            print(f"Output range: [{output.min():.4f}, {output.max():.4f}]")
            
            # Verify output properties
            expected_classes = arch_config['output']['num_classes']
            expected_seq_len = arch_config['output']['sequence_length']
            
            assert output.shape[0] == batch_size, f"Batch size mismatch: {output.shape[0]} != {batch_size}"
            assert output.shape[1] == expected_seq_len, f"Sequence length mismatch: {output.shape[1]} != {expected_seq_len}"
            assert output.shape[2] == expected_classes, f"Classes mismatch: {output.shape[2]} != {expected_classes}"
            
            # Check if output is properly normalized (softmax)
            if arch_config['output']['activation'] == 'softmax':
                output_sums = output.sum(dim=-1)
                assert torch.allclose(output_sums, torch.ones_like(output_sums), atol=1e-6), "Softmax normalization failed"
                print("✅ Softmax normalization verified")
            
            # Get model information
            model_info = model.get_model_info()
            print(f"Parameters: {model_info['model_size']['total_parameters']:,}")
            print(f"Model size: {model_info['model_size']['model_size_mb']:.2f} MB")
            print(f"Training memory: {model_info['memory_usage']['total_training_memory_gb']:.3f} GB")
            
            print(f"✅ {variant_name} model test passed!")
            
        except Exception as e:
            print(f"❌ {variant_name} model test failed: {str(e)}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 80)
    print("MODEL ARCHITECTURE TESTING COMPLETED")
    print("=" * 80)


def test_model_summary():
    """Test model summary generation."""
    
    print("\nTesting model summary generation...")
    
    # Load configuration
    config_path = os.path.join(project_root, "configs", "phase_03_config.yaml")
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    # Create standard model
    model = TJAGeneratorModel(config)
    
    # Generate summary
    summary = create_model_summary(model)
    print("\n" + summary)
    
    print("✅ Model summary generation test passed!")


def test_memory_constraints():
    """Test that models meet RTX 3070 memory constraints."""
    
    print("\nTesting RTX 3070 memory constraints...")
    
    # Load configuration
    config_path = os.path.join(project_root, "configs", "phase_03_config.yaml")
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    
    max_vram_gb = config['memory']['max_vram_usage_gb']
    
    variants = config.get('variants', {})
    
    for variant_name, variant_config in variants.items():
        # Update configuration with variant parameters
        model_config = config.copy()
        arch_config = model_config['model_architecture']
        
        # Apply variant-specific changes
        if 'encoder_channels' in variant_config:
            arch_config['encoder']['channels'] = variant_config['encoder_channels']
        if 'decoder_channels' in variant_config:
            arch_config['decoder']['channels'] = variant_config['decoder_channels']
        if 'attention_heads' in variant_config:
            arch_config['attention']['num_heads'] = variant_config['attention_heads']
        if 'embed_dim' in variant_config:
            arch_config['attention']['embed_dim'] = variant_config['embed_dim']
        
        # Create model
        model = TJAGeneratorModel(model_config)
        model_info = model.get_model_info()
        
        training_memory = model_info['memory_usage']['total_training_memory_gb']
        
        print(f"{variant_name}: {training_memory:.3f} GB / {max_vram_gb} GB")
        
        if training_memory <= max_vram_gb:
            print(f"✅ {variant_name} meets memory constraints")
        else:
            print(f"⚠️  {variant_name} exceeds memory constraints")
    
    print("✅ Memory constraint testing completed!")


if __name__ == "__main__":
    try:
        test_model_forward_pass()
        test_model_summary()
        test_memory_constraints()
        
        print("\n🎉 All Phase 3 model tests passed successfully!")
        
    except Exception as e:
        print(f"\n❌ Phase 3 model testing failed: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
