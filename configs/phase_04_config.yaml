# Phase 4: Training Pipeline Configuration
# Windows-compatible paths with RTX 3070 optimization

# Input/Output Paths (Windows format)
paths:
  # Input data from previous phases
  input_audio: "data\\processed\\phase2\\filtered_audio"
  input_metadata: "data\\processed\\phase1\\metadata"
  input_quality: "data\\processed\\phase2\\quality_metrics"
  input_timing: "data\\processed\\phase1\\timing_data"
  
  # Phase 3 model configurations
  model_configs: "data\\processed\\phase3\\model_configs"
  architecture_specs: "data\\processed\\phase3\\architecture_specs"
  
  # Phase 4 outputs
  output_root: "data\\processed\\phase4"
  model_checkpoints: "data\\processed\\phase4\\checkpoints"
  training_logs: "data\\processed\\phase4\\training_logs"
  validation_results: "data\\processed\\phase4\\validation_results"
  tensorboard_logs: "data\\processed\\phase4\\tensorboard"

# Training Configuration
training:
  # Model selection (from Phase 3 variants)
  primary_model: "standard"           # Primary model for training (standard: 1.6M params)
  alternative_models: ["lightweight", "enhanced"]  # Alternative models for comparison
  
  # Training parameters
  epochs: 100                         # Maximum training epochs
  batch_size: 8                       # RTX 3070 optimized batch size
  gradient_accumulation_steps: 4      # Gradient accumulation for effective batch size 32
  mixed_precision: true               # FP16 mixed precision training
  gradient_checkpointing: true        # Memory optimization
  gradient_clip_norm: 1.0             # Gradient clipping for stability
  
  # Learning rate and optimization
  learning_rate: 0.0001               # Initial learning rate (reduced for stability)
  optimizer: "adamw"                  # AdamW optimizer
  weight_decay: 0.01                  # Weight decay for regularization
  beta1: 0.9                          # Adam beta1
  beta2: 0.999                        # Adam beta2
  epsilon: 0.00000001                 # Adam epsilon (1e-8)
  
  # Learning rate scheduling
  scheduler: "cosine"                 # Cosine annealing scheduler
  warmup_steps: 1000                  # Warmup steps
  min_lr_ratio: 0.01                  # Minimum LR as ratio of initial LR
  
  # Validation and early stopping
  validation_split: 0.2               # 20% validation split
  validation_frequency: 1             # Validate every N epochs
  early_stopping_patience: 10         # Early stopping patience
  early_stopping_min_delta: 0.001     # Minimum improvement for early stopping
  
  # Model checkpointing
  save_frequency: 5                   # Save checkpoint every N epochs
  save_best_only: true                # Save only best models
  max_checkpoints: 5                  # Maximum number of checkpoints to keep

# Loss Function Configuration
loss:
  # Primary loss function
  loss_type: "cross_entropy"          # Cross-entropy loss for classification
  label_smoothing: 0.1                # Label smoothing factor
  
  # Class weights for imbalanced data (TJA notes 0,1,2,3,4)
  class_weights: [1.0, 1.2, 1.5, 1.5, 1.2]  # Weights for each note class
  
  # Additional loss components (optional)
  temporal_consistency_weight: 0.1    # Weight for temporal consistency loss
  confidence_penalty_weight: 0.05     # Weight for overconfidence penalty

# Data Processing Configuration
data:
  # Audio processing parameters (from Phase 1)
  sample_rate: 22050                  # Audio sample rate
  audio_channels: 1                   # Mono audio
  
  # Feature extraction parameters
  n_mels: 80                          # Number of mel-spectrogram bands
  n_fft: 2048                         # FFT window size
  hop_length: 512                     # Hop length for STFT
  win_length: 2048                    # Window length for STFT
  
  # Sequence processing
  input_sequence_length: 1024         # Input sequence length (frames)
  output_sequence_length: 512         # Output sequence length (frames)
  overlap_ratio: 0.5                  # Overlap ratio for sequence chunking
  
  # Data augmentation
  augmentation:
    enabled: true                     # Enable data augmentation
    time_stretch_range: [0.9, 1.1]   # Time stretching range
    pitch_shift_range: [-2, 2]       # Pitch shifting range (semitones)
    noise_level: 0.01                 # Gaussian noise level
    volume_range: [0.8, 1.2]         # Volume scaling range
    
  # Data loading
  num_workers: 2                      # Number of data loading workers
  pin_memory: true                    # Pin memory for faster GPU transfer
  shuffle_train: true                 # Shuffle training data
  shuffle_val: false                  # Don't shuffle validation data

# Memory Management (RTX 3070 constraints)
memory:
  max_vram_usage_gb: 7.0              # Maximum VRAM usage
  max_ram_usage_gb: 8.0               # Maximum RAM usage
  
  # Memory optimization techniques
  gradient_checkpointing: true        # Enable gradient checkpointing
  activation_checkpointing: true      # Enable activation checkpointing
  empty_cache_frequency: 10           # Empty CUDA cache every N batches
  
  # Batch processing optimization
  dynamic_batching: false             # Dynamic batch sizing (experimental)
  max_sequence_length: 2048           # Maximum sequence length for memory planning

# Monitoring and Logging
monitoring:
  # Metrics to track
  primary_metric: "accuracy"          # Primary metric for model selection
  track_metrics: ["accuracy", "loss", "f1_score", "precision", "recall"]
  
  # Logging configuration
  log_frequency: 10                   # Log every N batches
  save_predictions: true              # Save validation predictions
  save_attention_maps: false          # Save attention visualizations (memory intensive)
  
  # TensorBoard logging
  tensorboard_enabled: true           # Enable TensorBoard logging
  tensorboard_log_frequency: 50       # Log to TensorBoard every N batches
  
  # Progress tracking
  progress_bar: true                  # Show progress bars
  verbose_logging: true               # Detailed logging

# Quality Gates and Validation
quality_gates:
  min_training_success_rate: 0.95     # Minimum 95% training success rate
  max_vram_usage_gb: 7.0              # Maximum VRAM usage constraint
  min_validation_accuracy: 0.6        # Minimum validation accuracy threshold
  max_training_time_hours: 24         # Maximum training time limit
  
  # Model performance thresholds
  min_f1_score: 0.5                   # Minimum F1 score
  max_overfitting_gap: 0.15           # Maximum gap between train/val accuracy

# Hardware Configuration
hardware:
  device: "cuda"                      # Training device (CUDA for RTX 3070)
  cuda_device_id: 0                   # CUDA device ID
  deterministic: false                # Deterministic training (slower but reproducible)
  benchmark: true                     # Enable CUDNN benchmark for performance
  
  # Multi-GPU settings (if available)
  multi_gpu: false                    # Multi-GPU training
  distributed: false                  # Distributed training

# Experiment Configuration
experiment:
  name: "tja_generation_phase4"       # Experiment name
  version: "v1.0"                     # Experiment version
  description: "TJA chart generation training pipeline - Phase 4"
  tags: ["tja", "audio", "rhythm", "generation", "phase4"]
  
  # Reproducibility
  random_seed: 42                     # Random seed for reproducibility
  
  # Experiment tracking
  save_config: true                   # Save configuration with results
  save_model_summary: true            # Save model architecture summary
  save_training_history: true         # Save complete training history

# Error Handling and Recovery
error_handling:
  continue_on_error: false            # Stop training on errors
  save_on_interrupt: true             # Save checkpoint on keyboard interrupt
  auto_resume: true                   # Auto-resume from last checkpoint
  max_retry_attempts: 3               # Maximum retry attempts for failed operations
  
  # Checkpoint recovery
  recovery_checkpoint_frequency: 100  # Save recovery checkpoint every N batches
  
# Output Configuration
output:
  save_final_model: true              # Save final trained model
  save_best_model: true               # Save best validation model
  save_training_plots: true           # Save training progress plots
  save_confusion_matrix: true         # Save confusion matrix
  save_classification_report: true    # Save detailed classification report
  
  # Model export formats
  export_onnx: false                  # Export to ONNX format
  export_torchscript: false           # Export to TorchScript format
  
  # Results summary
  create_training_report: true        # Create comprehensive training report
  include_model_analysis: true        # Include model analysis in report
