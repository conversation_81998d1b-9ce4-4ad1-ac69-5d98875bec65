# 🧩 Phase 8: Note Sequence Pattern Learning

**Status**: 📋 Planned  
**Estimated Duration**: 5 days  
**Dependencies**: [Phase 7: Basic Note Type Classification](phase_07_note_classification.md)  
**Next Phase**: [Phase 9: Difficulty-Aware Pattern Modeling](phase_09_difficulty_patterns.md)

---

## 1. **Phase Purpose**

This phase learns sequential patterns in note arrangements to generate musically coherent note sequences. This step is isolated because:

- **Sequential modeling** requires different ML architectures (RNNs, Transformers)
- **Pattern recognition** captures musical phrases and rhythmic motifs
- **Context awareness** improves individual note predictions using surrounding notes
- **Temporal dependencies** model how notes relate across time

**Why Isolated**: Sequence learning requires specialized architectures and training procedures different from single-note classification. The learned patterns form the basis for generating coherent musical phrases.

---

## 2. **Inputs & Outputs**

### **Inputs**
```python
# From Phase 7
classified_notes: List[Dict]        # Individual note classifications
note_predictions: List[Dict]        # Classification results with confidence

# From Phase 5 (for timing context)
aligned_beats: List[Dict]           # Beat timing information
tempo_alignment: Dict               # BPM and timing data

# Training sequences (from TJA parsing)
training_sequences: List[Dict] = [
    {
        "sequence_id": str,
        "notes": List[Dict],            # Sequential note data
        "timing": List[float],          # Note timing positions
        "context": {
            "bpm": float,
            "time_signature": str,
            "difficulty": int,
            "genre": str
        }
    }
]

# Input directory structure (matching Phase 7 outputs)
data\\processed\\phase7\\
├── classified_notes\\              # Classified notes from Phase 7
│   ├── {filename}.json            # List of classified_notes Dicts
│   └── ...
├── predictions\\                   # Detailed predictions from Phase 7
│   ├── {filename}.json            # List of note_predictions Dicts
│   └── ...
├── models\\note_classifier.pth     # Trained classifier
data\\processed\\phase5\\
├── aligned_beats\\                 # Beat timing data from Phase 5
│   ├── {filename}.json            # List of aligned_beats Dicts
│   └── ...
```

### **Outputs**
```python
# Trained sequence model
sequence_model: Dict = {
    "model": torch.nn.Module,           # Trained sequence model (LSTM/Transformer)
    "tokenizer": Dict,                  # Note sequence tokenization
    "model_metadata": {
        "architecture": str,            # "LSTM", "GRU", "Transformer"
        "sequence_length": int,         # Input sequence length
        "vocab_size": int,              # Number of unique note tokens
        "hidden_size": int,             # Model hidden dimensions
        "num_layers": int,              # Number of model layers
        "training_sequences": int,      # Training dataset size
        "validation_perplexity": float  # Model perplexity score
    }
}

# Generated sequences
generated_sequences: List[Dict] = [
    {
        "sequence_id": str,
        "generated_notes": List[Dict],      # Generated note sequence
        "generation_confidence": float,    # Overall sequence confidence
        "pattern_coherence": float,        # Musical coherence score
        "timing_consistency": float,       # Timing regularity score
        "generation_metadata": {
            "seed_sequence": List,          # Input seed for generation
            "generation_length": int,       # Number of generated notes
            "temperature": float,           # Generation randomness
            "top_k": int                   # Top-k sampling parameter
        }
    }
]

# Pattern analysis
pattern_analysis: Dict = {
    "common_patterns": List[Dict],      # Frequently occurring patterns
    "pattern_transitions": Dict,        # Pattern transition probabilities
    "rhythmic_motifs": List[Dict],      # Identified rhythmic patterns
    "difficulty_patterns": Dict        # Patterns by difficulty level
}

# Output directory structure
data\\processed\\phase8\\
├── models\\
│   ├── sequence_model.pth          # Trained sequence model
│   ├── tokenizer.pkl               # Note tokenization
│   └── model_metadata.json         # Model configuration
├── generated_sequences\\*.json     # Generated note sequences
├── pattern_analysis\\*.json        # Pattern analysis results
├── training_data\\                 # Processed sequence data
│   ├── sequences.pkl               # Tokenized sequences
│   └── sequence_metadata.json      # Training data info
└── sequence_training_report.json   # Training results
```

---

## 3. **Implementation Plan**

### **Recommended Tools**
```python
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from torch.nn.utils.rnn import pad_sequence
import numpy as np
import pandas as pd
from collections import Counter, defaultdict
from sklearn.model_selection import train_test_split
import pickle
import json
from pathlib import Path
from tqdm import tqdm
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Optional
```

### **Sequence Tokenization**
```python
class NoteSequenceTokenizer:
    """Tokenizer for note sequences."""
    
    def __init__(self):
        self.note_to_token = {}
        self.token_to_note = {}
        self.vocab_size = 0
        
        # Special tokens
        self.PAD_TOKEN = 0
        self.START_TOKEN = 1
        self.END_TOKEN = 2
        self.UNK_TOKEN = 3
        
        # Initialize special tokens
        self.token_to_note = {
            0: "<PAD>",
            1: "<START>",
            2: "<END>", 
            3: "<UNK>"
        }
        self.note_to_token = {v: k for k, v in self.token_to_note.items()}
        self.vocab_size = 4
    
    def fit(self, sequences: List[List[str]]):
        """Build vocabulary from training sequences."""
        
        # Count all note types
        note_counts = Counter()
        for sequence in sequences:
            note_counts.update(sequence)
        
        # Add notes to vocabulary (most common first)
        for note, count in note_counts.most_common():
            if note not in self.note_to_token:
                self.note_to_token[note] = self.vocab_size
                self.token_to_note[self.vocab_size] = note
                self.vocab_size += 1
    
    def encode(self, sequence: List[str]) -> List[int]:
        """Convert note sequence to token sequence."""
        tokens = [self.START_TOKEN]
        for note in sequence:
            token = self.note_to_token.get(note, self.UNK_TOKEN)
            tokens.append(token)
        tokens.append(self.END_TOKEN)
        return tokens
    
    def decode(self, tokens: List[int]) -> List[str]:
        """Convert token sequence to note sequence."""
        notes = []
        for token in tokens:
            if token in [self.PAD_TOKEN, self.START_TOKEN, self.END_TOKEN]:
                continue
            note = self.token_to_note.get(token, "<UNK>")
            if note != "<UNK>":
                notes.append(note)
        return notes

class SequenceDataset(Dataset):
    """Dataset for sequence learning."""
    
    def __init__(self, sequences: List[List[int]], sequence_length: int = 32):
        self.sequences = sequences
        self.sequence_length = sequence_length
        self.data = self._prepare_data()
    
    def _prepare_data(self):
        """Prepare input-target pairs for training."""
        data = []
        
        for sequence in self.sequences:
            # Create sliding windows
            for i in range(len(sequence) - self.sequence_length):
                input_seq = sequence[i:i + self.sequence_length]
                target_seq = sequence[i + 1:i + self.sequence_length + 1]
                data.append((input_seq, target_seq))
        
        return data
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        input_seq, target_seq = self.data[idx]
        return torch.LongTensor(input_seq), torch.LongTensor(target_seq)

class OptimizedSequenceTransformer(nn.Module):
    """
    Optimized Transformer model for note sequence generation.

    Features:
    - Memory-efficient transformer architecture for RTX 3070
    - Gradient checkpointing for reduced memory usage
    - GELU activation for better performance
    - Positional encoding for sequence understanding
    """

    def __init__(self, vocab_size: int, embed_dim: int = 128, num_heads: int = 8,
                 num_layers: int = 4, max_seq_length: int = 512, dropout: float = 0.1):
        super(OptimizedSequenceTransformer, self).__init__()

        self.vocab_size = vocab_size
        self.embed_dim = embed_dim
        self.max_seq_length = max_seq_length

        # Token and positional embeddings
        self.token_embedding = nn.Embedding(vocab_size, embed_dim)
        self.pos_encoding = PositionalEncoding(embed_dim, dropout, max_seq_length)

        # Transformer encoder layers with gradient checkpointing support
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=embed_dim,
            nhead=num_heads,
            dim_feedforward=256,  # Reduced from 512 for memory efficiency
            dropout=dropout,
            activation='gelu',  # Better than ReLU for transformers
            batch_first=True,
            norm_first=True  # Pre-norm for better training stability
        )

        self.transformer = nn.TransformerEncoder(
            encoder_layer,
            num_layers=num_layers,
            enable_nested_tensor=False  # Required for gradient checkpointing
        )

        # Output projection with layer normalization
        self.layer_norm = nn.LayerNorm(embed_dim)
        self.output_projection = nn.Sequential(
            nn.Linear(embed_dim, embed_dim // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim // 2, vocab_size)
        )

        # Initialize weights
        self.apply(self._init_weights)

    def _init_weights(self, module):
        """Initialize weights using Xavier initialization."""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.Embedding):
            torch.nn.init.normal_(module.weight, mean=0, std=0.02)

    def forward(self, x, src_mask=None, src_key_padding_mask=None):
        # Token embedding
        seq_len = x.size(1)
        embedded = self.token_embedding(x) * math.sqrt(self.embed_dim)

        # Add positional encoding
        embedded = self.pos_encoding(embedded)

        # Transformer forward pass with optional gradient checkpointing
        if self.training and hasattr(self, 'use_gradient_checkpointing') and self.use_gradient_checkpointing:
            # Use gradient checkpointing to save memory
            transformer_out = torch.utils.checkpoint.checkpoint(
                self.transformer, embedded, src_mask, src_key_padding_mask
            )
        else:
            transformer_out = self.transformer(embedded, src_mask, src_key_padding_mask)

        # Layer normalization and output projection
        normalized = self.layer_norm(transformer_out)
        output = self.output_projection(normalized)

        return output

    def generate_square_subsequent_mask(self, sz: int) -> torch.Tensor:
        """Generate causal mask for autoregressive generation."""
        mask = torch.triu(torch.ones(sz, sz), diagonal=1)
        mask = mask.masked_fill(mask == 1, float('-inf'))
        return mask

class PositionalEncoding(nn.Module):
    """Positional encoding for transformer model."""

    def __init__(self, d_model: int, dropout: float = 0.1, max_len: int = 512):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)

        # Create positional encoding matrix
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)

        div_term = torch.exp(torch.arange(0, d_model, 2).float() *
                           (-math.log(10000.0) / d_model))

        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)

        self.register_buffer('pe', pe)

    def forward(self, x):
        x = x + self.pe[:x.size(1), :].transpose(0, 1)
        return self.dropout(x)

def train_optimized_sequence_model(
    training_sequences: List[List[str]],
    config: Optional[Dict] = None,
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
) -> Dict:
    """
    Train the optimized transformer sequence generation model with RTX 3070 optimizations.

    Args:
        training_sequences: List of note sequences for training
        config: Training configuration dictionary
        device: Training device

    Returns:
        Trained model and comprehensive training results
    """

    # Default RTX 3070 optimized configuration
    if config is None:
        config = {
            "model_params": {
                "vocab_size": 6,  # Including special tokens
                "embed_dim": 128,
                "num_heads": 8,
                "num_layers": 4,  # Reduced from 6 for memory
                "max_sequence_length": 512,
                "dropout": 0.1
            },
            "training_params": {
                "batch_size": 32,  # Smaller due to sequence length
                "learning_rate": 0.0005,
                "weight_decay": 1e-5,
                "epochs": 150,
                "patience": 20,
                "sequence_length": 64,
                "validation_split": 0.2
            },
            "optimization": {
                "gradient_checkpointing": True,
                "mixed_precision": True,  # FP16 for memory efficiency
                "gradient_clip_val": 1.0,
                "accumulate_grad_batches": 2,
                "lr_scheduler": "OneCycleLR"
            }
        }

    print(f"🚀 Starting optimized sequence model training with RTX 3070 configuration")
    print(f"📊 Training sequences: {len(training_sequences)}")
    print(f"⚙️  Model parameters: {config['model_params']}")
    print(f"🔧 Training parameters: {config['training_params']}")
    print(f"🎯 Optimization features: {config['optimization']}")
    
    # Initialize tokenizer
    tokenizer = NoteSequenceTokenizer()
    tokenizer.fit(training_sequences)
    
    # Tokenize sequences
    tokenized_sequences = [tokenizer.encode(seq) for seq in training_sequences]
    
    # Create dataset
    dataset = SequenceDataset(tokenized_sequences, sequence_length)
    
    # Split data
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(
        dataset, [train_size, val_size]
    )
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    # Initialize model
    model = NoteSequenceLSTM(
        vocab_size=tokenizer.vocab_size,
        embedding_dim=128,
        hidden_size=256,
        num_layers=2
    ).to(device)
    
    # Loss and optimizer
    criterion = nn.CrossEntropyLoss(ignore_index=tokenizer.PAD_TOKEN)
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5)
    
    # Training loop
    train_losses = []
    val_losses = []
    val_perplexities = []
    best_val_loss = float('inf')
    
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        
        for batch_input, batch_target in train_loader:
            batch_input, batch_target = batch_input.to(device), batch_target.to(device)
            
            # Initialize hidden state
            hidden = model.init_hidden(batch_input.size(0), device)
            
            optimizer.zero_grad()
            
            # Forward pass
            output, _ = model(batch_input, hidden)
            
            # Calculate loss
            loss = criterion(output.reshape(-1, tokenizer.vocab_size), 
                           batch_target.reshape(-1))
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            train_loss += loss.item()
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for batch_input, batch_target in val_loader:
                batch_input, batch_target = batch_input.to(device), batch_target.to(device)
                
                hidden = model.init_hidden(batch_input.size(0), device)
                output, _ = model(batch_input, hidden)
                
                loss = criterion(output.reshape(-1, tokenizer.vocab_size),
                               batch_target.reshape(-1))
                val_loss += loss.item()
        
        # Calculate metrics
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        val_perplexity = torch.exp(torch.tensor(avg_val_loss)).item()
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        val_perplexities.append(val_perplexity)
        
        # Learning rate scheduling
        scheduler.step(avg_val_loss)
        
        # Save best model
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            best_model_state = model.state_dict().copy()
        
        # Print progress
        if (epoch + 1) % 10 == 0:
            print(f"Epoch [{epoch+1}/{num_epochs}], "
                  f"Train Loss: {avg_train_loss:.4f}, "
                  f"Val Loss: {avg_val_loss:.4f}, "
                  f"Perplexity: {val_perplexity:.2f}")
    
    # Load best model
    model.load_state_dict(best_model_state)
    
    return {
        "model": model,
        "tokenizer": tokenizer,
        "training_history": {
            "train_losses": train_losses,
            "val_losses": val_losses,
            "val_perplexities": val_perplexities
        },
        "best_val_perplexity": min(val_perplexities),
        "vocab_size": tokenizer.vocab_size
    }

def generate_sequence(
    model: nn.Module,
    tokenizer: NoteSequenceTokenizer,
    seed_sequence: List[str],
    generation_length: int = 16,
    temperature: float = 1.0,
    top_k: int = 10,
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
) -> List[str]:
    """
    Generate a note sequence using the trained model.
    
    Args:
        model: Trained sequence model
        tokenizer: Note tokenizer
        seed_sequence: Initial sequence to start generation
        generation_length: Number of notes to generate
        temperature: Sampling temperature (higher = more random)
        top_k: Top-k sampling parameter
        device: Generation device
        
    Returns:
        Generated note sequence
    """
    
    model.eval()
    
    # Encode seed sequence
    current_sequence = tokenizer.encode(seed_sequence)
    generated_notes = []
    
    # Initialize hidden state
    hidden = model.init_hidden(1, device)
    
    with torch.no_grad():
        for _ in range(generation_length):
            # Prepare input (last sequence_length tokens)
            input_seq = current_sequence[-32:]  # Use last 32 tokens
            input_tensor = torch.LongTensor([input_seq]).to(device)
            
            # Forward pass
            output, hidden = model(input_tensor, hidden)
            
            # Get last output
            last_output = output[0, -1, :]  # [vocab_size]
            
            # Apply temperature
            last_output = last_output / temperature
            
            # Top-k sampling
            if top_k > 0:
                top_k_values, top_k_indices = torch.topk(last_output, top_k)
                # Set non-top-k values to very low probability
                last_output[last_output < top_k_values[-1]] = -float('inf')
            
            # Sample next token
            probabilities = torch.softmax(last_output, dim=0)
            next_token = torch.multinomial(probabilities, 1).item()
            
            # Stop if END token is generated
            if next_token == tokenizer.END_TOKEN:
                break
            
            # Add to sequence
            current_sequence.append(next_token)
            
            # Decode and add to generated notes
            if next_token not in [tokenizer.PAD_TOKEN, tokenizer.START_TOKEN, tokenizer.UNK_TOKEN]:
                note = tokenizer.token_to_note[next_token]
                generated_notes.append(note)
    
    return generated_notes

def analyze_patterns(sequences: List[List[str]], min_pattern_length: int = 3) -> Dict:
    """Analyze common patterns in note sequences."""
    
    pattern_counts = defaultdict(int)
    
    # Extract all possible patterns
    for sequence in sequences:
        for length in range(min_pattern_length, min(len(sequence) + 1, 8)):
            for i in range(len(sequence) - length + 1):
                pattern = tuple(sequence[i:i + length])
                pattern_counts[pattern] += 1
    
    # Sort patterns by frequency
    common_patterns = sorted(pattern_counts.items(), key=lambda x: x[1], reverse=True)
    
    # Convert to readable format
    pattern_analysis = {
        "common_patterns": [
            {
                "pattern": list(pattern),
                "frequency": count,
                "length": len(pattern)
            }
            for pattern, count in common_patterns[:50]  # Top 50 patterns
        ],
        "total_patterns": len(pattern_counts),
        "pattern_lengths": {
            str(length): sum(1 for p, _ in pattern_counts.items() if len(p) == length)
            for length in range(min_pattern_length, 8)
        }
    }
    
    return pattern_analysis
```

---

## 4. **Best Practices**

### **Sequence Preprocessing**
- Use appropriate sequence lengths (16-64 notes typical)
- Handle variable-length sequences with padding
- Include timing information in tokenization
- Balance sequence diversity vs. pattern learning

### **Model Architecture**
```python
# Consider different architectures based on data size
def select_model_architecture(data_size: int, vocab_size: int) -> nn.Module:
    """Select appropriate model architecture based on data size."""
    
    if data_size < 10000:
        # Simple LSTM for small datasets
        return NoteSequenceLSTM(vocab_size, hidden_size=128, num_layers=1)
    elif data_size < 100000:
        # Standard LSTM
        return NoteSequenceLSTM(vocab_size, hidden_size=256, num_layers=2)
    else:
        # Transformer for large datasets
        return NoteSequenceTransformer(vocab_size)
```

### **Generation Control**
- Use temperature to control randomness
- Implement top-k and nucleus sampling
- Add musical constraints (e.g., avoid too many consecutive rests)
- Consider beam search for more coherent generation

### **Training Optimization**
- Use gradient clipping to prevent exploding gradients
- Implement learning rate scheduling
- Monitor perplexity as primary metric
- Use teacher forcing during training

---

## 5. **Challenges & Pitfalls**

### **Long-term Dependencies**
- **Issue**: LSTM may forget long-range musical patterns
- **Example**: Verse-chorus structure, call-and-response patterns
- **Mitigation**: Use attention mechanisms or Transformers
- **Solution**: Implement hierarchical sequence modeling

### **Mode Collapse**
- **Issue**: Model generates repetitive or boring sequences
- **Symptoms**: Same patterns repeated, lack of variety
- **Mitigation**: Use diverse training data and regularization
- **Solution**: Implement diversity loss or adversarial training

### **Musical Coherence**
- **Issue**: Generated sequences may be technically valid but musically nonsensical
- **Example**: Random note patterns that don't follow musical logic
- **Mitigation**: Include musical constraints in generation
- **Solution**: Use music theory rules as post-processing filters

### **Training Data Quality**
- **Issue**: Poor quality training sequences lead to poor generation
- **Symptoms**: Model learns bad patterns from low-quality charts
- **Mitigation**: Curate high-quality training data
- **Solution**: Implement quality scoring for training sequences

---

## 5.5. **Iterative Training Strategy**

### **Multi-Execution Framework for Sequence Pattern Learning**

Sequence pattern learning benefits from curriculum learning and transfer learning approaches, progressively building from simple rhythmic patterns to complex polyrhythmic sequences.

#### **Iterative Sequence Learning Architecture**
```python
class IterativeSequenceTrainer:
    """
    Iterative training framework for note sequence pattern learning.

    Features:
    - Curriculum learning from simple to complex patterns
    - Transfer learning across difficulty levels
    - Progressive sequence length extension
    - Multi-scale pattern diversity optimization
    """

    def __init__(self, config: Dict):
        self.config = config
        self.paths = TJAGenPaths()
        self.logger = TJAGenLogger(8)

        # Iteration management
        self.current_iteration = 0
        self.max_iterations = config.get("max_iterations", 5)
        self.improvement_threshold = config.get("improvement_threshold", 0.015)

        # Model management
        self.models = {
            "base_model": None,        # Base transformer model
            "difficulty_adapters": {}, # Difficulty-specific adapter layers
            "transfer_models": {}      # Pre-trained models for transfer learning
        }

        # Curriculum management
        self.curriculum_stages = {
            "simple": {
                "sequence_length": 16,
                "pattern_complexity": "basic",
                "difficulty_range": [1, 4],
                "time_signatures": ["4/4"]
            },
            "moderate": {
                "sequence_length": 32,
                "pattern_complexity": "intermediate",
                "difficulty_range": [3, 7],
                "time_signatures": ["4/4", "3/4"]
            },
            "complex": {
                "sequence_length": 64,
                "pattern_complexity": "advanced",
                "difficulty_range": [6, 9],
                "time_signatures": ["4/4", "3/4", "6/8"]
            },
            "expert": {
                "sequence_length": 128,
                "pattern_complexity": "expert",
                "difficulty_range": [8, 10],
                "time_signatures": ["4/4", "3/4", "6/8", "7/8", "5/4"]
            }
        }

        # Training data organization
        self.training_data = {
            "by_difficulty": {},       # Data organized by difficulty level
            "by_complexity": {},       # Data organized by pattern complexity
            "transfer_data": {},       # Data for transfer learning
            "diversity_samples": []    # High-diversity pattern samples
        }

        # Performance tracking
        self.iteration_metrics = []
        self.best_perplexity = float('inf')
        self.best_sequence_accuracy = 0.0

        # Setup iteration directories
        self._setup_iteration_directories()

    def _setup_iteration_directories(self):
        """Create directories for iterative sequence training."""
        base_dir = self.paths.get_phase_output_dir(8)

        directories = [
            base_dir / "iterations",
            base_dir / "iterations" / "curriculum_stages",
            base_dir / "iterations" / "transfer_models",
            base_dir / "iterations" / "difficulty_adapters",
            base_dir / "iterations" / "diversity_analysis",
            base_dir / "iterations" / "pattern_metrics"
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

    def execute_iterative_sequence_training(self, initial_data: Dict) -> Dict:
        """
        Execute complete iterative training pipeline for sequence learning.

        Args:
            initial_data: Initial sequence training data

        Returns:
            Final training results with curriculum progression
        """

        self.logger.info("🔄 Starting iterative sequence pattern training")

        # Organize data by difficulty and complexity
        self._organize_training_data(initial_data)

        iteration_results = []

        for iteration in range(self.max_iterations):
            self.current_iteration = iteration
            self.logger.info(f"🔄 Starting iteration {iteration + 1}/{self.max_iterations}")

            # Execute single iteration with curriculum learning
            iteration_result = self._execute_sequence_iteration()
            iteration_results.append(iteration_result)

            # Check for convergence
            if self._check_sequence_convergence(iteration_result):
                self.logger.info(f"✅ Sequence learning converged after {iteration + 1} iterations")
                break

            # Prepare for next iteration
            self._prepare_next_sequence_iteration(iteration_result)

        # Generate final results
        final_results = self._generate_final_sequence_results(iteration_results)

        self.logger.info("🎉 Iterative sequence training completed")
        return final_results

    def _execute_sequence_iteration(self) -> Dict:
        """Execute a single sequence learning iteration."""

        iteration_start_time = time.time()

        # 1. Apply curriculum learning stage
        curriculum_stage = self._get_current_curriculum_stage()
        curriculum_data = self._apply_curriculum_learning(curriculum_stage)

        # 2. Apply transfer learning if applicable
        transfer_results = self._apply_transfer_learning(curriculum_stage)

        # 3. Train sequence model with progressive complexity
        training_results = self._train_sequence_model(curriculum_data, transfer_results)

        # 4. Evaluate pattern diversity and coverage
        diversity_metrics = self._evaluate_pattern_diversity(training_results)

        # 5. Identify sequence gaps for next iteration
        sequence_gaps = self._identify_sequence_gaps(training_results, diversity_metrics)

        # 6. Save iteration artifacts
        self._save_sequence_iteration_artifacts(
            training_results, diversity_metrics, sequence_gaps, curriculum_stage
        )

        iteration_time = time.time() - iteration_start_time

        return {
            "iteration": self.current_iteration,
            "curriculum_stage": curriculum_stage,
            "training_time": iteration_time,
            "training_results": training_results,
            "diversity_metrics": diversity_metrics,
            "sequence_gaps_identified": len(sequence_gaps),
            "transfer_learning_applied": transfer_results is not None,
            "perplexity": training_results.get("final_perplexity", float('inf')),
            "sequence_accuracy": training_results.get("sequence_accuracy", 0.0)
        }

    def _get_current_curriculum_stage(self) -> str:
        """Determine current curriculum learning stage."""

        # Map iterations to curriculum stages
        stage_mapping = {
            0: "simple",
            1: "moderate",
            2: "complex",
            3: "expert",
            4: "expert"  # Continue with expert level
        }

        return stage_mapping.get(self.current_iteration, "expert")

    def _apply_curriculum_learning(self, stage: str) -> Dict:
        """Apply curriculum learning for current stage."""

        stage_config = self.curriculum_stages[stage]

        # Filter data based on curriculum stage
        filtered_data = []

        for difficulty in stage_config["difficulty_range"]:
            if difficulty in self.training_data["by_difficulty"]:
                difficulty_data = self.training_data["by_difficulty"][difficulty]

                # Filter by sequence length
                max_length = stage_config["sequence_length"]
                length_filtered = [
                    seq for seq in difficulty_data
                    if len(seq["sequence"]) <= max_length
                ]

                # Filter by time signature
                time_sig_filtered = [
                    seq for seq in length_filtered
                    if seq.get("time_signature", "4/4") in stage_config["time_signatures"]
                ]

                filtered_data.extend(time_sig_filtered)

        # Apply progressive sequence length extension
        if self.current_iteration > 0:
            # Gradually increase sequence lengths
            extended_data = self._extend_sequence_lengths(filtered_data, stage_config)
            filtered_data.extend(extended_data)

        self.logger.info(f"📚 Curriculum stage '{stage}': {len(filtered_data)} sequences")

        return {
            "stage": stage,
            "stage_config": stage_config,
            "filtered_data": filtered_data,
            "data_count": len(filtered_data)
        }

    def _apply_transfer_learning(self, curriculum_stage: str) -> Optional[Dict]:
        """Apply transfer learning from pre-trained models."""

        # Only apply transfer learning after first iteration
        if self.current_iteration == 0:
            return None

        transfer_results = {}

        # Transfer from previous difficulty levels
        if curriculum_stage in ["moderate", "complex", "expert"]:
            # Load pre-trained model from previous stage
            previous_stages = {
                "moderate": "simple",
                "complex": "moderate",
                "expert": "complex"
            }

            previous_stage = previous_stages[curriculum_stage]

            if previous_stage in self.models["transfer_models"]:
                # Initialize current model with previous stage weights
                previous_model = self.models["transfer_models"][previous_stage]

                # Transfer compatible layers
                transfer_success = self._transfer_model_weights(previous_model)

                if transfer_success:
                    transfer_results["source_stage"] = previous_stage
                    transfer_results["target_stage"] = curriculum_stage
                    transfer_results["layers_transferred"] = self._count_transferred_layers()

                    self.logger.info(f"🔄 Applied transfer learning: {previous_stage} → {curriculum_stage}")

        # Transfer from difficulty-specific adapters
        if curriculum_stage in ["complex", "expert"]:
            adapter_results = self._apply_difficulty_adapters(curriculum_stage)
            if adapter_results:
                transfer_results["adapter_results"] = adapter_results

        return transfer_results if transfer_results else None

    def _train_sequence_model(self, curriculum_data: Dict, transfer_results: Optional[Dict]) -> Dict:
        """Train sequence model with curriculum data."""

        # Initialize or update model based on curriculum stage
        if self.models["base_model"] is None or transfer_results is None:
            self.models["base_model"] = self._create_sequence_model(curriculum_data["stage_config"])

        # Prepare training data
        train_sequences = curriculum_data["filtered_data"]

        # Apply progressive training strategy
        training_config = self._get_progressive_training_config(curriculum_data["stage"])

        # Execute training with stage-specific parameters
        training_results = self._execute_progressive_training(
            train_sequences, training_config, transfer_results
        )

        # Save stage-specific model
        stage_model_path = (
            self.paths.get_phase_output_dir(8) /
            "iterations" / "curriculum_stages" /
            f"{curriculum_data['stage']}_model.pth"
        )

        torch.save(self.models["base_model"].state_dict(), stage_model_path)

        # Store model for potential transfer learning
        self.models["transfer_models"][curriculum_data["stage"]] = self.models["base_model"]

        return training_results

    def _evaluate_pattern_diversity(self, training_results: Dict) -> Dict:
        """Evaluate pattern diversity and coverage."""

        diversity_metrics = {
            "pattern_types_covered": 0,
            "rhythm_complexity_distribution": {},
            "sequence_length_distribution": {},
            "difficulty_coverage": {},
            "unique_pattern_count": 0,
            "repetition_ratio": 0.0,
            "diversity_score": 0.0
        }

        # Analyze generated sequences
        generated_sequences = training_results.get("generated_sequences", [])

        if generated_sequences:
            # Count unique patterns
            unique_patterns = set()
            pattern_types = set()

            for sequence in generated_sequences:
                # Convert sequence to pattern signature
                pattern_signature = self._create_pattern_signature(sequence)
                unique_patterns.add(pattern_signature)

                # Identify pattern type
                pattern_type = self._classify_pattern_type(sequence)
                pattern_types.add(pattern_type)

            diversity_metrics["unique_pattern_count"] = len(unique_patterns)
            diversity_metrics["pattern_types_covered"] = len(pattern_types)
            diversity_metrics["repetition_ratio"] = 1.0 - (len(unique_patterns) / len(generated_sequences))

            # Calculate diversity score
            diversity_metrics["diversity_score"] = self._calculate_diversity_score(
                unique_patterns, pattern_types, generated_sequences
            )

        self.logger.info(f"🎨 Pattern diversity score: {diversity_metrics['diversity_score']:.3f}")

        return diversity_metrics

    def _identify_sequence_gaps(self, training_results: Dict, diversity_metrics: Dict) -> List[Dict]:
        """Identify gaps in sequence coverage for next iteration."""

        sequence_gaps = []

        # Identify under-represented pattern types
        target_pattern_types = [
            "simple_alternating", "complex_polyrhythm", "rapid_succession",
            "syncopated_patterns", "mixed_note_types", "long_sequences"
        ]

        covered_types = set(diversity_metrics.get("pattern_types_covered", []))

        for pattern_type in target_pattern_types:
            if pattern_type not in covered_types:
                gap = {
                    "gap_type": "missing_pattern_type",
                    "pattern_type": pattern_type,
                    "priority": "high",
                    "suggested_action": f"Generate more {pattern_type} examples"
                }
                sequence_gaps.append(gap)

        # Identify difficulty level gaps
        difficulty_coverage = diversity_metrics.get("difficulty_coverage", {})

        for difficulty in range(1, 11):
            coverage = difficulty_coverage.get(str(difficulty), 0)
            if coverage < 0.1:  # Less than 10% coverage
                gap = {
                    "gap_type": "difficulty_coverage",
                    "difficulty_level": difficulty,
                    "current_coverage": coverage,
                    "priority": "medium",
                    "suggested_action": f"Increase difficulty {difficulty} sequence generation"
                }
                sequence_gaps.append(gap)

        # Identify sequence length gaps
        length_distribution = diversity_metrics.get("sequence_length_distribution", {})
        target_lengths = [16, 32, 64, 128]

        for length in target_lengths:
            if str(length) not in length_distribution or length_distribution[str(length)] < 0.05:
                gap = {
                    "gap_type": "sequence_length",
                    "target_length": length,
                    "priority": "low",
                    "suggested_action": f"Generate more sequences of length {length}"
                }
                sequence_gaps.append(gap)

        self.logger.info(f"🔍 Identified {len(sequence_gaps)} sequence gaps")
        return sequence_gaps

# Iterative sequence training configuration
ITERATIVE_SEQUENCE_CONFIG = {
    "max_iterations": 5,
    "improvement_threshold": 0.015,  # 1.5% improvement threshold
    "target_perplexity": 15.0,       # Target perplexity threshold
    "target_sequence_accuracy": 0.82, # Target sequence accuracy

    # Curriculum learning schedule
    "curriculum_progression": {
        "iteration_0": "simple",     # Simple patterns, short sequences
        "iteration_1": "moderate",   # Moderate complexity, medium sequences
        "iteration_2": "complex",    # Complex patterns, longer sequences
        "iteration_3": "expert",     # Expert patterns, full complexity
        "iteration_4": "expert"      # Continued expert refinement
    },

    # Transfer learning configuration
    "transfer_learning": {
        "enable_stage_transfer": True,    # Transfer between curriculum stages
        "enable_difficulty_adapters": True, # Use difficulty-specific adapters
        "freeze_base_layers": False,      # Allow fine-tuning of all layers
        "adapter_dimension": 64           # Adapter layer dimension
    },

    # Pattern diversity targets
    "diversity_targets": {
        "min_unique_patterns": 1000,     # Minimum unique patterns
        "min_pattern_types": 8,          # Minimum pattern types covered
        "max_repetition_ratio": 0.3,     # Maximum pattern repetition
        "min_diversity_score": 0.7       # Minimum diversity score
    },

    # Retraining triggers
    "retraining_triggers": {
        "perplexity_increase": 2.0,       # Retrain if perplexity increases by 2.0
        "diversity_drop": 0.1,            # Retrain if diversity drops by 10%
        "new_pattern_threshold": 500,     # Retrain when 500 new patterns available
        "sequence_gap_threshold": 10      # Retrain when 10+ gaps identified
    }
}
```

#### **Multi-Execution Workflow for Sequence Learning**

```mermaid
graph TD
    A[Initial Sequence Data] --> B[Iteration 0: Simple Patterns]
    B --> C[Curriculum Learning Stage]
    C --> D[Transfer Learning Check]
    D --> E[Train Sequence Model]
    E --> F[Evaluate Pattern Diversity]
    F --> G[Identify Sequence Gaps]
    G --> H{Convergence Check}
    H -->|No| I[Iteration N+1: Next Curriculum Stage]
    H -->|Yes| J[Final Model Selection]
    I --> K[Update Training Data]
    K --> L[Apply Progressive Complexity]
    L --> C
    J --> M[Deploy Best Model]

    style A fill:#e1f5fe
    style J fill:#c8e6c9
    style M fill:#c8e6c9
```

#### **Sequence Learning Performance Targets**

```python
# Expected improvement targets per iteration
SEQUENCE_ITERATION_TARGETS = {
    "iteration_0": {
        "baseline_perplexity": 25.0,
        "target_perplexity": 20.0,
        "focus": "Simple alternating patterns, basic rhythms"
    },
    "iteration_1": {
        "baseline_perplexity": 20.0,
        "target_perplexity": 18.0,
        "focus": "Moderate complexity, mixed note types"
    },
    "iteration_2": {
        "baseline_perplexity": 18.0,
        "target_perplexity": 16.0,
        "focus": "Complex patterns, longer sequences"
    },
    "iteration_3": {
        "baseline_perplexity": 16.0,
        "target_perplexity": 15.0,
        "focus": "Expert patterns, polyrhythms"
    },
    "iteration_4": {
        "baseline_perplexity": 15.0,
        "target_perplexity": 14.0,
        "focus": "Refinement, edge case handling"
    }
}

# Quality gates for iterative sequence training
SEQUENCE_QUALITY_GATES = {
    "max_final_perplexity": 15.0,        # Maximum acceptable perplexity
    "min_sequence_accuracy": 0.82,       # Minimum sequence accuracy
    "min_pattern_diversity": 0.7,        # Minimum pattern diversity score
    "min_unique_patterns": 1000,         # Minimum unique patterns generated
    "max_repetition_ratio": 0.3          # Maximum pattern repetition allowed
}
```

---

## 6. **Dependencies & Unlocks**

### **Prerequisites**
- **Phase 7 Complete**: Individual note classifications
- **Required Files**:
  - `data\\processed\\phase7\\predictions\\*.json`
  - `data\\raw\\ese\\**\\*.tja` (for sequence training data)
- **Libraries**: `torch`, `transformers` (optional) for sequence modeling

### **What This Phase Unlocks**
- **Phase 9**: Sequence patterns enable difficulty-aware modeling
- **Phase 10**: Sequential context improves measure segmentation
- **Phase 13**: Generated sequences provide coherent chart content
- **Quality Improvement**: All downstream phases benefit from coherent note sequences

### **Output Dependencies**
Subsequent phases depend on these Phase 8 outputs:
- `data\\processed\\phase8\\models\\sequence_model.pth` - Trained sequence model
- `data\\processed\\phase8\\generated_sequences\\*.json` - Generated note sequences
- `data\\processed\\phase8\\pattern_analysis\\*.json` - Musical pattern analysis

---

## 7. **Validation Strategy**

### **Unit Tests**
```python
def test_tokenizer():
    """Test sequence tokenization."""
    tokenizer = NoteSequenceTokenizer()
    
    # Test sequences
    sequences = [
        ["don", "ka", "don", "rest"],
        ["ka", "don", "ka", "ka"]
    ]
    
    tokenizer.fit(sequences)
    
    # Test encoding/decoding
    encoded = tokenizer.encode(sequences[0])
    decoded = tokenizer.decode(encoded)
    
    assert decoded == sequences[0]
    assert tokenizer.vocab_size > 4  # Should include note types

def test_sequence_generation():
    """Test sequence generation functionality."""
    # Create simple model for testing
    vocab_size = 10
    model = NoteSequenceLSTM(vocab_size, embedding_dim=32, hidden_size=64, num_layers=1)
    
    # Create simple tokenizer
    tokenizer = NoteSequenceTokenizer()
    tokenizer.note_to_token = {"don": 4, "ka": 5, "rest": 6}
    tokenizer.token_to_note = {4: "don", 5: "ka", 6: "rest"}
    tokenizer.vocab_size = 7
    
    # Test generation
    seed = ["don", "ka"]
    generated = generate_sequence(model, tokenizer, seed, generation_length=4)
    
    assert len(generated) <= 4
    assert all(note in ["don", "ka", "rest"] for note in generated)
```

### **Quality Metrics**
- **Perplexity**: <50 on validation set (lower is better)
- **Pattern Coherence**: >0.7 musical coherence score
- **Generation Diversity**: Generated sequences should have >0.8 diversity score
- **Training Speed**: <2 hours training time on RTX 3070

### **Musical Validation**
```python
def evaluate_musical_quality(generated_sequences: List[List[str]]) -> Dict:
    """Evaluate musical quality of generated sequences."""
    
    metrics = {
        "pattern_repetition": calculate_pattern_repetition(generated_sequences),
        "rhythmic_consistency": calculate_rhythmic_consistency(generated_sequences),
        "note_distribution": calculate_note_distribution(generated_sequences),
        "sequence_diversity": calculate_sequence_diversity(generated_sequences)
    }
    
    return metrics

def calculate_pattern_repetition(sequences: List[List[str]]) -> float:
    """Calculate how much patterns repeat (lower is better for diversity)."""
    all_patterns = []
    
    for sequence in sequences:
        for i in range(len(sequence) - 2):
            pattern = tuple(sequence[i:i+3])
            all_patterns.append(pattern)
    
    if not all_patterns:
        return 0.0
    
    pattern_counts = Counter(all_patterns)
    total_patterns = len(all_patterns)
    unique_patterns = len(pattern_counts)
    
    return 1.0 - (unique_patterns / total_patterns)
```

### **Example Success Case**
```python
# Expected training results
training_results = {
    "best_val_perplexity": 12.5,
    "vocab_size": 8,
    "training_sequences": 5420,
    "pattern_coherence": 0.82
}

# Expected generated sequence
generated_sequence = {
    "sequence_id": "test_001",
    "generated_notes": ["don", "ka", "don", "don", "ka", "rest", "don", "ka"],
    "generation_confidence": 0.78,
    "pattern_coherence": 0.85,
    "timing_consistency": 0.91,
    "generation_metadata": {
        "seed_sequence": ["don", "ka"],
        "generation_length": 8,
        "temperature": 0.8,
        "top_k": 5
    }
}

# Expected pattern analysis
pattern_analysis = {
    "common_patterns": [
        {"pattern": ["don", "ka", "don"], "frequency": 245, "length": 3},
        {"pattern": ["ka", "don", "ka"], "frequency": 198, "length": 3},
        {"pattern": ["don", "don", "ka"], "frequency": 156, "length": 3}
    ],
    "total_patterns": 1247,
    "pattern_lengths": {"3": 892, "4": 245, "5": 110}
}
```

---

**Phase 8 Complete**. This phase learns sequential patterns in note arrangements, enabling the generation of musically coherent note sequences that form the foundation for difficulty-aware pattern modeling.

**Next**: [Phase 9: Difficulty-Aware Pattern Modeling](phase_09_difficulty_patterns.md)
