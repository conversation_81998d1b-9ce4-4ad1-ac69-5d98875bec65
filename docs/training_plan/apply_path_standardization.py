#!/usr/bin/env python3
"""
<PERSON><PERSON>t to apply comprehensive path standardization across all TJA generation pipeline phases.
This script updates all phase documentation files to use the standardized path formats.
"""

import re
from pathlib import Path
from typing import Dict, List, Tuple
import json

class PathStandardizer:
    """Apply comprehensive path standardization to all phase documentation."""
    
    def __init__(self):
        self.docs_dir = Path("docs/training_plan")
        self.phase_files = []
        self.standardization_log = []
        
        # Find all phase files
        self._find_phase_files()
        
        # Define standardization patterns
        self.path_patterns = self._define_path_patterns()
    
    def _find_phase_files(self):
        """Find all phase documentation files."""
        phase_patterns = [
            "phase_01_*.md",
            "phase_02_*.md", 
            "phase_03_*.md",
            "phase_04_*.md",
            "phase_05_*.md",
            "phase_06_*.md",
            "phase_06_5_*.md",  # Special case for Phase 6.5
            "phase_07_*.md",
            "phase_08_*.md",
            "phase_09_*.md",
            "phase_10_*.md",
            "phase_11_*.md",
            "phase_12_*.md",
            "phase_13_*.md",
            "phase_14_*.md",
            "phase_15_*.md",
            "README.md"
        ]
        
        for pattern in phase_patterns:
            files = list(self.docs_dir.glob(pattern))
            self.phase_files.extend(files)
        
        print(f"Found {len(self.phase_files)} files to standardize")
    
    def _define_path_patterns(self) -> Dict[str, Dict[str, str]]:
        """Define path standardization patterns."""
        return {
            "directory_separators": {
                # Convert forward slashes to backslashes in Windows paths
                r"data/([^/\s]+)": r"data\\\1",
                r"data\\([^\\]+)/([^/\s]+)": r"data\\\1\\\2",
                r"data\\([^\\]+)\\([^\\]+)/([^/\s]+)": r"data\\\1\\\2\\\3",
                r"src/([^/\s]+)": r"src\\\1",
                r"models/([^/\s]+)": r"models\\\1",
                r"logs/([^/\s]+)": r"logs\\\1",
                r"config/([^/\s]+)": r"config\\\1",
                r"temp/([^/\s]+)": r"temp\\\1",
                r"tests/([^/\s]+)": r"tests\\\1"
            },
            "phase_directories": {
                # Standardize phase directory naming
                r"data\\processed\\phase(\d+)([^\\]*)": r"data\\processed\\phase\1\2",
                r"data\\models\\phase(\d+)": r"data\\models\\phase\1",
                r"data\\logs\\phase(\d+)": r"data\\logs\\phase\1",
                r"data\\temp\\phase(\d+)": r"data\\temp\\phase\1"
            },
            "file_naming": {
                # Standardize file naming patterns
                r"([a-zA-Z0-9_]+)\.json": r"\1.json",
                r"([a-zA-Z0-9_]+)\.npy": r"\1.npy",
                r"([a-zA-Z0-9_]+)\.pth": r"\1.pth",
                r"([a-zA-Z0-9_]+)\.pkl": r"\1.pkl",
                r"([a-zA-Z0-9_]+)\.log": r"\1.log"
            },
            "model_paths": {
                # Standardize model file paths
                r"models/([^/\s]+)\.pth": r"models\\\1.pth",
                r"models/([^/\s]+)\.pkl": r"models\\\1.pkl",
                r"models/([^/\s]+)/([^/\s]+)": r"models\\\1\\\2"
            },
            "import_statements": {
                # Ensure proper pathlib imports
                r"from pathlib import Path": r"from pathlib import Path",
                r"import os\.path": r"from pathlib import Path  # Use pathlib instead of os.path",
                r"os\.path\.join": r"Path() /  # Use pathlib Path operations"
            }
        }
    
    def standardize_all_files(self):
        """Apply standardization to all phase files."""
        print("Starting comprehensive path standardization...")
        
        for phase_file in self.phase_files:
            print(f"Processing {phase_file.name}...")
            changes_made = self.standardize_file(phase_file)
            
            if changes_made:
                self.standardization_log.append({
                    "file": phase_file.name,
                    "changes": changes_made
                })
        
        # Generate standardization report
        self.generate_report()
        print("Path standardization completed!")
    
    def standardize_file(self, file_path: Path) -> List[str]:
        """Standardize paths in a single file."""
        changes_made = []
        
        try:
            # Read file content
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # Apply each category of standardization patterns
            for category, patterns in self.path_patterns.items():
                for pattern, replacement in patterns.items():
                    matches = re.findall(pattern, content)
                    if matches:
                        content = re.sub(pattern, replacement, content)
                        changes_made.append(f"{category}: {len(matches)} replacements")
            
            # Apply specific fixes for known issues
            content = self.apply_specific_fixes(content, file_path.name)
            
            # Write back if changes were made
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"  ✅ Updated {file_path.name}")
            else:
                print(f"  ℹ️  No changes needed for {file_path.name}")
        
        except Exception as e:
            print(f"  ❌ Error processing {file_path.name}: {str(e)}")
            changes_made.append(f"ERROR: {str(e)}")
        
        return changes_made
    
    def apply_specific_fixes(self, content: str, filename: str) -> str:
        """Apply specific fixes for known issues in particular files."""
        
        # Fix Phase 6 BMP -> BPM typo
        if "phase_06" in filename:
            content = content.replace("bmp_validation", "bpm_validation")
        
        # Fix Phase 7 dependency reference
        if "phase_07" in filename:
            content = content.replace("data\\processed\\phase6\\", "data\\processed\\phase6_5\\")
            content = content.replace("Phase 6 Complete", "Phase 6.5 Complete")
        
        # Fix Phase 11 BMP -> BPM typo
        if "phase_11" in filename:
            content = content.replace("bmp_changes", "bpm_changes")
        
        # Fix Phase 13 directory reference
        if "phase_13" in filename:
            content = content.replace("measure_boundaries", "measure_segments")
        
        # Fix Phase 15 model paths
        if "phase_15" in filename:
            content = re.sub(r'"models/([^"]+)"', r'"models\\\1"', content)
            content = re.sub(r"'models/([^']+)'", r"'models\\\1'", content)
        
        # Fix README paths
        if filename == "README.md":
            content = content.replace("data\\raw\\ese\\", "data\\\\raw\\\\ese\\\\")
            content = content.replace("D:\\TJAGen\\", "D:\\\\TJAGen\\\\")
        
        return content
    
    def generate_report(self):
        """Generate a comprehensive standardization report."""
        report = {
            "standardization_timestamp": "2024-01-15T14:30:22",
            "total_files_processed": len(self.phase_files),
            "files_modified": len(self.standardization_log),
            "files_unchanged": len(self.phase_files) - len(self.standardization_log),
            "detailed_changes": self.standardization_log,
            "summary": {
                "directory_separator_fixes": 0,
                "phase_directory_standardizations": 0,
                "file_naming_corrections": 0,
                "model_path_fixes": 0,
                "import_statement_updates": 0,
                "specific_issue_fixes": 0
            }
        }
        
        # Count changes by category
        for file_changes in self.standardization_log:
            for change in file_changes["changes"]:
                if "directory_separators" in change:
                    report["summary"]["directory_separator_fixes"] += 1
                elif "phase_directories" in change:
                    report["summary"]["phase_directory_standardizations"] += 1
                elif "file_naming" in change:
                    report["summary"]["file_naming_corrections"] += 1
                elif "model_paths" in change:
                    report["summary"]["model_path_fixes"] += 1
                elif "import_statements" in change:
                    report["summary"]["import_statement_updates"] += 1
                elif "ERROR" not in change:
                    report["summary"]["specific_issue_fixes"] += 1
        
        # Save report
        report_file = self.docs_dir / "path_standardization_applied.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 Standardization Report:")
        print(f"  Total files processed: {report['total_files_processed']}")
        print(f"  Files modified: {report['files_modified']}")
        print(f"  Files unchanged: {report['files_unchanged']}")
        print(f"  Directory separator fixes: {report['summary']['directory_separator_fixes']}")
        print(f"  Phase directory standardizations: {report['summary']['phase_directory_standardizations']}")
        print(f"  File naming corrections: {report['summary']['file_naming_corrections']}")
        print(f"  Model path fixes: {report['summary']['model_path_fixes']}")
        print(f"  Import statement updates: {report['summary']['import_statement_updates']}")
        print(f"  Specific issue fixes: {report['summary']['specific_issue_fixes']}")
        print(f"\n📄 Detailed report saved: {report_file}")
    
    def validate_standardization(self) -> bool:
        """Validate that standardization was applied correctly."""
        print("\n🔍 Validating standardization...")
        
        validation_issues = []
        
        for phase_file in self.phase_files:
            with open(phase_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for remaining forward slashes in Windows paths
            if re.search(r'data/[^/\s]*[^/\s]', content):
                validation_issues.append(f"{phase_file.name}: Forward slashes in data paths")
            
            # Check for inconsistent phase directory naming
            if re.search(r'phase\d+[^\\]', content) and 'phase' in content:
                validation_issues.append(f"{phase_file.name}: Inconsistent phase directory naming")
            
            # Check for old BMP references
            if 'bmp_' in content.lower() and phase_file.name in ['phase_06_note_candidates.md', 'phase_11_special_sections.md']:
                validation_issues.append(f"{phase_file.name}: BMP references should be BPM")
        
        if validation_issues:
            print("❌ Validation issues found:")
            for issue in validation_issues:
                print(f"  - {issue}")
            return False
        else:
            print("✅ All validations passed!")
            return True

def main():
    """Execute comprehensive path standardization."""
    print("🚀 TJA Generation Pipeline Path Standardization")
    print("=" * 60)
    
    standardizer = PathStandardizer()
    
    # Apply standardization
    standardizer.standardize_all_files()
    
    # Validate results
    validation_passed = standardizer.validate_standardization()
    
    print("\n" + "=" * 60)
    if validation_passed:
        print("✅ Path standardization completed successfully!")
        print("All 16 phases now use consistent Windows-compatible paths.")
        return 0
    else:
        print("❌ Path standardization completed with issues!")
        print("Please review the validation issues above.")
        return 1

if __name__ == "__main__":
    import sys
    exit_code = main()
    sys.exit(exit_code)
