"""
Training pipeline implementation for Phase 4.
Handles model training, validation, and optimization.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.cuda.amp import GradScaler, autocast
from torch.utils.tensorboard import SummaryWriter
import numpy as np
import logging
import json
import os
import time
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime
import matplotlib.pyplot as plt
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score, confusion_matrix

logger = logging.getLogger(__name__)


class TJATrainer:
    """Main trainer class for TJA chart generation."""
    
    def __init__(self, model: nn.Module, config: Dict[str, Any], device: torch.device):
        """
        Initialize the trainer.
        
        Args:
            model: The neural network model to train
            config: Configuration dictionary
            device: Training device (CPU/CUDA)
        """
        self.model = model
        self.config = config
        self.device = device
        
        # Move model to device
        self.model.to(device)
        
        # Training configuration
        self.training_config = config['training']
        self.loss_config = config['loss']
        self.memory_config = config['memory']
        self.monitoring_config = config['monitoring']
        
        # Setup training components
        self._setup_optimizer()
        self._setup_scheduler()
        self._setup_loss_function()
        self._setup_mixed_precision()
        self._setup_monitoring()
        
        # Training state
        self.current_epoch = 0
        self.global_step = 0
        self.best_val_metric = 0.0
        self.patience_counter = 0
        
        # Training history
        self.train_history = {
            'loss': [], 'accuracy': [], 'f1_score': [],
            'precision': [], 'recall': []
        }
        self.val_history = {
            'loss': [], 'accuracy': [], 'f1_score': [],
            'precision': [], 'recall': []
        }
        
        logger.info("TJA Trainer initialized successfully")
    
    def _setup_optimizer(self):
        """Setup optimizer with configuration parameters."""
        
        optimizer_name = self.training_config['optimizer'].lower()
        lr = self.training_config['learning_rate']
        weight_decay = self.training_config['weight_decay']
        
        if optimizer_name == 'adamw':
            self.optimizer = optim.AdamW(
                self.model.parameters(),
                lr=lr,
                weight_decay=weight_decay,
                betas=(self.training_config['beta1'], self.training_config['beta2']),
                eps=self.training_config['epsilon']
            )
        elif optimizer_name == 'adam':
            self.optimizer = optim.Adam(
                self.model.parameters(),
                lr=lr,
                weight_decay=weight_decay,
                betas=(self.training_config['beta1'], self.training_config['beta2']),
                eps=self.training_config['epsilon']
            )
        else:
            raise ValueError(f"Unsupported optimizer: {optimizer_name}")
        
        logger.info(f"Optimizer setup: {optimizer_name} with lr={lr}")
    
    def _setup_scheduler(self):
        """Setup learning rate scheduler."""
        
        scheduler_name = self.training_config['scheduler'].lower()
        
        if scheduler_name == 'cosine':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=self.training_config['epochs'],
                eta_min=self.training_config['learning_rate'] * self.training_config['min_lr_ratio']
            )
        elif scheduler_name == 'step':
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=self.training_config.get('step_size', 30),
                gamma=self.training_config.get('gamma', 0.1)
            )
        else:
            self.scheduler = None
        
        logger.info(f"Scheduler setup: {scheduler_name}")
    
    def _setup_loss_function(self):
        """Setup loss function with class weights and label smoothing."""
        
        # Class weights
        class_weights = torch.FloatTensor(self.loss_config['class_weights']).to(self.device)
        
        # Cross-entropy loss with label smoothing
        self.criterion = nn.CrossEntropyLoss(
            weight=class_weights,
            label_smoothing=self.loss_config['label_smoothing']
        )
        
        # Additional loss components
        self.temporal_consistency_weight = self.loss_config.get('temporal_consistency_weight', 0.0)
        self.confidence_penalty_weight = self.loss_config.get('confidence_penalty_weight', 0.0)
        
        logger.info("Loss function setup with class weights and label smoothing")
    
    def _setup_mixed_precision(self):
        """Setup mixed precision training."""
        
        if self.training_config['mixed_precision']:
            self.scaler = GradScaler()
            self.use_amp = True
            logger.info("Mixed precision training enabled")
        else:
            self.scaler = None
            self.use_amp = False
            logger.info("Mixed precision training disabled")
    
    def _setup_monitoring(self):
        """Setup monitoring and logging."""
        
        # TensorBoard writer
        if self.monitoring_config['tensorboard_enabled']:
            log_dir = self.config['paths']['tensorboard_logs']
            os.makedirs(log_dir, exist_ok=True)
            self.writer = SummaryWriter(log_dir)
            logger.info(f"TensorBoard logging enabled: {log_dir}")
        else:
            self.writer = None
        
        # Metrics tracking
        self.primary_metric = self.monitoring_config['primary_metric']
        self.track_metrics = self.monitoring_config['track_metrics']
    
    def train_epoch(self, train_loader) -> Dict[str, float]:
        """
        Train for one epoch.
        
        Args:
            train_loader: Training data loader
            
        Returns:
            Dictionary of training metrics
        """
        
        self.model.train()
        
        # Initialize metrics
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        # Progress tracking
        num_batches = len(train_loader)
        log_frequency = self.monitoring_config['log_frequency']
        
        for batch_idx, batch in enumerate(train_loader):
            # Move data to device
            inputs = batch['input'].to(self.device)
            targets = batch['target'].to(self.device)
            
            # Forward pass with mixed precision
            if self.use_amp:
                with autocast():
                    outputs = self.model(inputs)
                    loss = self._compute_loss(outputs, targets)
            else:
                outputs = self.model(inputs)
                loss = self._compute_loss(outputs, targets)
            
            # Backward pass
            if self.use_amp:
                self.scaler.scale(loss).backward()

                # Gradient accumulation
                if (batch_idx + 1) % self.training_config['gradient_accumulation_steps'] == 0:
                    # Gradient clipping
                    if 'gradient_clip_norm' in self.training_config:
                        self.scaler.unscale_(self.optimizer)
                        torch.nn.utils.clip_grad_norm_(
                            self.model.parameters(),
                            self.training_config['gradient_clip_norm']
                        )

                    self.scaler.step(self.optimizer)
                    self.scaler.update()
                    self.optimizer.zero_grad()
            else:
                loss.backward()

                # Gradient accumulation
                if (batch_idx + 1) % self.training_config['gradient_accumulation_steps'] == 0:
                    # Gradient clipping
                    if 'gradient_clip_norm' in self.training_config:
                        torch.nn.utils.clip_grad_norm_(
                            self.model.parameters(),
                            self.training_config['gradient_clip_norm']
                        )

                    self.optimizer.step()
                    self.optimizer.zero_grad()
            
            # Update metrics
            total_loss += loss.item()
            
            # Get predictions
            predictions = torch.argmax(outputs, dim=-1)
            all_predictions.extend(predictions.cpu().numpy().flatten())
            all_targets.extend(targets.cpu().numpy().flatten())
            
            # Logging
            if batch_idx % log_frequency == 0:
                logger.info(f"Epoch {self.current_epoch}, Batch {batch_idx}/{num_batches}, "
                           f"Loss: {loss.item():.4f}")
            
            # TensorBoard logging
            if self.writer and batch_idx % self.monitoring_config['tensorboard_log_frequency'] == 0:
                self.writer.add_scalar('Train/BatchLoss', loss.item(), self.global_step)
                self.writer.add_scalar('Train/LearningRate', 
                                     self.optimizer.param_groups[0]['lr'], self.global_step)
            
            self.global_step += 1
            
            # Memory management
            if batch_idx % self.memory_config['empty_cache_frequency'] == 0:
                torch.cuda.empty_cache()
        
        # Calculate epoch metrics
        avg_loss = total_loss / num_batches
        metrics = self._calculate_metrics(all_predictions, all_targets)
        metrics['loss'] = avg_loss
        
        return metrics
    
    def validate_epoch(self, val_loader) -> Dict[str, float]:
        """
        Validate for one epoch.
        
        Args:
            val_loader: Validation data loader
            
        Returns:
            Dictionary of validation metrics
        """
        
        self.model.eval()
        
        # Initialize metrics
        total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(val_loader):
                # Move data to device
                inputs = batch['input'].to(self.device)
                targets = batch['target'].to(self.device)
                
                # Forward pass
                if self.use_amp:
                    with autocast():
                        outputs = self.model(inputs)
                        loss = self._compute_loss(outputs, targets)
                else:
                    outputs = self.model(inputs)
                    loss = self._compute_loss(outputs, targets)
                
                # Update metrics
                total_loss += loss.item()
                
                # Get predictions
                predictions = torch.argmax(outputs, dim=-1)
                all_predictions.extend(predictions.cpu().numpy().flatten())
                all_targets.extend(targets.cpu().numpy().flatten())
        
        # Calculate epoch metrics
        avg_loss = total_loss / len(val_loader)
        metrics = self._calculate_metrics(all_predictions, all_targets)
        metrics['loss'] = avg_loss
        
        return metrics
    
    def _compute_loss(self, outputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Compute total loss including additional components.
        
        Args:
            outputs: Model outputs (batch, seq_len, num_classes)
            targets: Target labels (batch, seq_len)
            
        Returns:
            Total loss tensor
        """
        
        # Reshape for cross-entropy loss
        batch_size, seq_len, num_classes = outputs.shape
        outputs_flat = outputs.view(-1, num_classes)
        targets_flat = targets.view(-1)
        
        # Primary cross-entropy loss
        primary_loss = self.criterion(outputs_flat, targets_flat)
        
        total_loss = primary_loss
        
        # Temporal consistency loss (optional)
        if self.temporal_consistency_weight > 0:
            temporal_loss = self._compute_temporal_consistency_loss(outputs)
            total_loss += self.temporal_consistency_weight * temporal_loss
        
        # Confidence penalty (optional)
        if self.confidence_penalty_weight > 0:
            confidence_penalty = self._compute_confidence_penalty(outputs)
            total_loss += self.confidence_penalty_weight * confidence_penalty
        
        return total_loss
    
    def _compute_temporal_consistency_loss(self, outputs: torch.Tensor) -> torch.Tensor:
        """Compute temporal consistency loss to encourage smooth predictions."""
        
        # Compute differences between consecutive time steps
        diff = outputs[:, 1:, :] - outputs[:, :-1, :]
        temporal_loss = torch.mean(torch.sum(diff ** 2, dim=-1))
        
        return temporal_loss
    
    def _compute_confidence_penalty(self, outputs: torch.Tensor) -> torch.Tensor:
        """Compute confidence penalty to prevent overconfident predictions."""
        
        # Apply softmax to get probabilities
        probs = torch.softmax(outputs, dim=-1)
        
        # Compute entropy (higher entropy = less confident)
        entropy = -torch.sum(probs * torch.log(probs + 1e-8), dim=-1)
        
        # Penalty for low entropy (high confidence)
        max_entropy = np.log(outputs.shape[-1])  # Maximum possible entropy
        confidence_penalty = torch.mean(max_entropy - entropy)
        
        return confidence_penalty
    
    def _calculate_metrics(self, predictions: List[int], targets: List[int]) -> Dict[str, float]:
        """Calculate evaluation metrics."""
        
        predictions = np.array(predictions)
        targets = np.array(targets)
        
        metrics = {}
        
        if 'accuracy' in self.track_metrics:
            metrics['accuracy'] = accuracy_score(targets, predictions)
        
        if 'f1_score' in self.track_metrics:
            metrics['f1_score'] = f1_score(targets, predictions, average='weighted', zero_division=0)
        
        if 'precision' in self.track_metrics:
            metrics['precision'] = precision_score(targets, predictions, average='weighted', zero_division=0)
        
        if 'recall' in self.track_metrics:
            metrics['recall'] = recall_score(targets, predictions, average='weighted', zero_division=0)
        
        return metrics
    
    def save_checkpoint(self, filepath: str, is_best: bool = False):
        """Save model checkpoint."""
        
        checkpoint = {
            'epoch': self.current_epoch,
            'global_step': self.global_step,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'scaler_state_dict': self.scaler.state_dict() if self.scaler else None,
            'best_val_metric': self.best_val_metric,
            'train_history': self.train_history,
            'val_history': self.val_history,
            'config': self.config
        }
        
        torch.save(checkpoint, filepath)
        
        if is_best:
            best_filepath = filepath.replace('.pth', '_best.pth')
            torch.save(checkpoint, best_filepath)
        
        logger.info(f"Checkpoint saved: {filepath}")
    
    def load_checkpoint(self, filepath: str):
        """Load model checkpoint."""
        
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if self.scheduler and checkpoint['scheduler_state_dict']:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        if self.scaler and checkpoint['scaler_state_dict']:
            self.scaler.load_state_dict(checkpoint['scaler_state_dict'])
        
        self.current_epoch = checkpoint['epoch']
        self.global_step = checkpoint['global_step']
        self.best_val_metric = checkpoint['best_val_metric']
        self.train_history = checkpoint['train_history']
        self.val_history = checkpoint['val_history']
        
        logger.info(f"Checkpoint loaded: {filepath}")
    
    def should_stop_early(self, val_metric: float) -> bool:
        """Check if training should stop early."""

        patience = self.training_config['early_stopping_patience']
        min_delta = self.training_config['early_stopping_min_delta']

        if val_metric > self.best_val_metric + min_delta:
            self.best_val_metric = val_metric
            self.patience_counter = 0
            return False
        else:
            self.patience_counter += 1
            return self.patience_counter >= patience

    def log_epoch_metrics(self, epoch: int, train_metrics: Dict[str, float],
                         val_metrics: Dict[str, float]):
        """Log metrics for the epoch."""

        # Update history
        for metric_name in self.track_metrics:
            if metric_name in train_metrics:
                self.train_history[metric_name].append(train_metrics[metric_name])
            if metric_name in val_metrics:
                self.val_history[metric_name].append(val_metrics[metric_name])

        # Console logging
        logger.info(f"Epoch {epoch} Summary:")
        logger.info(f"  Train - Loss: {train_metrics['loss']:.4f}, "
                   f"Accuracy: {train_metrics.get('accuracy', 0):.4f}")
        logger.info(f"  Val   - Loss: {val_metrics['loss']:.4f}, "
                   f"Accuracy: {val_metrics.get('accuracy', 0):.4f}")

        # TensorBoard logging
        if self.writer:
            for metric_name, value in train_metrics.items():
                self.writer.add_scalar(f'Train/{metric_name}', value, epoch)

            for metric_name, value in val_metrics.items():
                self.writer.add_scalar(f'Val/{metric_name}', value, epoch)

    def cleanup(self):
        """Cleanup resources."""
        if self.writer:
            self.writer.close()

        # Clear CUDA cache
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        logger.info("Trainer cleanup completed")
