"""
Full dataset execution script for Phase 1: Audio Loading & Format Standardization
Processes all 2,878 audio files with resume capability and robust error handling.
"""

import os
import sys
import time
import json
from pathlib import Path

def main():
    """Main execution function for full dataset processing."""
    print("🎼 TJA Chart Generation - Phase 1 Full Dataset Processing")
    print("=" * 70)
    
    # Add src to Python path
    src_path = Path(__file__).parent.parent / "src"
    sys.path.insert(0, str(src_path))
    
    try:
        from phases.phase_01_audio_loading import Phase1AudioProcessor
        
        # Initialize processor
        processor = Phase1AudioProcessor()
        
        # Get all audio files
        print("📁 Scanning for audio files...")
        audio_files = processor.get_audio_files_list(validation_mode=False)
        total_files = len(audio_files)
        
        print(f"Found {total_files} audio files to process")
        
        # Check for existing processed files to enable resume
        audio_output_dir = Path(processor.config['paths']['audio_output'])
        existing_files = set()
        if audio_output_dir.exists():
            existing_files = {f.stem for f in audio_output_dir.glob("*.npy")}
            print(f"Found {len(existing_files)} already processed files")
        
        # Filter out already processed files
        remaining_files = [f for f in audio_files if f.stem not in existing_files]
        print(f"Remaining files to process: {len(remaining_files)}")
        
        if not remaining_files:
            print("✅ All files already processed!")
            return True
        
        # Process in manageable chunks
        chunk_size = 100  # Smaller chunks for stability
        chunks = [remaining_files[i:i + chunk_size] for i in range(0, len(remaining_files), chunk_size)]
        
        print(f"Processing in {len(chunks)} chunks of up to {chunk_size} files each")
        
        total_successful = len(existing_files)  # Start with already processed
        total_failed = 0
        
        for chunk_idx, chunk in enumerate(chunks, 1):
            print(f"\n🚀 Processing chunk {chunk_idx}/{len(chunks)} ({len(chunk)} files)...")
            
            chunk_successful = 0
            chunk_failed = 0
            
            # Process each file in the chunk individually for better error handling
            for file_idx, file_path in enumerate(chunk, 1):
                try:
                    success, metadata = processor.process_single_file(file_path)
                    
                    if success:
                        chunk_successful += 1
                        total_successful += 1
                        print(f"  ✅ [{file_idx}/{len(chunk)}] {file_path.name}")
                    else:
                        chunk_failed += 1
                        total_failed += 1
                        error_msg = metadata.get('error_message', 'Unknown error')
                        print(f"  ❌ [{file_idx}/{len(chunk)}] {file_path.name}: {error_msg}")
                        
                        processor.stats['errors'].append({
                            'file': str(file_path),
                            'error': error_msg
                        })
                    
                    # Update statistics
                    processor.stats['processed_files'] = total_successful + total_failed
                    processor.stats['successful_files'] = total_successful
                    processor.stats['failed_files'] = total_failed
                    
                    # Periodic memory cleanup
                    if file_idx % 10 == 0:
                        processor.memory_manager.force_garbage_collection()
                        
                except Exception as e:
                    chunk_failed += 1
                    total_failed += 1
                    print(f"  💥 [{file_idx}/{len(chunk)}] {file_path.name}: Unexpected error - {str(e)}")
                    
                    processor.stats['errors'].append({
                        'file': str(file_path),
                        'error': f"Unexpected error: {str(e)}"
                    })
            
            # Chunk summary
            success_rate = total_successful / (total_successful + total_failed) if (total_successful + total_failed) > 0 else 0
            overall_progress = (total_successful + total_failed) / total_files
            
            print(f"Chunk {chunk_idx} complete: {chunk_successful}/{len(chunk)} successful")
            print(f"Overall progress: {total_successful + total_failed}/{total_files} files ({overall_progress:.1%})")
            print(f"Overall success rate: {success_rate:.1%}")
            
            # Save intermediate progress
            if chunk_idx % 5 == 0:  # Save every 5 chunks
                processor.stats['total_files'] = total_files
                processor.save_processing_summary()
                print(f"📊 Progress saved after chunk {chunk_idx}")
            
            # Brief pause between chunks
            time.sleep(2)
        
        # Final statistics
        final_success_rate = total_successful / total_files if total_files > 0 else 0
        
        print(f"\n📊 Final Results:")
        print(f"   Total files: {total_files}")
        print(f"   Successful: {total_successful} ({final_success_rate:.1%})")
        print(f"   Failed: {total_failed}")
        print(f"   Already processed: {len(existing_files)}")
        
        # Save final summary
        processor.stats['total_files'] = total_files
        processor.save_processing_summary()
        
        # Validate outputs
        validation_results = processor.validate_outputs()
        print(f"   Audio files created: {validation_results['audio_files_created']}")
        print(f"   Metadata files created: {validation_results['metadata_files_created']}")
        
        # Check quality gates
        min_success_rate = processor.config['quality']['min_success_rate']
        if final_success_rate >= min_success_rate:
            print(f"✅ Quality gate passed: {final_success_rate:.1%} >= {min_success_rate:.1%}")
            return True
        else:
            print(f"❌ Quality gate failed: {final_success_rate:.1%} < {min_success_rate:.1%}")
            return False
            
    except Exception as e:
        print(f"❌ Error during processing: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
