"""
Phase 3: Model Architecture Design
Implements neural network architecture for audio-to-TJA chart generation.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import logging
import json
import os
import yaml
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import time
from datetime import datetime

from ..utils.model_utils import (
    TJAEncoder, TJADecoder, MultiHeadAttention,
    calculate_model_size, estimate_memory_usage,
    save_model_config, validate_model_config
)
from ..utils.path_utils import ensure_directory_exists

logger = logging.getLogger(__name__)


class TJAGeneratorModel(nn.Module):
    """Complete TJA chart generation model with encoder-decoder architecture."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        arch_config = config['model_architecture']
        
        # Encoder
        self.encoder = TJAEncoder(arch_config)
        
        # Attention mechanism
        attention_config = arch_config['attention']
        self.attention = MultiHeadAttention(
            embed_dim=attention_config['embed_dim'],
            num_heads=attention_config['num_heads'],
            dropout=attention_config['dropout']
        )
        
        # Feature projection for attention
        self.feature_proj = nn.Linear(self.encoder.output_dim, attention_config['embed_dim'])
        
        # Decoder
        self.decoder = TJADecoder(arch_config)
        
        # Output configuration
        self.num_classes = arch_config['output']['num_classes']
        self.output_activation = arch_config['output']['activation']
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Forward pass through the complete model."""
        
        # Encode audio features
        encoded = self.encoder(x)  # (batch, time, features)
        
        # Project to attention dimension
        projected = self.feature_proj(encoded)  # (batch, time, embed_dim)
        
        # Apply attention
        attended = self.attention(projected, mask)  # (batch, time, embed_dim)
        
        # Decode to TJA chart
        output = self.decoder(attended)  # (batch, time, num_classes)
        
        # Apply output activation
        if self.output_activation == "softmax":
            output = F.softmax(output, dim=-1)
        elif self.output_activation == "sigmoid":
            output = torch.sigmoid(output)
            
        return output
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get comprehensive model information."""
        model_size = calculate_model_size(self)
        
        # Get input dimensions from config
        input_config = self.config['model_architecture']['input_features']
        batch_size = self.config['memory']['batch_size']
        
        memory_usage = estimate_memory_usage(
            self, batch_size, 
            input_config['sequence_length'],
            input_config['feature_dim']
        )
        
        return {
            'architecture_type': self.config['model_architecture']['architecture_type'],
            'model_size': model_size,
            'memory_usage': memory_usage,
            'input_shape': (batch_size, input_config['sequence_length'], input_config['feature_dim']),
            'output_shape': (batch_size, self.config['model_architecture']['output']['sequence_length'], self.num_classes)
        }


class ModelArchitectureDesigner:
    """Main class for Phase 3: Model Architecture Design."""
    
    def __init__(self, config_path: str):
        """Initialize the model architecture designer."""
        
        # Load configuration
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        # Validate configuration
        if not validate_model_config(self.config):
            raise ValueError("Invalid model configuration")
        
        # Setup paths
        self.setup_paths()
        
        # Setup logging
        self.setup_logging()
        
        # Initialize statistics
        self.stats = {
            'models_created': 0,
            'models_failed': 0,
            'total_processing_time': 0.0,
            'memory_profiles': [],
            'architecture_variants': []
        }
        
        logger.info("Model Architecture Designer initialized")
    
    def setup_paths(self) -> None:
        """Setup output directory structure."""
        paths = self.config['paths']
        
        # Create output directories
        for path_key, path_value in paths.items():
            if 'output' in path_key or path_key in ['model_configs', 'architecture_specs', 'logs_output']:
                ensure_directory_exists(path_value)
        
        logger.info("Output directory structure created")
    
    def setup_logging(self) -> None:
        """Setup logging configuration."""
        log_config = self.config['logging']
        log_file = os.path.join(self.config['paths']['logs_output'], 'phase3_model_architecture.log')
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, log_config['level']),
            format=log_config['format'],
            handlers=[
                logging.FileHandler(log_file, encoding=log_config['file_encoding']),
                logging.StreamHandler()
            ]
        )
        
        logger.info("Logging configured")
    
    def create_model_variant(self, variant_name: str, variant_config: Dict[str, Any]) -> Optional[TJAGeneratorModel]:
        """Create a model variant with specific configuration."""
        
        try:
            start_time = time.time()
            
            # Update configuration with variant parameters
            model_config = self.config.copy()
            arch_config = model_config['model_architecture']
            
            # Apply variant-specific changes
            if 'encoder_channels' in variant_config:
                arch_config['encoder']['channels'] = variant_config['encoder_channels']
            if 'decoder_channels' in variant_config:
                arch_config['decoder']['channels'] = variant_config['decoder_channels']
            if 'attention_heads' in variant_config:
                arch_config['attention']['num_heads'] = variant_config['attention_heads']
            if 'embed_dim' in variant_config:
                arch_config['attention']['embed_dim'] = variant_config['embed_dim']
            
            # Create model
            model = TJAGeneratorModel(model_config)
            
            # Get model information
            model_info = model.get_model_info()
            
            # Check memory constraints
            max_vram = self.config['memory']['max_vram_usage_gb']
            if model_info['memory_usage']['total_training_memory_gb'] > max_vram:
                logger.warning(f"Model variant '{variant_name}' exceeds VRAM limit: "
                             f"{model_info['memory_usage']['total_training_memory_gb']:.2f}GB > {max_vram}GB")
            
            # Save model configuration
            config_path = os.path.join(
                self.config['paths']['model_configs'],
                f"{variant_name}_config.json"
            )
            save_model_config(model_config, config_path)
            
            # Save architecture specifications
            spec_path = os.path.join(
                self.config['paths']['architecture_specs'],
                f"{variant_name}_specs.json"
            )
            with open(spec_path, 'w', encoding='utf-8') as f:
                json.dump(model_info, f, indent=2, ensure_ascii=False)
            
            processing_time = time.time() - start_time
            
            # Update statistics
            self.stats['models_created'] += 1
            self.stats['total_processing_time'] += processing_time
            self.stats['memory_profiles'].append({
                'variant': variant_name,
                'memory_usage': model_info['memory_usage']
            })
            self.stats['architecture_variants'].append({
                'variant': variant_name,
                'model_size': model_info['model_size'],
                'processing_time': processing_time
            })
            
            logger.info(f"Model variant '{variant_name}' created successfully")
            logger.info(f"Parameters: {model_info['model_size']['total_parameters']:,}")
            logger.info(f"Model size: {model_info['model_size']['model_size_mb']:.2f} MB")
            logger.info(f"Training memory: {model_info['memory_usage']['total_training_memory_gb']:.2f} GB")
            
            return model
            
        except Exception as e:
            self.stats['models_failed'] += 1
            logger.error(f"Failed to create model variant '{variant_name}': {str(e)}")
            return None
    
    def design_architecture_variants(self) -> Dict[str, Optional[TJAGeneratorModel]]:
        """Design multiple architecture variants for experimentation."""
        
        logger.info("Starting architecture variant design")
        
        variants = self.config.get('variants', {})
        models = {}
        
        for variant_name, variant_config in variants.items():
            logger.info(f"Creating model variant: {variant_name}")
            model = self.create_model_variant(variant_name, variant_config)
            models[variant_name] = model
        
        logger.info(f"Architecture variant design completed. "
                   f"Created: {self.stats['models_created']}, Failed: {self.stats['models_failed']}")
        
        return models
    
    def validate_architecture_constraints(self, model: TJAGeneratorModel) -> bool:
        """Validate that architecture meets project constraints."""
        
        model_info = model.get_model_info()
        quality_gates = self.config['quality_gates']
        
        # Check model size constraint
        if model_info['model_size']['model_size_mb'] > quality_gates['max_model_size_mb']:
            logger.error(f"Model size exceeds limit: "
                        f"{model_info['model_size']['model_size_mb']:.2f} MB > "
                        f"{quality_gates['max_model_size_mb']} MB")
            return False
        
        # Check VRAM constraint
        max_vram = self.config['memory']['max_vram_usage_gb']
        if model_info['memory_usage']['total_training_memory_gb'] > max_vram:
            logger.error(f"Training memory exceeds VRAM limit: "
                        f"{model_info['memory_usage']['total_training_memory_gb']:.2f} GB > "
                        f"{max_vram} GB")
            return False
        
        # Check output format (simplified TJA)
        if model.num_classes != 5:
            logger.error(f"Model output classes must be 5 for simplified TJA, got: {model.num_classes}")
            return False
        
        logger.info("Architecture constraints validation passed")
        return True
    
    def generate_summary_report(self) -> Dict[str, Any]:
        """Generate comprehensive summary report."""
        
        # Calculate success rate
        total_attempts = self.stats['models_created'] + self.stats['models_failed']
        success_rate = self.stats['models_created'] / total_attempts if total_attempts > 0 else 0.0
        
        # Quality gate assessment
        quality_gates = self.config['quality_gates']
        meets_quality_gates = success_rate >= quality_gates['min_model_creation_rate']
        
        summary = {
            'phase': 'Phase 3: Model Architecture Design',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'configuration': self.config,
            'statistics': {
                'total_variants_attempted': total_attempts,
                'successful_variants': self.stats['models_created'],
                'failed_variants': self.stats['models_failed'],
                'success_rate': success_rate,
                'total_processing_time': self.stats['total_processing_time'],
                'average_processing_time': (
                    self.stats['total_processing_time'] / self.stats['models_created'] 
                    if self.stats['models_created'] > 0 else 0.0
                )
            },
            'quality_metrics': {
                'success_rate': success_rate,
                'meets_quality_gates': meets_quality_gates,
                'architecture_variants': len(self.stats['architecture_variants']),
                'memory_profiles_generated': len(self.stats['memory_profiles'])
            },
            'architecture_variants': self.stats['architecture_variants'],
            'memory_profiles': self.stats['memory_profiles']
        }
        
        # Save summary report
        summary_path = os.path.join(
            self.config['paths']['logs_output'],
            'phase3_summary.json'
        )
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Summary report saved to: {summary_path}")
        return summary
    
    def run_phase(self) -> Dict[str, Any]:
        """Execute the complete Phase 3 process."""
        
        logger.info("=== Starting Phase 3: Model Architecture Design ===")
        start_time = time.time()
        
        try:
            # Design architecture variants
            models = self.design_architecture_variants()
            
            # Validate at least one successful model
            successful_models = [m for m in models.values() if m is not None]
            if not successful_models:
                raise RuntimeError("No successful model variants created")
            
            # Validate architecture constraints for successful models
            for variant_name, model in models.items():
                if model is not None:
                    if not self.validate_architecture_constraints(model):
                        logger.warning(f"Model variant '{variant_name}' failed constraint validation")
            
            # Generate summary report
            summary = self.generate_summary_report()
            
            total_time = time.time() - start_time
            logger.info(f"=== Phase 3 completed successfully in {total_time:.2f} seconds ===")
            logger.info(f"Created {len(successful_models)} successful model variants")
            
            return summary
            
        except Exception as e:
            logger.error(f"Phase 3 failed: {str(e)}")
            raise
