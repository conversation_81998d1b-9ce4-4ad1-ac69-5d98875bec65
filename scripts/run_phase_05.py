"""
Phase 5 Execution Script: Tempo Alignment & BPM Validation
Runs the complete Phase 5 tempo alignment and BPM validation process.
"""

import sys
import os
import logging
import traceback
import time
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.phases.phase_05_tempo_alignment import Phase5TempoAlignment


def check_prerequisites():
    """Check that all prerequisites are met for Phase 5."""
    
    print("Checking Phase 5 prerequisites...")
    
    # Check Phase 1 outputs (audio data)
    phase1_audio = "data/processed/phase1/audio"
    if not os.path.exists(phase1_audio):
        raise FileNotFoundError(f"Phase 1 audio data not found: {phase1_audio}")
    
    # Check for audio files
    audio_files = list(Path(phase1_audio).glob("*.npy"))
    if not audio_files:
        raise FileNotFoundError(f"No audio files found in {phase1_audio}")
    
    print(f"Found {len(audio_files)} audio files")
    
    # Check Phase 1 metadata
    phase1_metadata = "data/processed/phase1/metadata"
    if not os.path.exists(phase1_metadata):
        raise FileNotFoundError(f"Phase 1 metadata not found: {phase1_metadata}")
    
    # Check TJA data
    tja_data_dir = "data/raw/ese"
    if not os.path.exists(tja_data_dir):
        raise FileNotFoundError(f"TJA data directory not found: {tja_data_dir}")
    
    # Check for TJA files
    tja_files = list(Path(tja_data_dir).rglob("*.tja"))
    if not tja_files:
        raise FileNotFoundError(f"No TJA files found in {tja_data_dir}")
    
    print(f"Found {len(tja_files)} TJA files")
    
    # Check Phase 2 quality metrics (optional)
    phase2_quality = "data/processed/phase2/quality_metrics"
    if os.path.exists(phase2_quality):
        print("Phase 2 quality metrics found")
    else:
        print("Phase 2 quality metrics not found (optional)")
    
    print("All prerequisites satisfied!")


def display_processing_progress(summary):
    """Display processing progress and results."""
    
    print("\n" + "=" * 80)
    print("PHASE 5 PROCESSING RESULTS")
    print("=" * 80)
    
    # Processing statistics
    stats = summary['processing_stats']
    print(f"📊 Processing Statistics:")
    print(f"   Total files: {stats['total_files']}")
    print(f"   Processed files: {stats['processed_files']}")
    print(f"   Successful alignments: {stats['successful_alignments']}")
    print(f"   Failed alignments: {stats['failed_alignments']}")
    print(f"   Validation passed: {stats['validation_passed']}")
    print(f"   Validation failed: {stats['validation_failed']}")
    print(f"   Processing time: {stats['processing_time']:.2f} seconds")
    
    # Quality metrics
    quality = summary['quality_metrics']
    print(f"\n📈 Quality Metrics:")
    print(f"   Success rate: {quality['success_rate']:.1%}")
    print(f"   Validation pass rate: {quality['validation_pass_rate']:.1%}")
    print(f"   Average BPM error: {quality['avg_bmp_error']:.2f}%")
    print(f"   Average alignment confidence: {quality['avg_alignment_confidence']:.3f}")
    print(f"   Average tempo drift: {quality['avg_tempo_drift']:.2f}%")
    
    # Quality gates
    gates = summary['quality_gates']
    print(f"\n🎯 Quality Gates:")
    print(f"   Overall: {'✅ PASSED' if gates['passed'] else '❌ FAILED'}")
    print(f"   Success rate: {'✅' if gates['results']['success_rate_passed'] else '❌'} "
          f"{quality['success_rate']:.1%} (required: {gates['requirements']['min_processing_success_rate']:.1%})")
    print(f"   Validation rate: {'✅' if gates['results']['validation_rate_passed'] else '❌'} "
          f"{quality['validation_pass_rate']:.1%} (required: {gates['requirements']['min_bmp_validation_pass_rate']:.1%})")
    print(f"   BPM error: {'✅' if gates['results']['bmp_error_passed'] else '❌'} "
          f"{quality['avg_bmp_error']:.2f}% (max: {gates['requirements']['max_avg_bmp_error']:.2f}%)")
    print(f"   Confidence: {'✅' if gates['results']['confidence_passed'] else '❌'} "
          f"{quality['avg_alignment_confidence']:.3f} (min: {gates['requirements']['min_avg_alignment_confidence']:.3f})")
    
    # Errors
    if summary['errors']:
        print(f"\n❌ Processing Errors ({len(summary['errors'])}):")
        for error in summary['errors'][:5]:  # Show first 5 errors
            print(f"   - {error['song']}: {error['error']}")
        if len(summary['errors']) > 5:
            print(f"   ... and {len(summary['errors']) - 5} more errors")
    
    print("=" * 80)


def main():
    """Main execution function."""
    
    print("PHASE 5: TEMPO ALIGNMENT & BPM VALIDATION")
    print("=" * 80)
    print("This phase aligns detected beats with TJA BPM references")
    print("and validates tempo consistency across the dataset.")
    print("=" * 80)
    
    try:
        # Check prerequisites
        check_prerequisites()
        
        # Configuration
        config_path = "configs/phase_05_config.yaml"
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        print(f"\n📋 Loading configuration from: {config_path}")
        
        # Initialize Phase 5 processor
        print("Initializing Tempo Alignment Pipeline...")
        processor = Phase5TempoAlignment(config_path)
        
        # Run processing
        print("Starting Phase 5 tempo alignment execution...")
        print("This may take several minutes depending on dataset size...")
        
        start_time = time.time()
        summary = processor.run_tempo_alignment_pipeline()
        end_time = time.time()
        
        # Display results
        display_processing_progress(summary)
        
        # Success message
        print("\n" + "=" * 80)
        print("PHASE 5 TEMPO ALIGNMENT COMPLETED SUCCESSFULLY!")
        print("Beat positions have been aligned with TJA BPM references!")
        print("Output files saved to:")
        print(f"   - Tempo alignment: data\\processed\\phase5\\tempo_alignment\\")
        print(f"   - BPM validation: data\\processed\\phase5\\bmp_validation\\")
        print(f"   - Aligned beats: data\\processed\\phase5\\aligned_beats\\")
        print(f"   - Timing analysis: data\\processed\\phase5\\timing_analysis\\")
        print(f"   - Summary report: data\\processed\\phase5\\alignment_report.json")
        print("=" * 80)
        
        # Check if ready for Phase 6
        if summary['quality_gates']['passed']:
            print("\nReady to proceed to Phase 6: Note Candidate Window Detection")
        else:
            print("\nQuality gates not met - review results before proceeding")
        
        return 0
        
    except KeyboardInterrupt:
        print("\nPhase 5 processing interrupted by user")
        return 1
    
    except Exception as e:
        print(f"\nPhase 5 processing failed: {str(e)}")
        print("\nFull error traceback:")
        traceback.print_exc()
        
        print("\nTroubleshooting suggestions:")
        print("1. Check that Phase 1 audio data exists and is accessible")
        print("2. Verify TJA files are present in data/raw/ese/")
        print("3. Ensure sufficient disk space for output files")
        print("4. Check that librosa is properly installed")
        print("5. Review processing logs for detailed error information")
        
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
