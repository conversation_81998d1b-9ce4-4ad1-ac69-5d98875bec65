# Phase 2: Audio Quality Assessment & Filtering Configuration
# Windows-compatible paths with RTX 3070 memory management

# Input/Output Paths (Windows format)
paths:
  input_audio: "data\\processed\\phase1\\audio"
  input_metadata: "data\\processed\\phase1\\metadata"
  output_root: "data\\processed\\phase2"
  quality_metrics: "data\\processed\\phase2\\quality_metrics"
  filtered_audio: "data\\processed\\phase2\\filtered_audio"
  filtered_metadata: "data\\processed\\phase2\\filtered_metadata"
  logs_output: "data\\processed\\phase2\\processing_logs"

# Quality Assessment Parameters
quality_thresholds:
  min_snr_db: 15.0              # Minimum signal-to-noise ratio (dB)
  min_dynamic_range_db: 20.0    # Minimum dynamic range (dB)
  max_clipping_percentage: 1.0  # Maximum clipping percentage (%)
  max_silence_percentage: 10.0  # Maximum silence percentage (%)
  min_spectral_centroid: 500.0  # Minimum spectral centroid (Hz)
  max_spectral_centroid: 8000.0 # Maximum spectral centroid (Hz)
  min_zero_crossing_rate: 0.01  # Minimum zero crossing rate
  max_zero_crossing_rate: 0.5   # Maximum zero crossing rate
  min_quality_score: 0.7        # Minimum overall quality score (0-1)

# Feature Extraction Parameters
features:
  # Spectral analysis
  n_fft: 2048                   # FFT window size
  hop_length: 512               # Hop length for STFT
  n_mels: 80                    # Number of mel bands
  n_mfcc: 13                    # Number of MFCC coefficients
  
  # Quality analysis windows
  frame_length: 2048            # Frame length for analysis
  window_size_seconds: 0.1      # Analysis window size (seconds)
  overlap_ratio: 0.5            # Window overlap ratio
  
  # Noise estimation
  noise_floor_percentile: 5     # Percentile for noise floor estimation
  signal_percentile: 95         # Percentile for signal level estimation
  
# Memory Management (RTX 3070 constraints - Optimized for full dataset)
memory:
  max_ram_usage_gb: 6.5         # Maximum RAM usage (6.5GB limit for safety margin)
  max_files_in_memory: 15       # Maximum files to process simultaneously (reduced)
  chunk_size: 1024              # Audio chunk size for processing
  gc_frequency: 3               # Garbage collection frequency (every 3 batches for full dataset)

# Processing Parameters (Optimized for 2,878 files)
processing:
  max_workers: 2                # Parallel processing workers (reduced for large dataset)
  batch_size: 8                 # Files per batch (smaller for memory efficiency)
  validation_subset_size: 50    # Files for initial validation
  timeout_seconds: 60           # Timeout per file processing (longer for quality analysis)
  retry_attempts: 2             # Retry attempts for failed files
  
# Quality Gates
quality_gates:
  min_success_rate: 0.95        # Minimum 95% success rate
  min_quality_pass_rate: 0.90   # Minimum 90% quality pass rate
  min_filtered_files: 50        # Minimum number of files that must pass filtering
  
# Logging Configuration
logging:
  level: "INFO"                 # Logging level (DEBUG, INFO, WARNING, ERROR)
  format: "%(asctime)s - %(levelname)s - %(message)s"
  file_encoding: "utf-8"        # Unicode support for file paths
  max_log_size_mb: 100          # Maximum log file size
  backup_count: 5               # Number of backup log files
  
# Error Handling
error_handling:
  continue_on_error: true       # Continue processing if individual files fail
  save_error_details: true      # Save detailed error information
  create_error_report: true     # Generate error summary report
  
# Output Format
output:
  save_quality_metrics: true    # Save quality metrics as JSON
  save_filtered_files: true     # Save filtered audio files
  copy_metadata: true           # Copy metadata for filtered files
  compress_arrays: false        # Compress numpy arrays (slower but smaller)
  metadata_indent: 2            # JSON indentation for readability
  
# Validation Settings
validation:
  run_validation: true          # Run validation after processing
  validate_file_integrity: true # Check file integrity
  validate_quality_metrics: true # Check quality metrics validity
  generate_statistics: true     # Generate processing statistics
  create_quality_report: true   # Create comprehensive quality report

# Quality Score Weights (must sum to 1.0)
quality_weights:
  snr_weight: 0.25              # Signal-to-noise ratio importance
  dynamic_range_weight: 0.20    # Dynamic range importance
  clipping_weight: 0.15         # Clipping penalty importance
  silence_weight: 0.10          # Silence penalty importance
  spectral_weight: 0.15         # Spectral characteristics importance
  consistency_weight: 0.15      # Audio consistency importance
