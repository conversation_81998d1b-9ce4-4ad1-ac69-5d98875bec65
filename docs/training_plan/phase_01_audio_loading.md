# 🧩 Phase 1: Audio Loading & Format Standardization

**Status**: ✅ Planned  
**Estimated Duration**: 2 days  
**Dependencies**: None  
**Next Phase**: [Phase 2: Audio Quality Assessment](phase_02_quality_assessment.md)

---

## 1. **Phase Purpose**

This phase handles the most fundamental step: loading raw `.ogg` audio files and converting them into a standardized format suitable for all downstream processing. This step is isolated because:

- **Format inconsistencies** across the dataset can break later phases
- **Memory management** needs to be established early for large audio files
- **Error handling** for corrupted or unsupported files must be centralized
- **Metadata extraction** (sample rate, duration, channels) provides critical info for subsequent phases

**Why Isolated**: Audio loading seems simple but involves complex format handling, memory management, and error recovery that affects every subsequent phase. Standardizing this early prevents cascading failures.

---

## 2. **Inputs & Outputs**

### **Inputs**
```python
# File path structure
input_path: Path = "data\\raw\\ese\\[genre]\\[song_name]\\[song_name].ogg"

# Expected file characteristics (variable)
- Sample rates: 44100Hz, 48000Hz, or others
- Channels: mono or stereo
- Bit depth: 16-bit or 24-bit
- Duration: 1-6 minutes typically
- File sizes: 2-20MB per song
```

### **Outputs**
```python
# Standardized audio data
audio_data: np.ndarray  # Shape: (n_samples,) - mono, float32
sample_rate: int = 22050  # Standardized sample rate
duration: float  # Duration in seconds

# Audio metadata (standardized format for downstream phases)
audio_metadata: Dict = {
    "file_path": str,               # Full Windows path to original audio file
    "filename": str,                # Filename without extension
    "file_extension": str,          # File extension (.wav, .mp3, etc.)
    "original_sr": int,             # Original sample rate (Hz)
    "original_channels": int,       # Original channel count (1=mono, 2=stereo)
    "original_duration": float,     # Original duration in seconds
    "file_size_mb": float,          # File size in megabytes
    "standardized_sr": int,         # Standardized sample rate (22050)
    "standardized_channels": int,   # Standardized channels (1=mono)
    "conversion_applied": bool,     # Whether format conversion was needed
    "load_success": bool,           # Whether file loaded successfully
    "error_message": Optional[str], # Error message if loading failed
    "processing_time": float,       # Processing time in seconds
    "audio_format": str,            # Detected audio format
    "bit_depth": Optional[int],     # Original bit depth if available
    "encoding": Optional[str]       # Audio encoding type if available
}

# Output directory structure (Windows-compatible paths)
data\\processed\\phase1\\
├── audio\\                         # Standardized audio files
│   ├── {filename}.npy             # Audio data as numpy array (float32)
│   └── ...
├── metadata\\                      # Audio metadata files
│   ├── {filename}.json             # Audio metadata in JSON format
│   └── ...
└── processing_logs\\               # Processing logs
    ├── phase1_processing.log       # Detailed processing log
    └── phase1_summary.json         # Processing summary statistics
```

---

## 3. **Implementation Plan**

### **Recommended Tools**
```python
import librosa
import soundfile as sf
import numpy as np
from pathlib import Path
import logging
import json
import time
import gc
from typing import Tuple, Optional, Dict
from tqdm import tqdm
```

### **Core Loading Function**
```python
def load_and_standardize_audio(file_path: Path, target_sr: int = 22050) -> Tuple[Optional[np.ndarray], Optional[int], Dict]:
    """
    Load audio file and convert to standardized format.
    
    Args:
        file_path: Path to .ogg audio file
        target_sr: Target sample rate for standardization
        
    Returns:
        (audio_data, sample_rate, metadata)
    """
    start_time = time.time()
    metadata = {
        "file_path": str(file_path),
        "load_success": False,
        "error_message": None,
        "processing_time": 0.0
    }
    
    try:
        # Get original file info first
        info = sf.info(file_path)
        metadata.update({
            "original_sr": info.samplerate,
            "original_channels": info.channels,
            "original_duration": info.duration,
            "file_size_mb": file_path.stat().st_size / (1024*1024)
        })
        
        # Skip files that are too long (>10 minutes)
        if info.duration > 600:
            raise ValueError(f"File too long: {info.duration:.1f}s")
        
        # Load with librosa (handles most formats, auto-converts to mono)
        audio, sr = librosa.load(
            file_path, 
            sr=target_sr,  # Resample to target
            mono=True,     # Convert to mono
            dtype=np.float32
        )
        
        # Validate loaded audio
        if len(audio) == 0:
            raise ValueError("Loaded audio is empty")
        
        # Check for clipping or unusual values
        if np.max(np.abs(audio)) > 1.0:
            logging.warning(f"Audio clipping detected in {file_path}")
        
        metadata.update({
            "conversion_applied": (info.samplerate != target_sr) or (info.channels > 1),
            "load_success": True,
            "final_duration": len(audio) / sr,
            "final_samples": len(audio)
        })
        
        metadata["processing_time"] = time.time() - start_time
        return audio, sr, metadata
        
    except Exception as e:
        metadata["error_message"] = str(e)
        metadata["processing_time"] = time.time() - start_time
        logging.error(f"Failed to load {file_path}: {e}")
        return None, None, metadata
```

### **Batch Processing Pipeline**
```python
def process_audio_dataset(
    data_root: Path = Path("data\\raw\\ese"),
    output_dir: Path = Path("data\\processed\\phase1"),
    target_sr: int = 22050,
    max_files: Optional[int] = None
) -> Dict:
    """Process entire dataset with progress tracking and error handling."""
    
    # Setup output directories
    (output_dir / "audio").mkdir(parents=True, exist_ok=True)
    (output_dir / "metadata").mkdir(parents=True, exist_ok=True)
    (output_dir / "logs").mkdir(parents=True, exist_ok=True)
    
    # Setup logging
    logging.basicConfig(
        filename=output_dir / "logs" / "phase1_processing.log",
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Discover all .ogg files
    audio_files = list(data_root.rglob("*.ogg"))
    if max_files:
        audio_files = audio_files[:max_files]
    
    logging.info(f"Found {len(audio_files)} audio files to process")
    
    # Process files with progress tracking
    results = {
        "successful": 0,
        "failed": 0,
        "total_files": len(audio_files),
        "failed_files": [],
        "processing_stats": []
    }
    
    for i, audio_file in enumerate(tqdm(audio_files, desc="Loading audio files")):
        try:
            # Load and standardize
            audio, sr, metadata = load_and_standardize_audio(audio_file, target_sr)
            
            if metadata["load_success"]:
                # Save standardized audio
                relative_path = audio_file.relative_to(data_root)
                output_name = str(relative_path).replace("\\", "_").replace(".ogg", "")
                
                np.save(output_dir / "audio" / f"{output_name}.npy", audio)
                
                # Save metadata
                with open(output_dir / "metadata" / f"{output_name}.json", 'w') as f:
                    json.dump(metadata, f, indent=2)
                
                results["successful"] += 1
            else:
                results["failed"] += 1
                results["failed_files"].append(str(audio_file))
            
            results["processing_stats"].append(metadata)
            
            # Memory cleanup every 50 files
            if (i + 1) % 50 == 0:
                gc.collect()
                
        except Exception as e:
            logging.error(f"Unexpected error processing {audio_file}: {e}")
            results["failed"] += 1
            results["failed_files"].append(str(audio_file))
    
    # Save final results
    with open(output_dir / "processing_results.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    logging.info(f"Processing complete: {results['successful']}/{results['total_files']} successful")
    return results
```

---

## 4. **Best Practices**

### **Memory Management**
- Use `dtype=np.float32` instead of float64 to halve memory usage
- Call `gc.collect()` after processing batches of files
- Monitor memory usage: target <8GB total for RTX 3070 compatibility
- Process in chunks of 50 files to prevent memory accumulation

### **Error Resilience**
- Never crash on single file failures - log and continue
- Validate file existence and permissions before attempting to load
- Set reasonable timeouts for very large files (>100MB)
- Implement graceful degradation for edge cases

### **Performance Optimization**
```python
# Efficient loading for known good files
audio, sr = librosa.load(path, sr=22050, mono=True, offset=0.0, duration=None)

# For problematic files, use soundfile first to check
try:
    info = sf.info(path)
    if info.duration > 600:  # Skip files longer than 10 minutes
        raise ValueError("File too long")
except:
    # Handle with librosa fallback
    pass
```

### **Windows Path Handling**
```python
# Use pathlib for cross-platform compatibility
from pathlib import Path

# Correct Windows path handling
audio_path = Path("data") / "raw" / "ese" / genre / song_name / f"{song_name}.ogg"

# Avoid string concatenation for paths
# WRONG: path = "data\\raw\\ese\\" + genre + "\\" + song
# RIGHT: path = Path("data") / "raw" / "ese" / genre / song
```

---

## 5. **Challenges & Pitfalls**

### **Format Variations**
- **Issue**: Some `.ogg` files may use unusual codecs or be corrupted
- **Symptoms**: `librosa.load()` fails with codec errors
- **Mitigation**: Try `soundfile.read()` as fallback if `librosa.load()` fails
- **Detection**: Log original format info to identify problematic patterns

### **Memory Overflow**
- **Issue**: Loading very long audio files (>10 minutes) can exhaust memory
- **Symptoms**: System becomes unresponsive, Python crashes with MemoryError
- **Mitigation**: Implement duration checking before loading, skip oversized files
- **Fallback**: Load in chunks for oversized files (future enhancement)

### **Sample Rate Inconsistencies**
- **Issue**: Original sample rates vary (44.1kHz, 48kHz, etc.)
- **Impact**: Timing calculations become inconsistent across files
- **Solution**: Always resample to 22050Hz for consistency
- **Validation**: Log conversion ratios to detect quality loss

### **Stereo vs Mono Handling**
- **Issue**: Some files are stereo, others mono
- **Impact**: Feature extraction expects consistent channel count
- **Solution**: Force mono conversion using `librosa.load(mono=True)`
- **Quality Check**: Compare energy levels between channels before conversion

### **File System Issues**
- **Issue**: Windows path length limits, special characters in filenames
- **Symptoms**: FileNotFoundError despite file existing
- **Mitigation**: Use pathlib, validate paths before processing
- **Fallback**: Skip files with problematic names, log for manual review

---

## 6. **Dependencies & Unlocks**

### **Prerequisites**
- Python environment with `librosa>=0.9.0`, `soundfile>=0.10.0`
- Sufficient disk space for dataset (~50GB estimated)
- Windows 10/11 with proper audio codec support
- Dataset structure: `data\\raw\\ese\\[genre]\\[song]\\[song].ogg`

### **What This Phase Unlocks**
- **Phase 2**: Consistent audio format enables reliable quality assessment
- **Phase 3**: Standardized sample rate allows accurate silence detection
- **Phase 4**: Uniform timing base enables beat position estimation
- **All Future Phases**: Uniform data format prevents downstream errors

### **Output Dependencies**
Other phases depend on these Phase 1 outputs:
- `data\\processed\\phase1\\audio\\*.npy` - Standardized audio arrays
- `data\\processed\\phase1\\metadata\\*.json` - File metadata and conversion info
- `data\\processed\\phase1\\processing_results.json` - Overall processing statistics

---

## 7. **Validation Strategy**

### **Unit Tests**
```python
def test_audio_loading():
    """Test basic audio loading functionality."""
    # Test with known good file
    test_file = Path("test_data\\sample.ogg")
    audio, sr, metadata = load_and_standardize_audio(test_file)
    
    assert audio is not None
    assert sr == 22050
    assert metadata["load_success"] == True
    assert audio.dtype == np.float32
    assert len(audio.shape) == 1  # Mono
    assert len(audio) > 0

def test_error_handling():
    """Test error handling for problematic files."""
    # Test with non-existent file
    audio, sr, metadata = load_and_standardize_audio(Path("nonexistent.ogg"))
    assert audio is None
    assert metadata["load_success"] == False
    assert metadata["error_message"] is not None

def test_format_standardization():
    """Test that different input formats are standardized correctly."""
    # Test files with different sample rates, channels
    # Verify all outputs have sr=22050, mono format
    pass
```

### **Quality Metrics**
- **Success Rate**: >99% of files should load successfully
- **Duration Accuracy**: Loaded duration should match original ±0.1 seconds
- **Memory Usage**: Peak memory <4GB during batch processing
- **Processing Speed**: >100 files per minute on RTX 3070 system
- **File Size Consistency**: Output files should be reasonable size (not empty, not huge)

### **Visual Validation**
```python
def visualize_loaded_audio(audio: np.ndarray, sr: int, title: str = "Loaded Audio"):
    """Plot waveform to verify loading quality."""
    import matplotlib.pyplot as plt
    
    time = np.linspace(0, len(audio)/sr, len(audio))
    plt.figure(figsize=(12, 4))
    plt.plot(time, audio)
    plt.title(f"{title} (SR: {sr}Hz, Duration: {len(audio)/sr:.1f}s)")
    plt.xlabel("Time (seconds)")
    plt.ylabel("Amplitude")
    plt.grid(True, alpha=0.3)
    plt.show()

def validate_dataset_loading(output_dir: Path):
    """Validate a sample of loaded files."""
    audio_files = list((output_dir / "audio").glob("*.npy"))
    sample_files = np.random.choice(audio_files, min(10, len(audio_files)), replace=False)
    
    for audio_file in sample_files:
        audio = np.load(audio_file)
        visualize_loaded_audio(audio, 22050, audio_file.stem)
```

### **Example Success Case**
```python
# Expected output for successful loading
audio, sr, metadata = load_and_standardize_audio(Path("data\\raw\\ese\\01 Pop\\Lemon\\Lemon.ogg"))

# Expected results:
# audio: np.array with shape (1,323,000,) for ~60 second song at 22050Hz, dtype=float32
# sr: 22050

# Expected metadata format (matching Phase 2 input requirements):
audio_metadata = {
    "file_path": "data\\raw\\ese\\01 Pop\\Lemon\\Lemon.ogg",
    "filename": "Lemon",
    "file_extension": ".ogg",
    "original_sr": 44100,
    "original_channels": 2,
    "original_duration": 60.0,
    "file_size_mb": 4.2,
    "standardized_sr": 22050,
    "standardized_channels": 1,
    "conversion_applied": True,
    "load_success": True,
    "error_message": None,
    "processing_time": 0.15,
    "audio_format": "ogg",
    "bit_depth": 16,
    "encoding": "Vorbis"
}

# Expected file outputs:
# data\\processed\\phase1\\audio\\Lemon.npy - Audio array (float32)
# data\\processed\\phase1\\metadata\\Lemon.json - Metadata dict
```

### **Failure Case Analysis**
```python
# Common failure patterns to test:
test_cases = [
    "corrupted_file.ogg",      # Corrupted audio data
    "empty_file.ogg",          # Zero-byte file
    "unsupported_codec.ogg",   # Unusual codec
    "very_long_file.ogg",      # >10 minute duration
    "special_chars_名前.ogg"    # Unicode filename
]

for test_case in test_cases:
    audio, sr, metadata = load_and_standardize_audio(Path(test_case))
    assert metadata["load_success"] == False
    assert metadata["error_message"] is not None
```

---

**Phase 1 Complete**. This phase establishes the foundation for all audio processing by ensuring every audio file is loaded into a consistent, standardized format.

**Next**: [Phase 2: Audio Quality Assessment & Filtering](phase_02_quality_assessment.md)
