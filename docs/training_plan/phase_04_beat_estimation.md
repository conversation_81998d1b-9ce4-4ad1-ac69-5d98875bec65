# 🧩 Phase 4: Beat Position Estimation

**Status**: 📋 Planned  
**Estimated Duration**: 3 days  
**Dependencies**: [Phase 1: Audio Loading](phase_01_audio_loading.md), [Phase 3: Silence Detection](phase_03_silence_detection.md)  
**Next Phase**: [Phase 5: Tempo Alignment](phase_05_tempo_alignment.md)

---

## 1. **Phase Purpose**

This phase detects beat positions in audio segments to establish the rhythmic foundation for TJA chart generation. This step is isolated because:

- **Beat detection** requires specialized signal processing algorithms
- **Temporal precision** is critical for accurate note placement
- **Multiple detection methods** need to be combined for robustness
- **Beat tracking** provides the timing grid for all subsequent note analysis

**Why Isolated**: Beat detection is computationally intensive and requires different expertise than silence detection. The beat positions form the temporal backbone that all other phases depend on.

---

## 2. **Inputs & Outputs**

### **Inputs**
```python
# From Phase 3 (exact format match)
silence_map: dict = {               # Silence detection results
    "silence_regions": [           # List of silence intervals
        {"start": float, "end": float, "duration": float, "confidence": float}
    ],
    "music_regions": [             # List of musical intervals
        {"start": float, "end": float, "duration": float, "energy": float}
    ],
    "silence_percentage": float,   # Total silence percentage
    "music_percentage": float,     # Total music percentage
    "segment_count": int,          # Number of musical segments
    "detection_params": dict       # Parameters used for detection
}

# Audio segments (matching Phase 3 output format)
audio_segments: List[dict] = [
    {
        "segment_id": int,
        "start_time": float,
        "end_time": float,
        "duration": float,
        "audio_data": np.ndarray,  # Segmented audio
        "energy_profile": np.ndarray,  # RMS energy over time
        "is_musical": bool
    }
]

# From TJA metadata (for validation)
bpm: float                          # Expected BPM from TJA file
offset: float                       # Timing offset in seconds

# Input directory structure (matching Phase 3 outputs)
data\\processed\\phase3\\
├── silence_maps\\*.json        # Silence detection results
├── audio_segments\\            # Segmented audio files
│   ├── {filename}_segment_{N}.npy  # Individual segments
│   └── {filename}_segments.json    # Segment metadata
├── energy_profiles\\*.npy      # RMS energy profiles
└── segmentation_report.json    # Overall segmentation statistics
```

### **Outputs**
```python
# Beat detection results
beat_positions: dict = {
    "beats": [                      # List of detected beats
        {
            "time": float,          # Beat time in seconds
            "confidence": float,    # Detection confidence (0-1)
            "strength": float,      # Beat strength/salience
            "beat_number": int,     # Sequential beat index
            "measure_position": float  # Position within measure (0-1)
        }
    ],
    "tempo": float,                 # Estimated tempo (BPM)
    "tempo_confidence": float,      # Tempo estimation confidence
    "beat_intervals": np.ndarray,   # Inter-beat intervals
    "detection_method": str,        # Algorithm used
    "segment_id": int              # Source segment ID
}

# Onset detection results
onset_positions: dict = {
    "onsets": [                     # List of detected onsets
        {
            "time": float,          # Onset time in seconds
            "strength": float,      # Onset strength
            "frequency": float,     # Dominant frequency at onset
            "onset_type": str       # "percussive", "harmonic", "mixed"
        }
    ],
    "onset_density": float,         # Onsets per second
    "detection_params": dict        # Parameters used
}

# Output directory structure
data\\processed\\phase4\\
├── beat_positions\\*.json          # Beat detection results
├── onset_positions\\*.json         # Onset detection results
├── tempo_analysis\\*.json          # Tempo estimation details
├── beat_visualizations\\*.png      # Visual validation plots
└── beat_tracking_report.json       # Overall statistics
```

---

## 3. **Implementation Plan**

### **Recommended Tools**
```python
import librosa
import numpy as np
import scipy.signal
from pathlib import Path
import json
import logging
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import matplotlib.pyplot as plt
from sklearn.cluster import DBSCAN
import madmom  # Advanced beat tracking library
```

### **Core Beat Detection Function**
```python
def detect_beats_multi_method(
    audio: np.ndarray,
    sr: int = 22050,
    expected_bpm: Optional[float] = None,
    method: str = "combined"
) -> Dict:
    """
    Detect beats using multiple methods and combine results.
    
    Args:
        audio: Input audio segment
        sr: Sample rate
        expected_bpm: Expected BPM for validation (from TJA)
        method: Detection method ("librosa", "madmom", "combined")
        
    Returns:
        Dictionary with beat positions and metadata
    """
    
    results = {
        "beats": [],
        "tempo": 0.0,
        "tempo_confidence": 0.0,
        "beat_intervals": np.array([]),
        "detection_method": method,
        "segment_duration": len(audio) / sr
    }
    
    try:
        if method in ["librosa", "combined"]:
            # Method 1: Librosa beat tracking
            tempo_librosa, beats_librosa = librosa.beat.beat_track(
                y=audio,
                sr=sr,
                hop_length=512,
                start_bpm=expected_bpm if expected_bpm else 120,
                tightness=100  # Higher values = more consistent tempo
            )
            
            # Convert beat frames to time
            beat_times_librosa = librosa.frames_to_time(beats_librosa, sr=sr, hop_length=512)
            
        if method in ["madmom", "combined"]:
            # Method 2: Madmom beat tracking (more robust)
            try:
                import madmom
                proc = madmom.features.beats.RNNBeatProcessor()
                act = proc(audio)
                beat_proc = madmom.features.beats.DBNBeatTrackingProcessor(fps=100)
                beat_times_madmom = beat_proc(act)
                
                # Estimate tempo from madmom beats
                if len(beat_times_madmom) > 1:
                    intervals = np.diff(beat_times_madmom)
                    tempo_madmom = 60.0 / np.median(intervals)
                else:
                    tempo_madmom = 120.0
                    
            except ImportError:
                logging.warning("Madmom not available, using librosa only")
                beat_times_madmom = np.array([])
                tempo_madmom = 120.0
        
        # Combine methods or use single method
        if method == "combined" and len(beat_times_madmom) > 0:
            # Combine beats from both methods
            all_beats = np.concatenate([beat_times_librosa, beat_times_madmom])
            
            # Cluster nearby beats to remove duplicates
            if len(all_beats) > 0:
                clustering = DBSCAN(eps=0.05, min_samples=1)  # 50ms tolerance
                clusters = clustering.fit_predict(all_beats.reshape(-1, 1))
                
                # Take median of each cluster
                final_beats = []
                for cluster_id in np.unique(clusters):
                    cluster_beats = all_beats[clusters == cluster_id]
                    final_beats.append(np.median(cluster_beats))
                
                beat_times = np.array(sorted(final_beats))
                
                # Use average tempo
                tempo = (tempo_librosa + tempo_madmom) / 2
            else:
                beat_times = beat_times_librosa
                tempo = tempo_librosa
                
        elif method == "madmom" and len(beat_times_madmom) > 0:
            beat_times = beat_times_madmom
            tempo = tempo_madmom
        else:
            beat_times = beat_times_librosa
            tempo = tempo_librosa
        
        # Calculate beat intervals and confidence
        if len(beat_times) > 1:
            beat_intervals = np.diff(beat_times)
            
            # Tempo confidence based on interval consistency
            interval_std = np.std(beat_intervals)
            tempo_confidence = max(0.0, 1.0 - (interval_std / np.mean(beat_intervals)))
            
            # Create beat list with metadata
            beats = []
            for i, beat_time in enumerate(beat_times):
                # Calculate beat strength using onset strength
                onset_strength = calculate_beat_strength(audio, sr, beat_time)
                
                # Calculate measure position (assuming 4/4 time)
                measure_position = (i % 4) / 4.0
                
                beats.append({
                    "time": float(beat_time),
                    "confidence": float(tempo_confidence),
                    "strength": float(onset_strength),
                    "beat_number": int(i),
                    "measure_position": float(measure_position)
                })
            
            results.update({
                "beats": beats,
                "tempo": float(tempo),
                "tempo_confidence": float(tempo_confidence),
                "beat_intervals": beat_intervals
            })
            
        else:
            logging.warning("No beats detected in audio segment")
            
    except Exception as e:
        logging.error(f"Beat detection failed: {e}")
        
    return results

def calculate_beat_strength(audio: np.ndarray, sr: int, beat_time: float, window: float = 0.05) -> float:
    """Calculate the strength of a beat at a specific time."""
    # Convert time to sample index
    beat_sample = int(beat_time * sr)
    window_samples = int(window * sr)
    
    # Extract window around beat
    start_idx = max(0, beat_sample - window_samples)
    end_idx = min(len(audio), beat_sample + window_samples)
    
    if end_idx > start_idx:
        beat_window = audio[start_idx:end_idx]
        # Use RMS energy as beat strength
        strength = np.sqrt(np.mean(beat_window ** 2))
        return min(1.0, strength * 10)  # Normalize to 0-1 range
    else:
        return 0.0
```

### **Onset Detection Function**
```python
def detect_onsets(
    audio: np.ndarray,
    sr: int = 22050,
    hop_length: int = 512,
    onset_threshold: float = 0.3
) -> Dict:
    """
    Detect onset positions using spectral analysis.
    
    Args:
        audio: Input audio segment
        sr: Sample rate
        hop_length: Hop length for analysis
        onset_threshold: Minimum onset strength
        
    Returns:
        Dictionary with onset positions and metadata
    """
    
    # Detect onsets using multiple methods
    onset_frames_spectral = librosa.onset.onset_detect(
        y=audio,
        sr=sr,
        hop_length=hop_length,
        units='frames',
        onset_envelope=librosa.onset.onset_strength(y=audio, sr=sr, hop_length=hop_length)
    )
    
    # Convert to time
    onset_times = librosa.frames_to_time(onset_frames_spectral, sr=sr, hop_length=hop_length)
    
    # Calculate onset strengths
    onset_envelope = librosa.onset.onset_strength(y=audio, sr=sr, hop_length=hop_length)
    
    onsets = []
    for i, onset_time in enumerate(onset_times):
        # Get onset strength
        frame_idx = librosa.time_to_frames(onset_time, sr=sr, hop_length=hop_length)
        if frame_idx < len(onset_envelope):
            strength = float(onset_envelope[frame_idx])
            
            # Classify onset type based on spectral characteristics
            onset_type = classify_onset_type(audio, sr, onset_time)
            
            # Get dominant frequency at onset
            dominant_freq = get_dominant_frequency(audio, sr, onset_time)
            
            if strength >= onset_threshold:
                onsets.append({
                    "time": float(onset_time),
                    "strength": strength,
                    "frequency": float(dominant_freq),
                    "onset_type": onset_type
                })
    
    return {
        "onsets": onsets,
        "onset_density": len(onsets) / (len(audio) / sr),
        "detection_params": {
            "hop_length": hop_length,
            "onset_threshold": onset_threshold
        }
    }

def classify_onset_type(audio: np.ndarray, sr: int, onset_time: float) -> str:
    """Classify onset as percussive, harmonic, or mixed."""
    # Simple classification based on spectral characteristics
    # This is a placeholder - could be enhanced with ML classification
    
    # Extract short window around onset
    onset_sample = int(onset_time * sr)
    window_size = int(0.05 * sr)  # 50ms window
    start_idx = max(0, onset_sample - window_size // 2)
    end_idx = min(len(audio), onset_sample + window_size // 2)
    
    if end_idx > start_idx:
        window = audio[start_idx:end_idx]
        
        # Calculate spectral features
        stft = librosa.stft(window, hop_length=256)
        magnitude = np.abs(stft)
        
        # Spectral centroid (brightness)
        spectral_centroid = np.mean(librosa.feature.spectral_centroid(S=magnitude))
        
        # Spectral rolloff
        spectral_rolloff = np.mean(librosa.feature.spectral_rolloff(S=magnitude))
        
        # Zero crossing rate
        zcr = np.mean(librosa.feature.zero_crossing_rate(window))
        
        # Simple heuristic classification
        if zcr > 0.1 and spectral_centroid > 3000:
            return "percussive"
        elif spectral_rolloff < 4000:
            return "harmonic"
        else:
            return "mixed"
    
    return "unknown"

def get_dominant_frequency(audio: np.ndarray, sr: int, onset_time: float) -> float:
    """Get dominant frequency at onset time."""
    onset_sample = int(onset_time * sr)
    window_size = int(0.05 * sr)  # 50ms window
    start_idx = max(0, onset_sample - window_size // 2)
    end_idx = min(len(audio), onset_sample + window_size // 2)
    
    if end_idx > start_idx:
        window = audio[start_idx:end_idx]
        
        # Use FFT to find dominant frequency
        fft = np.fft.fft(window)
        freqs = np.fft.fftfreq(len(window), 1/sr)
        
        # Find peak frequency (positive frequencies only)
        positive_freqs = freqs[:len(freqs)//2]
        positive_fft = np.abs(fft[:len(fft)//2])
        
        if len(positive_fft) > 0:
            peak_idx = np.argmax(positive_fft)
            return abs(positive_freqs[peak_idx])
    
    return 0.0
```

### **Batch Processing Pipeline**
```python
def process_beat_detection(
    input_dir: Path = Path("data\\processed\\phase3"),
    output_dir: Path = Path("data\\processed\\phase4"),
    detection_method: str = "combined",
    use_tja_bpm: bool = True
) -> Dict:
    """Process beat detection for entire dataset."""
    
    # Setup output directories
    (output_dir / "beat_positions").mkdir(parents=True, exist_ok=True)
    (output_dir / "onset_positions").mkdir(parents=True, exist_ok=True)
    (output_dir / "tempo_analysis").mkdir(parents=True, exist_ok=True)
    (output_dir / "beat_visualizations").mkdir(parents=True, exist_ok=True)
    
    # Find all audio segments
    segment_files = list((input_dir / "audio_segments").glob("*_segment_*.npy"))
    
    results = {
        "total_segments": len(segment_files),
        "processed_segments": 0,
        "failed_segments": 0,
        "avg_tempo": 0.0,
        "avg_beats_per_segment": 0.0,
        "tempo_accuracy": 0.0,  # Compared to TJA BPM
        "processing_errors": []
    }
    
    all_tempos = []
    all_beat_counts = []
    tempo_errors = []
    
    for segment_file in tqdm(segment_files, desc="Detecting beats"):
        try:
            # Load audio segment
            audio = np.load(segment_file)
            
            # Load segment metadata to get expected BPM
            song_name = segment_file.stem.split("_segment_")[0]
            expected_bpm = None
            
            if use_tja_bpm:
                # Try to load BPM from TJA metadata
                expected_bpm = load_bpm_from_tja(song_name)
            
            # Detect beats
            beat_results = detect_beats_multi_method(
                audio, 
                sr=22050, 
                expected_bpm=expected_bpm,
                method=detection_method
            )
            
            # Detect onsets
            onset_results = detect_onsets(audio, sr=22050)
            
            # Save results
            beat_file = output_dir / "beat_positions" / f"{segment_file.stem}.json"
            with open(beat_file, 'w') as f:
                json.dump(beat_results, f, indent=2, default=convert_numpy)
            
            onset_file = output_dir / "onset_positions" / f"{segment_file.stem}.json"
            with open(onset_file, 'w') as f:
                json.dump(onset_results, f, indent=2, default=convert_numpy)
            
            # Create visualization
            if len(beat_results["beats"]) > 0:
                visualize_beat_detection(
                    audio, 22050, beat_results, onset_results,
                    output_dir / "beat_visualizations" / f"{segment_file.stem}.png"
                )
            
            # Update statistics
            results["processed_segments"] += 1
            
            if beat_results["tempo"] > 0:
                all_tempos.append(beat_results["tempo"])
                all_beat_counts.append(len(beat_results["beats"]))
                
                # Calculate tempo accuracy if expected BPM available
                if expected_bpm and expected_bpm > 0:
                    tempo_error = abs(beat_results["tempo"] - expected_bpm) / expected_bpm
                    tempo_errors.append(tempo_error)
            
        except Exception as e:
            error_info = {"file": str(segment_file), "error": str(e)}
            results["processing_errors"].append(error_info)
            results["failed_segments"] += 1
            logging.error(f"Error processing {segment_file}: {e}")
    
    # Calculate final statistics
    if all_tempos:
        results["avg_tempo"] = float(np.mean(all_tempos))
        results["avg_beats_per_segment"] = float(np.mean(all_beat_counts))
        
    if tempo_errors:
        results["tempo_accuracy"] = float(1.0 - np.mean(tempo_errors))
    
    # Save results
    with open(output_dir / "beat_tracking_report.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    return results

def convert_numpy(obj):
    """Convert numpy types to JSON serializable types."""
    if isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    else:
        return obj

def load_bpm_from_tja(song_name: str) -> Optional[float]:
    """Load BPM from corresponding TJA file."""
    # This would load the BPM from the original TJA file
    # Implementation depends on TJA parser from references
    try:
        # Placeholder - implement based on TJA parser
        tja_path = Path("data\\raw\\ese") / "**" / f"{song_name}.tja"
        # Parse TJA and extract BPM
        return None  # Placeholder
    except:
        return None
```

---

## 4. **Best Practices**

### **Multi-Method Approach**
- Combine multiple beat detection algorithms for robustness
- Use librosa for speed, madmom for accuracy
- Validate results against expected BPM from TJA files
- Fall back to simpler methods if advanced ones fail

### **Temporal Precision**
- Use high temporal resolution (hop_length=512 or smaller)
- Apply sub-frame interpolation for precise beat timing
- Account for system latency and processing delays
- Validate timing accuracy against reference beats

### **Parameter Tuning**
```python
# Adaptive parameter selection based on audio characteristics
def select_detection_params(audio: np.ndarray, sr: int) -> Dict:
    """Select optimal parameters based on audio analysis."""
    # Analyze tempo range
    tempo_estimate = estimate_tempo_range(audio, sr)
    
    # Adjust parameters based on tempo
    if tempo_estimate < 80:  # Slow tempo
        return {"hop_length": 256, "tightness": 200}
    elif tempo_estimate > 160:  # Fast tempo
        return {"hop_length": 1024, "tightness": 50}
    else:  # Medium tempo
        return {"hop_length": 512, "tightness": 100}
```

### **Quality Control**
- Validate beat intervals for consistency
- Check tempo estimates against expected ranges
- Flag segments with poor beat detection confidence
- Implement manual review for problematic cases

---

## 5. **Challenges & Pitfalls**

### **Complex Rhythmic Patterns**
- **Issue**: Syncopated rhythms, polyrhythms, irregular meters
- **Example**: Jazz, progressive rock, world music
- **Mitigation**: Use multiple detection methods, longer analysis windows
- **Solution**: Implement rhythm pattern recognition

### **Tempo Changes**
- **Issue**: Songs with tempo variations, accelerando/ritardando
- **Symptoms**: Inconsistent beat intervals, poor tracking confidence
- **Mitigation**: Use adaptive tempo tracking, segment-based analysis
- **Solution**: Implement tempo change detection and handling

### **Weak Beat Patterns**
- **Issue**: Ambient music, sustained notes, minimal percussion
- **Example**: Drone music, some electronic genres
- **Mitigation**: Use harmonic change detection in addition to percussive onsets
- **Solution**: Multi-feature beat detection (spectral, harmonic, rhythmic)

### **False Beat Detection**
- **Issue**: Regular non-rhythmic patterns detected as beats
- **Example**: Tremolo, vibrato, mechanical noise
- **Mitigation**: Use musical knowledge constraints, validate against onset patterns
- **Solution**: Implement beat validation using multiple features

### **Phase Alignment**
- **Issue**: Detected beats may be off-phase from actual musical beats
- **Symptoms**: Beats detected between actual beat positions
- **Mitigation**: Use onset information to validate beat phases
- **Solution**: Implement phase correction using onset alignment

---

## 6. **Dependencies & Unlocks**

### **Prerequisites**
- **Phase 1 & 3 Complete**: Clean audio segments with timing information
- **Required Files**:
  - `data\\processed\\phase3\\audio_segments\\*_segment_*.npy`
  - `data\\processed\\phase3\\silence_maps\\*.json`
- **Optional**: TJA files for BPM validation
- **Libraries**: `librosa`, `madmom` (optional), `scipy`, `sklearn`

### **What This Phase Unlocks**
- **Phase 5**: Beat positions enable tempo alignment and BPM validation
- **Phase 6**: Rhythmic grid provides framework for note candidate detection
- **Phase 7**: Beat timing is essential for note type classification
- **All Training Phases**: Beat positions form the temporal foundation for chart generation

### **Output Dependencies**
Subsequent phases depend on these Phase 4 outputs:
- `data\\processed\\phase4\\beat_positions\\*.json` - Precise beat timing information
- `data\\processed\\phase4\\onset_positions\\*.json` - Onset detection for note placement
- `data\\processed\\phase4\\tempo_analysis\\*.json` - Tempo estimation and confidence

---

## 6.5. **Iterative Training Strategy**

### **Multi-Execution Framework for Beat Detection**

Beat position estimation benefits significantly from iterative training due to the complexity of rhythm patterns and the need for progressive learning from simple to complex musical structures.

#### **Iterative Training Architecture**
```python
class IterativeBeatEstimator:
    """
    Iterative training framework for beat position estimation.

    Features:
    - Progressive complexity curriculum learning
    - Semi-supervised pseudo-labeling
    - Adaptive retraining triggers
    - Multi-model ensemble approach
    """

    def __init__(self, config: Dict):
        self.config = config
        self.paths = TJAGenPaths()
        self.logger = TJAGenLogger(4)

        # Iteration management
        self.current_iteration = 0
        self.max_iterations = config.get("max_iterations", 5)
        self.improvement_threshold = config.get("improvement_threshold", 0.02)

        # Model ensemble
        self.models = {
            "cnn_crnn": None,      # CNN+CRNN for local patterns
            "attention": None,      # Attention-based for global context
            "ensemble": None       # Ensemble combiner
        }

        # Training data management
        self.training_data = {
            "labeled": [],         # High-confidence labeled data
            "pseudo_labeled": [],  # Pseudo-labeled data from previous iterations
            "unlabeled": [],       # Unlabeled data for semi-supervised learning
            "hard_examples": []    # Difficult cases for focused training
        }

        # Performance tracking
        self.iteration_metrics = []
        self.best_performance = 0.0

        # Setup iteration directories
        self._setup_iteration_directories()

    def _setup_iteration_directories(self):
        """Create directories for iterative training artifacts."""
        base_dir = self.paths.get_phase_output_dir(4)

        directories = [
            base_dir / "iterations",
            base_dir / "iterations" / "models",
            base_dir / "iterations" / "data",
            base_dir / "iterations" / "metrics",
            base_dir / "iterations" / "pseudo_labels",
            base_dir / "iterations" / "hard_examples"
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

    def execute_iterative_training(self, initial_data: Dict) -> Dict:
        """
        Execute complete iterative training pipeline.

        Args:
            initial_data: Initial labeled training data

        Returns:
            Final training results with iteration history
        """

        self.logger.info("🔄 Starting iterative beat estimation training")

        # Initialize with labeled data
        self.training_data["labeled"] = initial_data["labeled_beats"]
        self.training_data["unlabeled"] = initial_data.get("unlabeled_audio", [])

        iteration_results = []

        for iteration in range(self.max_iterations):
            self.current_iteration = iteration
            self.logger.info(f"🔄 Starting iteration {iteration + 1}/{self.max_iterations}")

            # Execute single iteration
            iteration_result = self._execute_single_iteration()
            iteration_results.append(iteration_result)

            # Check for convergence
            if self._check_convergence(iteration_result):
                self.logger.info(f"✅ Converged after {iteration + 1} iterations")
                break

            # Prepare data for next iteration
            self._prepare_next_iteration(iteration_result)

        # Generate final results
        final_results = self._generate_final_results(iteration_results)

        self.logger.info("🎉 Iterative training completed")
        return final_results

    def _execute_single_iteration(self) -> Dict:
        """Execute a single training iteration."""

        iteration_start_time = time.time()

        # 1. Curriculum Learning: Progressive Complexity
        curriculum_data = self._apply_curriculum_learning()

        # 2. Train models with current data
        model_results = self._train_iteration_models(curriculum_data)

        # 3. Generate pseudo-labels for unlabeled data
        pseudo_labels = self._generate_pseudo_labels()

        # 4. Identify hard examples for focused training
        hard_examples = self._identify_hard_examples(model_results)

        # 5. Evaluate iteration performance
        performance_metrics = self._evaluate_iteration_performance()

        # 6. Save iteration artifacts
        self._save_iteration_artifacts(model_results, pseudo_labels, hard_examples)

        iteration_time = time.time() - iteration_start_time

        return {
            "iteration": self.current_iteration,
            "training_time": iteration_time,
            "model_results": model_results,
            "pseudo_labels_generated": len(pseudo_labels),
            "hard_examples_found": len(hard_examples),
            "performance_metrics": performance_metrics,
            "data_statistics": self._get_data_statistics()
        }

    def _apply_curriculum_learning(self) -> Dict:
        """Apply curriculum learning strategy for progressive complexity."""

        # Define complexity levels for beat estimation
        complexity_levels = {
            "simple": {
                "tempo_range": [60, 120],      # Simple tempos
                "time_signatures": ["4/4"],    # Standard time signature
                "rhythm_complexity": "low",    # Simple rhythms
                "genre_filter": ["pop", "rock"] # Straightforward genres
            },
            "moderate": {
                "tempo_range": [50, 180],      # Wider tempo range
                "time_signatures": ["4/4", "3/4"], # Add 3/4 time
                "rhythm_complexity": "medium", # More complex rhythms
                "genre_filter": ["pop", "rock", "jazz", "classical"]
            },
            "complex": {
                "tempo_range": [40, 200],      # Full tempo range
                "time_signatures": ["4/4", "3/4", "6/8", "7/8"], # Complex signatures
                "rhythm_complexity": "high",   # Complex polyrhythms
                "genre_filter": "all"         # All genres
            }
        }

        # Determine current complexity level based on iteration
        if self.current_iteration < 2:
            level = "simple"
        elif self.current_iteration < 4:
            level = "moderate"
        else:
            level = "complex"

        current_complexity = complexity_levels[level]

        # Filter training data based on complexity
        filtered_data = self._filter_data_by_complexity(current_complexity)

        self.logger.info(f"📚 Curriculum level: {level} ({len(filtered_data)} samples)")

        return {
            "complexity_level": level,
            "complexity_config": current_complexity,
            "filtered_data": filtered_data,
            "data_count": len(filtered_data)
        }

    def _train_iteration_models(self, curriculum_data: Dict) -> Dict:
        """Train all models for current iteration."""

        model_results = {}

        # Combine labeled and pseudo-labeled data
        combined_data = (
            self.training_data["labeled"] +
            self.training_data["pseudo_labeled"] +
            self.training_data["hard_examples"]
        )

        # Apply curriculum filtering
        training_data = self._apply_curriculum_filter(combined_data, curriculum_data)

        # Train CNN+CRNN model
        self.logger.info("🧠 Training CNN+CRNN model")
        cnn_crnn_result = self._train_cnn_crnn_model(training_data)
        model_results["cnn_crnn"] = cnn_crnn_result

        # Train attention-based model
        self.logger.info("🧠 Training attention model")
        attention_result = self._train_attention_model(training_data)
        model_results["attention"] = attention_result

        # Train ensemble combiner
        self.logger.info("🧠 Training ensemble model")
        ensemble_result = self._train_ensemble_model(cnn_crnn_result, attention_result)
        model_results["ensemble"] = ensemble_result

        return model_results

    def _generate_pseudo_labels(self) -> List[Dict]:
        """Generate pseudo-labels for unlabeled data using current models."""

        pseudo_labels = []
        confidence_threshold = self.config.get("pseudo_label_confidence", 0.8)

        for unlabeled_sample in self.training_data["unlabeled"]:
            # Get predictions from all models
            cnn_crnn_pred = self.models["cnn_crnn"].predict(unlabeled_sample)
            attention_pred = self.models["attention"].predict(unlabeled_sample)
            ensemble_pred = self.models["ensemble"].predict([cnn_crnn_pred, attention_pred])

            # Check prediction confidence
            if ensemble_pred["confidence"] > confidence_threshold:
                # Check agreement between models
                agreement_score = self._calculate_model_agreement(
                    cnn_crnn_pred, attention_pred, ensemble_pred
                )

                if agreement_score > 0.7:  # High agreement threshold
                    pseudo_label = {
                        "audio_data": unlabeled_sample,
                        "predicted_beats": ensemble_pred["beats"],
                        "confidence": ensemble_pred["confidence"],
                        "agreement_score": agreement_score,
                        "iteration_generated": self.current_iteration
                    }
                    pseudo_labels.append(pseudo_label)

        self.logger.info(f"📋 Generated {len(pseudo_labels)} pseudo-labels")
        return pseudo_labels

    def _identify_hard_examples(self, model_results: Dict) -> List[Dict]:
        """Identify hard examples for focused training."""

        hard_examples = []

        # Analyze prediction errors
        for model_name, results in model_results.items():
            validation_errors = results.get("validation_errors", [])

            for error in validation_errors:
                if error["error_magnitude"] > self.config.get("hard_example_threshold", 0.1):
                    hard_example = {
                        "audio_data": error["sample"],
                        "true_beats": error["true_beats"],
                        "predicted_beats": error["predicted_beats"],
                        "error_type": error["error_type"],
                        "error_magnitude": error["error_magnitude"],
                        "model_source": model_name,
                        "iteration_identified": self.current_iteration
                    }
                    hard_examples.append(hard_example)

        # Remove duplicates and sort by error magnitude
        hard_examples = self._deduplicate_hard_examples(hard_examples)
        hard_examples.sort(key=lambda x: x["error_magnitude"], reverse=True)

        # Limit number of hard examples per iteration
        max_hard_examples = self.config.get("max_hard_examples_per_iteration", 100)
        hard_examples = hard_examples[:max_hard_examples]

        self.logger.info(f"🎯 Identified {len(hard_examples)} hard examples")
        return hard_examples

    def _check_convergence(self, iteration_result: Dict) -> bool:
        """Check if training has converged."""

        current_performance = iteration_result["performance_metrics"]["overall_accuracy"]

        # Check if this is the best performance so far
        if current_performance > self.best_performance:
            improvement = current_performance - self.best_performance
            self.best_performance = current_performance

            # Check if improvement is below threshold
            if improvement < self.improvement_threshold:
                self.logger.info(f"📈 Improvement {improvement:.4f} below threshold {self.improvement_threshold}")
                return True

        # Check if we've reached target performance
        target_accuracy = self.config.get("target_accuracy", 0.95)
        if current_performance >= target_accuracy:
            self.logger.info(f"🎯 Target accuracy {target_accuracy} reached")
            return True

        return False

# Retraining trigger configuration
ITERATIVE_TRAINING_CONFIG = {
    "max_iterations": 5,
    "improvement_threshold": 0.02,  # 2% improvement threshold
    "target_accuracy": 0.95,        # 95% target accuracy
    "pseudo_label_confidence": 0.8, # 80% confidence for pseudo-labels
    "hard_example_threshold": 0.1,  # 10% error threshold for hard examples
    "max_hard_examples_per_iteration": 100,

    # Retraining triggers
    "retraining_triggers": {
        "accuracy_drop": 0.05,      # Retrain if accuracy drops by 5%
        "new_data_threshold": 1000, # Retrain when 1000 new samples available
        "time_based": "weekly",     # Weekly retraining schedule
        "error_pattern_change": True # Retrain if error patterns change
    },

    # Model ensemble configuration
    "ensemble_config": {
        "cnn_crnn_weight": 0.4,     # CNN+CRNN model weight
        "attention_weight": 0.4,    # Attention model weight
        "ensemble_weight": 0.2      # Ensemble meta-learner weight
    },

    # Curriculum learning schedule
    "curriculum_schedule": {
        "simple_iterations": [0, 1],    # Iterations 0-1: simple patterns
        "moderate_iterations": [2, 3],  # Iterations 2-3: moderate complexity
        "complex_iterations": [4]       # Iteration 4+: full complexity
    }
}
```

#### **Multi-Execution Workflow**

```mermaid
graph TD
    A[Initial Labeled Data] --> B[Iteration 0: Simple Patterns]
    B --> C[Train CNN+CRNN Model]
    B --> D[Train Attention Model]
    C --> E[Train Ensemble Model]
    D --> E
    E --> F[Generate Pseudo-Labels]
    F --> G[Identify Hard Examples]
    G --> H{Convergence Check}
    H -->|No| I[Iteration N+1: Increased Complexity]
    H -->|Yes| J[Final Model Selection]
    I --> K[Update Training Data]
    K --> L[Apply Curriculum Learning]
    L --> C
    J --> M[Deploy Best Model]

    style A fill:#e1f5fe
    style J fill:#c8e6c9
    style M fill:#c8e6c9
```

#### **Performance Improvement Tracking**

```python
# Expected improvement targets per iteration
ITERATION_TARGETS = {
    "iteration_0": {
        "baseline_accuracy": 0.70,
        "target_accuracy": 0.80,
        "focus": "Simple 4/4 patterns, basic tempo detection"
    },
    "iteration_1": {
        "baseline_accuracy": 0.80,
        "target_accuracy": 0.85,
        "focus": "Tempo variations, simple syncopation"
    },
    "iteration_2": {
        "baseline_accuracy": 0.85,
        "target_accuracy": 0.90,
        "focus": "Complex time signatures, genre diversity"
    },
    "iteration_3": {
        "baseline_accuracy": 0.90,
        "target_accuracy": 0.93,
        "focus": "Polyrhythms, extreme tempos"
    },
    "iteration_4": {
        "baseline_accuracy": 0.93,
        "target_accuracy": 0.95,
        "focus": "Edge cases, hard examples refinement"
    }
}

# Quality gates for iterative training
ITERATIVE_QUALITY_GATES = {
    "min_iteration_improvement": 0.02,  # 2% minimum improvement per iteration
    "max_iterations_without_improvement": 2,  # Stop after 2 iterations without improvement
    "final_accuracy_threshold": 0.95,   # Final model must achieve 95% accuracy
    "pseudo_label_quality_threshold": 0.8,  # Pseudo-labels must have 80% confidence
    "hard_example_coverage": 0.9        # Must address 90% of identified hard examples
}
```

---

## 7. **Validation Strategy**

### **Unit Tests**
```python
def test_beat_detection():
    """Test beat detection on synthetic rhythmic audio."""
    sr = 22050
    bpm = 120
    duration = 8.0  # 8 seconds
    
    # Create synthetic audio with clear beats
    beat_interval = 60.0 / bpm
    t = np.linspace(0, duration, int(sr * duration))
    
    # Add beats every beat_interval seconds
    audio = np.zeros_like(t)
    for beat_time in np.arange(0, duration, beat_interval):
        beat_sample = int(beat_time * sr)
        if beat_sample < len(audio):
            # Add short percussive hit
            hit_duration = int(0.05 * sr)
            audio[beat_sample:beat_sample+hit_duration] = 0.5 * np.sin(2 * np.pi * 1000 * t[:hit_duration])
    
    # Detect beats
    results = detect_beats_multi_method(audio, sr, expected_bpm=bpm)
    
    # Validate results
    assert len(results["beats"]) >= 6  # Should detect most beats
    assert 110 <= results["tempo"] <= 130  # Tempo should be close to 120
    assert results["tempo_confidence"] > 0.7  # Should be confident

def test_onset_detection():
    """Test onset detection functionality."""
    sr = 22050
    # Create audio with clear onsets
    audio = create_test_audio_with_onsets(sr)
    
    results = detect_onsets(audio, sr)
    
    assert len(results["onsets"]) > 0
    assert all(onset["strength"] > 0 for onset in results["onsets"])
    assert results["onset_density"] > 0
```

### **Quality Metrics**
- **Beat Detection Accuracy**: >80% of beats within ±50ms of reference
- **Tempo Accuracy**: Estimated tempo within ±5 BPM of TJA reference
- **Detection Confidence**: Average confidence >0.7 for musical segments
- **Processing Speed**: >20 segments per minute on RTX 3070

### **Visual Validation**
```python
def visualize_beat_detection(
    audio: np.ndarray, 
    sr: int, 
    beat_results: Dict, 
    onset_results: Dict,
    output_path: Path
):
    """Create visualization of beat detection results."""
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 10))
    
    # Plot waveform with beats
    time = np.linspace(0, len(audio)/sr, len(audio))
    ax1.plot(time, audio, alpha=0.7, color='blue')
    
    # Mark detected beats
    for beat in beat_results["beats"]:
        ax1.axvline(beat["time"], color='red', alpha=0.8, linewidth=2)
    
    ax1.set_title(f'Audio Waveform with Detected Beats (Tempo: {beat_results["tempo"]:.1f} BPM)')
    ax1.set_xlabel('Time (seconds)')
    ax1.set_ylabel('Amplitude')
    
    # Plot onset strength
    onset_envelope = librosa.onset.onset_strength(y=audio, sr=sr)
    onset_times = librosa.frames_to_time(np.arange(len(onset_envelope)), sr=sr)
    
    ax2.plot(onset_times, onset_envelope, color='green', linewidth=2)
    
    # Mark detected onsets
    for onset in onset_results["onsets"]:
        ax2.axvline(onset["time"], color='orange', alpha=0.6)
    
    ax2.set_title('Onset Strength Function with Detected Onsets')
    ax2.set_xlabel('Time (seconds)')
    ax2.set_ylabel('Onset Strength')
    
    # Plot beat intervals
    if len(beat_results["beat_intervals"]) > 0:
        ax3.plot(beat_results["beat_intervals"], 'o-', color='purple')
        ax3.axhline(np.mean(beat_results["beat_intervals"]), color='red', linestyle='--', 
                   label=f'Mean: {np.mean(beat_results["beat_intervals"]):.3f}s')
        ax3.set_title('Beat Intervals')
        ax3.set_xlabel('Beat Number')
        ax3.set_ylabel('Interval (seconds)')
        ax3.legend()
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
```

### **Example Success Case**
```python
# Expected output for typical song segment
beat_results = detect_beats_multi_method(typical_segment, 22050, expected_bpm=128)

# Expected results:
# {
#     "beats": [
#         {"time": 0.468, "confidence": 0.85, "strength": 0.72, "beat_number": 0, "measure_position": 0.0},
#         {"time": 0.937, "confidence": 0.85, "strength": 0.68, "beat_number": 1, "measure_position": 0.25},
#         {"time": 1.406, "confidence": 0.85, "strength": 0.75, "beat_number": 2, "measure_position": 0.5},
#         {"time": 1.875, "confidence": 0.85, "strength": 0.71, "beat_number": 3, "measure_position": 0.75}
#     ],
#     "tempo": 128.2,
#     "tempo_confidence": 0.85,
#     "beat_intervals": [0.469, 0.469, 0.469],
#     "detection_method": "combined",
#     "segment_duration": 8.0
# }
```

---

**Phase 4 Complete**. This phase establishes the rhythmic foundation by detecting precise beat positions and onsets, enabling accurate temporal alignment for note generation.

**Next**: [Phase 5: Tempo Alignment & BPM Validation](phase_05_tempo_alignment.md)
