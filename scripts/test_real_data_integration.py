"""
Real Data Integration Test for Phase 3 Models
Tests Phase 3 models with actual audio data from the dataset.
"""

import sys
import os
import torch
import numpy as np
import yaml
import librosa
from pathlib import Path
import time

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.phases.phase_03_model_architecture import TJAGenerator<PERSON><PERSON><PERSON>


def extract_mel_features(audio_data: np.ndarray, sample_rate: int = 22050, 
                        n_mels: int = 80, n_fft: int = 2048, hop_length: int = 512) -> np.ndarray:
    """Extract mel-spectrogram features from audio data."""
    
    # Compute mel-spectrogram
    mel_spec = librosa.feature.melspectrogram(
        y=audio_data,
        sr=sample_rate,
        n_mels=n_mels,
        n_fft=n_fft,
        hop_length=hop_length,
        power=2.0
    )
    
    # Convert to log scale
    log_mel = librosa.power_to_db(mel_spec, ref=np.max)
    
    # Transpose to (time, frequency) format
    return log_mel.T


def test_real_data_processing():
    """Test Phase 3 models with real audio data from the dataset."""
    
    print("🎵 TESTING REAL DATA INTEGRATION")
    print("=" * 60)
    
    # Load Phase 3 configuration
    config_path = "configs/phase_03_config.yaml"
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Get sample audio files
    phase2_audio_path = "data/processed/phase2/filtered_audio"
    audio_files = [f for f in os.listdir(phase2_audio_path) if f.endswith('.npy')][:5]  # Test with 5 files
    
    print(f"Testing with {len(audio_files)} real audio files...")
    
    # Test each model variant
    variants = config.get('variants', {})
    
    for variant_name, variant_config in variants.items():
        print(f"\n🔧 Testing {variant_name} model with real data...")
        
        try:
            # Create model configuration
            model_config = config.copy()
            arch_config = model_config['model_architecture']
            
            # Apply variant-specific changes
            if 'encoder_channels' in variant_config:
                arch_config['encoder']['channels'] = variant_config['encoder_channels']
            if 'decoder_channels' in variant_config:
                arch_config['decoder']['channels'] = variant_config['decoder_channels']
            if 'attention_heads' in variant_config:
                arch_config['attention']['num_heads'] = variant_config['attention_heads']
            if 'embed_dim' in variant_config:
                arch_config['attention']['embed_dim'] = variant_config['embed_dim']
            
            # Create model
            model = TJAGeneratorModel(model_config)
            model.eval()
            
            # Process each audio file
            successful_files = 0
            total_inference_time = 0
            
            for audio_file in audio_files:
                try:
                    # Load audio data
                    audio_path = os.path.join(phase2_audio_path, audio_file)
                    audio_data = np.load(audio_path)
                    
                    # Extract features
                    features = extract_mel_features(audio_data)
                    
                    # Prepare input for model (chunk to sequence length)
                    sequence_length = arch_config['input_features']['sequence_length']
                    
                    if features.shape[0] >= sequence_length:
                        # Take first sequence_length frames
                        input_features = features[:sequence_length, :]
                    else:
                        # Pad if too short
                        padding = sequence_length - features.shape[0]
                        input_features = np.pad(features, ((0, padding), (0, 0)), mode='constant')
                    
                    # Convert to tensor and add batch dimension
                    input_tensor = torch.FloatTensor(input_features).unsqueeze(0)
                    
                    # Model inference
                    start_time = time.time()
                    with torch.no_grad():
                        output = model(input_tensor)
                    inference_time = time.time() - start_time
                    total_inference_time += inference_time
                    
                    # Validate output
                    expected_shape = (1, arch_config['output']['sequence_length'], arch_config['output']['num_classes'])
                    
                    if output.shape == expected_shape:
                        successful_files += 1
                        
                        # Check output properties
                        output_np = output.squeeze(0).numpy()
                        max_prob = np.max(output_np, axis=1)
                        predicted_notes = np.argmax(output_np, axis=1)
                        
                        print(f"   ✅ {audio_file[:30]}... - Output: {output.shape}")
                        print(f"      Inference time: {inference_time*1000:.1f}ms")
                        print(f"      Max probability: {np.mean(max_prob):.3f}")
                        print(f"      Note distribution: {np.bincount(predicted_notes, minlength=5)}")
                    else:
                        print(f"   ❌ {audio_file[:30]}... - Wrong output shape: {output.shape}")
                        
                except Exception as e:
                    print(f"   ❌ {audio_file[:30]}... - Error: {str(e)}")
            
            # Summary for this variant
            success_rate = successful_files / len(audio_files)
            avg_inference_time = total_inference_time / len(audio_files) if len(audio_files) > 0 else 0
            
            print(f"\n   📊 {variant_name} Summary:")
            print(f"      Success rate: {success_rate:.1%} ({successful_files}/{len(audio_files)})")
            print(f"      Average inference time: {avg_inference_time*1000:.1f}ms")
            
            if success_rate >= 0.8:  # 80% success rate threshold
                print(f"      ✅ {variant_name} PASSED real data test")
            else:
                print(f"      ❌ {variant_name} FAILED real data test")
                
        except Exception as e:
            print(f"   ❌ {variant_name} model creation failed: {str(e)}")
    
    print("\n🎉 Real data integration testing completed!")


def test_feature_extraction_pipeline():
    """Test the feature extraction pipeline compatibility."""
    
    print("\n🔧 TESTING FEATURE EXTRACTION PIPELINE")
    print("=" * 60)
    
    # Load sample audio
    phase2_audio_path = "data/processed/phase2/filtered_audio"
    audio_files = [f for f in os.listdir(phase2_audio_path) if f.endswith('.npy')]
    
    if not audio_files:
        print("❌ No audio files found for testing")
        return
    
    sample_file = os.path.join(phase2_audio_path, audio_files[0])
    audio_data = np.load(sample_file)
    
    print(f"Testing with: {audio_files[0]}")
    print(f"Audio shape: {audio_data.shape}")
    print(f"Audio duration: {len(audio_data)/22050:.2f} seconds")
    
    # Test feature extraction
    try:
        features = extract_mel_features(audio_data)
        print(f"✅ Feature extraction successful")
        print(f"   Features shape: {features.shape}")
        print(f"   Feature range: [{features.min():.2f}, {features.max():.2f}]")
        
        # Test different sequence lengths
        sequence_lengths = [512, 1024, 2048]
        
        for seq_len in sequence_lengths:
            if features.shape[0] >= seq_len:
                chunked_features = features[:seq_len, :]
                print(f"   ✅ Sequence length {seq_len}: {chunked_features.shape}")
            else:
                padded_features = np.pad(features, ((0, seq_len - features.shape[0]), (0, 0)), mode='constant')
                print(f"   ✅ Sequence length {seq_len} (padded): {padded_features.shape}")
        
    except Exception as e:
        print(f"❌ Feature extraction failed: {str(e)}")


def main():
    """Main test execution."""
    
    print("🚀 STARTING REAL DATA INTEGRATION TESTS")
    print("=" * 80)
    
    try:
        test_feature_extraction_pipeline()
        test_real_data_processing()
        
        print("\n" + "=" * 80)
        print("✅ ALL REAL DATA INTEGRATION TESTS COMPLETED SUCCESSFULLY")
        print("🎯 Phase 3 models are ready to process real audio data!")
        print("=" * 80)
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Real data integration tests failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
