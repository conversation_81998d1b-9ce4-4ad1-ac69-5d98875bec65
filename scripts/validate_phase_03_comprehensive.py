"""
Comprehensive Phase 3 Validation Script
Validates all Phase 3 outputs and readiness for Phase 4 Training Pipeline.
"""

import sys
import os
import json
import yaml
import torch
import numpy as np
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Any
import time

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.phases.phase_03_model_architecture import TJ<PERSON><PERSON>atorModel, ModelArchitectureDesigner
from src.utils.model_utils import create_model_summary


class Phase3Validator:
    """Comprehensive validator for Phase 3 outputs."""
    
    def __init__(self):
        self.validation_results = {
            'dataset_coverage': {},
            'output_validation': {},
            'integration_testing': {},
            'readiness_assessment': {},
            'overall_status': 'PENDING'
        }
        
    def validate_dataset_coverage(self) -> Dict[str, Any]:
        """Validate dataset coverage and compatibility."""
        print("🔍 VALIDATING DATASET COVERAGE")
        print("=" * 60)
        
        results = {}
        
        # Check Phase 1 dataset
        phase1_audio_path = "data/processed/phase1/audio"
        phase1_metadata_path = "data/processed/phase1/metadata"
        
        if os.path.exists(phase1_audio_path):
            phase1_audio_files = [f for f in os.listdir(phase1_audio_path) if f.endswith('.npy')]
            results['phase1_audio_count'] = len(phase1_audio_files)
            print(f"✅ Phase 1 Audio Files: {len(phase1_audio_files)}")
        else:
            results['phase1_audio_count'] = 0
            print("❌ Phase 1 audio directory not found")
            
        if os.path.exists(phase1_metadata_path):
            phase1_metadata_files = [f for f in os.listdir(phase1_metadata_path) if f.endswith('.json')]
            results['phase1_metadata_count'] = len(phase1_metadata_files)
            print(f"✅ Phase 1 Metadata Files: {len(phase1_metadata_files)}")
        else:
            results['phase1_metadata_count'] = 0
            print("❌ Phase 1 metadata directory not found")
        
        # Check Phase 2 dataset
        phase2_audio_path = "data/processed/phase2/filtered_audio"
        phase2_quality_path = "data/processed/phase2/quality_metrics"
        
        if os.path.exists(phase2_audio_path):
            phase2_audio_files = [f for f in os.listdir(phase2_audio_path) if f.endswith('.npy')]
            results['phase2_audio_count'] = len(phase2_audio_files)
            print(f"✅ Phase 2 Filtered Audio Files: {len(phase2_audio_files)}")
        else:
            results['phase2_audio_count'] = 0
            print("❌ Phase 2 filtered audio directory not found")
            
        if os.path.exists(phase2_quality_path):
            phase2_quality_files = [f for f in os.listdir(phase2_quality_path) if f.endswith('.json')]
            results['phase2_quality_count'] = len(phase2_quality_files)
            print(f"✅ Phase 2 Quality Metrics Files: {len(phase2_quality_files)}")
        else:
            results['phase2_quality_count'] = 0
            print("❌ Phase 2 quality metrics directory not found")
        
        # Validate audio file compatibility
        if phase1_audio_files:
            sample_file = os.path.join(phase1_audio_path, phase1_audio_files[0])
            try:
                audio_data = np.load(sample_file)
                results['sample_audio_shape'] = audio_data.shape
                results['sample_duration'] = len(audio_data) / 22050
                results['sample_rate_compatible'] = True
                print(f"✅ Sample Audio Shape: {audio_data.shape}")
                print(f"✅ Sample Duration: {len(audio_data)/22050:.2f} seconds")
                print(f"✅ Sample Rate: 22,050 Hz (compatible)")
            except Exception as e:
                results['sample_rate_compatible'] = False
                print(f"❌ Audio file validation failed: {str(e)}")
        
        # Dataset consistency check
        results['dataset_consistent'] = (
            results.get('phase1_audio_count', 0) == results.get('phase1_metadata_count', 0) and
            results.get('phase2_audio_count', 0) == results.get('phase2_quality_count', 0)
        )
        
        if results['dataset_consistent']:
            print("✅ Dataset consistency validated")
        else:
            print("⚠️  Dataset inconsistency detected")
        
        # Calculate coverage metrics
        total_original = 2800  # From Phase 1 summary
        phase2_coverage = results.get('phase2_audio_count', 0) / total_original
        results['phase2_coverage_rate'] = phase2_coverage
        
        print(f"📊 Phase 2 Coverage: {phase2_coverage:.1%} ({results.get('phase2_audio_count', 0)}/{total_original})")
        
        return results
    
    def validate_phase3_outputs(self) -> Dict[str, Any]:
        """Validate Phase 3 output files and deliverables."""
        print("\n🔍 VALIDATING PHASE 3 OUTPUTS")
        print("=" * 60)
        
        results = {}
        
        # Check Phase 3 directory structure
        phase3_root = "data/processed/phase3"
        required_dirs = ['model_configs', 'architecture_specs', 'processing_logs']
        
        for dir_name in required_dirs:
            dir_path = os.path.join(phase3_root, dir_name)
            exists = os.path.exists(dir_path)
            results[f'{dir_name}_exists'] = exists
            
            if exists:
                files = os.listdir(dir_path)
                results[f'{dir_name}_files'] = files
                print(f"✅ {dir_name}: {len(files)} files")
            else:
                results[f'{dir_name}_files'] = []
                print(f"❌ {dir_name}: Directory not found")
        
        # Validate model configuration files
        expected_variants = ['lightweight', 'standard', 'enhanced']
        config_path = os.path.join(phase3_root, 'model_configs')
        
        if os.path.exists(config_path):
            for variant in expected_variants:
                config_file = f"{variant}_config.json"
                config_filepath = os.path.join(config_path, config_file)
                
                if os.path.exists(config_filepath):
                    try:
                        with open(config_filepath, 'r') as f:
                            config_data = json.load(f)
                        results[f'{variant}_config_valid'] = True
                        print(f"✅ {variant} config: Valid JSON")
                    except Exception as e:
                        results[f'{variant}_config_valid'] = False
                        print(f"❌ {variant} config: Invalid - {str(e)}")
                else:
                    results[f'{variant}_config_valid'] = False
                    print(f"❌ {variant} config: File not found")
        
        # Validate architecture specification files
        specs_path = os.path.join(phase3_root, 'architecture_specs')
        
        if os.path.exists(specs_path):
            for variant in expected_variants:
                spec_file = f"{variant}_specs.json"
                spec_filepath = os.path.join(specs_path, spec_file)
                
                if os.path.exists(spec_filepath):
                    try:
                        with open(spec_filepath, 'r') as f:
                            spec_data = json.load(f)
                        
                        # Validate required fields
                        required_fields = ['architecture_type', 'model_size', 'memory_usage', 'input_shape', 'output_shape']
                        all_fields_present = all(field in spec_data for field in required_fields)
                        
                        results[f'{variant}_specs_valid'] = all_fields_present
                        
                        if all_fields_present:
                            print(f"✅ {variant} specs: All required fields present")
                            print(f"   Parameters: {spec_data['model_size']['total_parameters']:,}")
                            print(f"   Training Memory: {spec_data['memory_usage']['total_training_memory_gb']:.3f} GB")
                        else:
                            print(f"❌ {variant} specs: Missing required fields")
                            
                    except Exception as e:
                        results[f'{variant}_specs_valid'] = False
                        print(f"❌ {variant} specs: Invalid - {str(e)}")
                else:
                    results[f'{variant}_specs_valid'] = False
                    print(f"❌ {variant} specs: File not found")
        
        # Validate processing logs
        logs_path = os.path.join(phase3_root, 'processing_logs')
        summary_file = os.path.join(logs_path, 'phase3_summary.json')
        
        if os.path.exists(summary_file):
            try:
                with open(summary_file, 'r') as f:
                    summary_data = json.load(f)
                
                results['summary_valid'] = True
                results['summary_data'] = summary_data
                
                # Check key metrics
                stats = summary_data.get('statistics', {})
                quality = summary_data.get('quality_metrics', {})
                
                print(f"✅ Summary report: Valid")
                print(f"   Success Rate: {stats.get('success_rate', 0):.1%}")
                print(f"   Quality Gates: {'✅ PASSED' if quality.get('meets_quality_gates', False) else '❌ FAILED'}")
                
            except Exception as e:
                results['summary_valid'] = False
                print(f"❌ Summary report: Invalid - {str(e)}")
        else:
            results['summary_valid'] = False
            print(f"❌ Summary report: File not found")
        
        return results
    
    def validate_integration(self) -> Dict[str, Any]:
        """Test integration between phases and model compatibility."""
        print("\n🔍 VALIDATING INTEGRATION")
        print("=" * 60)
        
        results = {}
        
        try:
            # Load Phase 3 configuration
            config_path = "configs/phase_03_config.yaml"
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            results['config_loaded'] = True
            print("✅ Phase 3 configuration loaded successfully")
            
            # Test model creation for each variant
            variants = config.get('variants', {})
            
            for variant_name, variant_config in variants.items():
                try:
                    # Update configuration with variant parameters
                    model_config = config.copy()
                    arch_config = model_config['model_architecture']
                    
                    # Apply variant-specific changes
                    if 'encoder_channels' in variant_config:
                        arch_config['encoder']['channels'] = variant_config['encoder_channels']
                    if 'decoder_channels' in variant_config:
                        arch_config['decoder']['channels'] = variant_config['decoder_channels']
                    if 'attention_heads' in variant_config:
                        arch_config['attention']['num_heads'] = variant_config['attention_heads']
                    if 'embed_dim' in variant_config:
                        arch_config['attention']['embed_dim'] = variant_config['embed_dim']
                    
                    # Create model
                    model = TJAGeneratorModel(model_config)
                    model.eval()
                    
                    # Test with realistic data dimensions
                    batch_size = model_config['memory']['batch_size']
                    sequence_length = arch_config['input_features']['sequence_length']
                    feature_dim = arch_config['input_features']['feature_dim']
                    
                    # Create sample input
                    sample_input = torch.randn(batch_size, sequence_length, feature_dim)
                    
                    # Forward pass
                    with torch.no_grad():
                        output = model(sample_input)
                    
                    # Validate output dimensions
                    expected_output_seq = arch_config['output']['sequence_length']
                    expected_classes = arch_config['output']['num_classes']
                    
                    output_valid = (
                        output.shape[0] == batch_size and
                        output.shape[1] == expected_output_seq and
                        output.shape[2] == expected_classes
                    )
                    
                    results[f'{variant_name}_integration'] = output_valid
                    
                    if output_valid:
                        print(f"✅ {variant_name}: Integration test passed")
                        print(f"   Input: {sample_input.shape} → Output: {output.shape}")
                    else:
                        print(f"❌ {variant_name}: Integration test failed")
                        print(f"   Expected output: ({batch_size}, {expected_output_seq}, {expected_classes})")
                        print(f"   Actual output: {output.shape}")
                    
                except Exception as e:
                    results[f'{variant_name}_integration'] = False
                    print(f"❌ {variant_name}: Integration test failed - {str(e)}")
            
        except Exception as e:
            results['config_loaded'] = False
            print(f"❌ Configuration loading failed: {str(e)}")
        
        # Test data pipeline compatibility
        try:
            # Load sample Phase 2 data
            phase2_path = "data/processed/phase2/filtered_audio"
            if os.path.exists(phase2_path):
                audio_files = [f for f in os.listdir(phase2_path) if f.endswith('.npy')]
                if audio_files:
                    sample_file = os.path.join(phase2_path, audio_files[0])
                    audio_data = np.load(sample_file)
                    
                    # Check if audio data can be processed by models
                    # (This would require feature extraction, but we can validate basic compatibility)
                    results['data_pipeline_compatible'] = True
                    print(f"✅ Data pipeline compatibility: Audio data loadable")
                    print(f"   Sample file: {audio_files[0]}")
                    print(f"   Audio length: {len(audio_data)} samples ({len(audio_data)/22050:.1f}s)")
                else:
                    results['data_pipeline_compatible'] = False
                    print("❌ Data pipeline compatibility: No audio files found")
            else:
                results['data_pipeline_compatible'] = False
                print("❌ Data pipeline compatibility: Phase 2 directory not found")
                
        except Exception as e:
            results['data_pipeline_compatible'] = False
            print(f"❌ Data pipeline compatibility test failed: {str(e)}")
        
        return results
    
    def assess_readiness(self, dataset_results: Dict, output_results: Dict, integration_results: Dict) -> Dict[str, Any]:
        """Assess readiness for Phase 4 implementation."""
        print("\n🔍 ASSESSING PHASE 4 READINESS")
        print("=" * 60)
        
        results = {}
        
        # Dataset readiness
        dataset_ready = (
            dataset_results.get('phase2_audio_count', 0) > 2000 and
            dataset_results.get('dataset_consistent', False) and
            dataset_results.get('sample_rate_compatible', False)
        )
        results['dataset_ready'] = dataset_ready
        
        if dataset_ready:
            print("✅ Dataset readiness: READY")
            print(f"   Available files: {dataset_results.get('phase2_audio_count', 0)}")
            print(f"   Coverage rate: {dataset_results.get('phase2_coverage_rate', 0):.1%}")
        else:
            print("❌ Dataset readiness: NOT READY")
        
        # Model architecture readiness
        variants_ready = all([
            output_results.get('lightweight_config_valid', False),
            output_results.get('standard_config_valid', False),
            output_results.get('enhanced_config_valid', False),
            output_results.get('lightweight_specs_valid', False),
            output_results.get('standard_specs_valid', False),
            output_results.get('enhanced_specs_valid', False)
        ])
        results['models_ready'] = variants_ready
        
        if variants_ready:
            print("✅ Model architectures: READY")
            print("   All 3 variants validated")
        else:
            print("❌ Model architectures: NOT READY")
        
        # Integration readiness
        integration_ready = (
            integration_results.get('config_loaded', False) and
            integration_results.get('data_pipeline_compatible', False) and
            all([
                integration_results.get('lightweight_integration', False),
                integration_results.get('standard_integration', False),
                integration_results.get('enhanced_integration', False)
            ])
        )
        results['integration_ready'] = integration_ready
        
        if integration_ready:
            print("✅ Integration: READY")
            print("   All models pass integration tests")
        else:
            print("❌ Integration: NOT READY")
        
        # Overall readiness
        overall_ready = dataset_ready and variants_ready and integration_ready
        results['overall_ready'] = overall_ready
        
        if overall_ready:
            print("\n🎉 PHASE 4 READINESS: ✅ READY TO PROCEED")
        else:
            print("\n⚠️  PHASE 4 READINESS: ❌ ISSUES NEED RESOLUTION")
        
        return results
    
    def run_comprehensive_validation(self) -> Dict[str, Any]:
        """Run complete validation suite."""
        print("🚀 STARTING COMPREHENSIVE PHASE 3 VALIDATION")
        print("=" * 80)
        
        start_time = time.time()
        
        # Run all validation steps
        dataset_results = self.validate_dataset_coverage()
        output_results = self.validate_phase3_outputs()
        integration_results = self.validate_integration()
        readiness_results = self.assess_readiness(dataset_results, output_results, integration_results)
        
        # Compile final results
        self.validation_results = {
            'dataset_coverage': dataset_results,
            'output_validation': output_results,
            'integration_testing': integration_results,
            'readiness_assessment': readiness_results,
            'validation_time': time.time() - start_time,
            'overall_status': 'READY' if readiness_results.get('overall_ready', False) else 'NOT_READY'
        }
        
        return self.validation_results


def main():
    """Main validation execution."""
    validator = Phase3Validator()
    results = validator.run_comprehensive_validation()
    
    # Save validation results
    results_path = "data/processed/phase3/processing_logs/comprehensive_validation_report.json"
    os.makedirs(os.path.dirname(results_path), exist_ok=True)
    
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n📄 Validation report saved to: {results_path}")
    
    # Final summary
    print("\n" + "=" * 80)
    print("COMPREHENSIVE VALIDATION SUMMARY")
    print("=" * 80)
    print(f"Overall Status: {results['overall_status']}")
    print(f"Validation Time: {results['validation_time']:.2f} seconds")
    
    if results['overall_status'] == 'READY':
        print("\n🎉 Phase 3 validation PASSED - Ready for Phase 4!")
        return 0
    else:
        print("\n⚠️  Phase 3 validation FAILED - Issues need resolution")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
