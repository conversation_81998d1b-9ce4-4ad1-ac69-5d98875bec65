"""
TJA-Audio Pairing Analysis Script
Analyzes the pairing between TJA files and audio files based on WAVE parameter.
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Set, Tuple
from collections import defaultdict

def parse_tja_wave_parameter(tja_path: Path) -> str:
    """Extract WAVE parameter from TJA file."""
    try:
        # Try different encodings
        encodings = ['utf-8-sig', 'utf-8', 'shift-jis', 'cp932']
        
        for encoding in encodings:
            try:
                with open(tja_path, 'r', encoding=encoding) as f:
                    content = f.read()
                break
            except UnicodeDecodeError:
                continue
        else:
            print(f"❌ Could not decode {tja_path.name}")
            return ""
        
        # Look for WAVE parameter (case insensitive)
        wave_match = re.search(r'^WAVE\s*:\s*(.+)$', content, re.MULTILINE | re.IGNORECASE)
        if wave_match:
            wave_value = wave_match.group(1).strip()
            # Remove quotes if present
            wave_value = wave_value.strip('"\'')
            return wave_value
        
        return ""
        
    except Exception as e:
        print(f"❌ Error parsing {tja_path.name}: {e}")
        return ""

def analyze_dataset_pairing():
    """Analyze TJA-audio pairing in the dataset."""
    print("🔍 TJA-Audio Pairing Analysis")
    print("=" * 50)
    
    dataset_root = Path("data/raw/ese")
    
    # Collect all files
    print("📁 Scanning dataset...")
    ogg_files = list(dataset_root.rglob("*.ogg"))
    tja_files = list(dataset_root.rglob("*.tja"))
    
    print(f"Found {len(ogg_files)} OGG files")
    print(f"Found {len(tja_files)} TJA files")
    print(f"Difference: {len(ogg_files) - len(tja_files)} orphaned OGG files")
    
    # Create filename-based mappings
    ogg_by_name = {}
    ogg_by_stem = {}
    
    for ogg_file in ogg_files:
        ogg_by_name[ogg_file.name] = ogg_file
        ogg_by_stem[ogg_file.stem] = ogg_file
    
    # Analyze TJA files and their WAVE parameters
    print("\n🎵 Analyzing TJA WAVE parameters...")
    
    wave_analysis = {
        'valid_pairs': [],
        'filename_matches': [],
        'wave_matches': [],
        'orphaned_tja': [],
        'wave_parse_errors': [],
        'filename_vs_wave_mismatches': []
    }
    
    for i, tja_file in enumerate(tja_files, 1):
        if i % 100 == 0:
            print(f"  Processed {i}/{len(tja_files)} TJA files...")
        
        # Get WAVE parameter
        wave_param = parse_tja_wave_parameter(tja_file)
        
        if not wave_param:
            wave_analysis['wave_parse_errors'].append({
                'tja_file': str(tja_file),
                'error': 'No WAVE parameter found'
            })
            continue
        
        # Check filename-based matching
        tja_stem = tja_file.stem
        filename_match = tja_stem in ogg_by_stem
        
        # Check WAVE parameter matching
        wave_match = wave_param in ogg_by_name
        
        if filename_match and wave_match:
            # Both match - ideal case
            if tja_stem + '.ogg' == wave_param:
                wave_analysis['valid_pairs'].append({
                    'tja_file': str(tja_file),
                    'ogg_file': str(ogg_by_stem[tja_stem]),
                    'wave_param': wave_param,
                    'match_type': 'perfect'
                })
            else:
                wave_analysis['filename_vs_wave_mismatches'].append({
                    'tja_file': str(tja_file),
                    'filename_ogg': str(ogg_by_stem[tja_stem]),
                    'wave_ogg': str(ogg_by_name[wave_param]),
                    'wave_param': wave_param
                })
        elif filename_match:
            # Only filename matches
            wave_analysis['filename_matches'].append({
                'tja_file': str(tja_file),
                'ogg_file': str(ogg_by_stem[tja_stem]),
                'wave_param': wave_param,
                'wave_exists': wave_param in ogg_by_name
            })
        elif wave_match:
            # Only WAVE parameter matches
            wave_analysis['wave_matches'].append({
                'tja_file': str(tja_file),
                'ogg_file': str(ogg_by_name[wave_param]),
                'wave_param': wave_param,
                'filename_stem': tja_stem
            })
        else:
            # No matches - orphaned TJA
            wave_analysis['orphaned_tja'].append({
                'tja_file': str(tja_file),
                'wave_param': wave_param,
                'filename_stem': tja_stem
            })
    
    # Find orphaned OGG files
    paired_ogg_files = set()
    
    for category in ['valid_pairs', 'filename_matches', 'wave_matches']:
        for item in wave_analysis[category]:
            if 'ogg_file' in item:
                paired_ogg_files.add(item['ogg_file'])
    
    for item in wave_analysis['filename_vs_wave_mismatches']:
        paired_ogg_files.add(item['filename_ogg'])
        paired_ogg_files.add(item['wave_ogg'])
    
    orphaned_ogg = []
    for ogg_file in ogg_files:
        if str(ogg_file) not in paired_ogg_files:
            orphaned_ogg.append(str(ogg_file))
    
    # Generate report
    print(f"\n📊 Pairing Analysis Results:")
    print(f"  Perfect pairs (filename = WAVE): {len(wave_analysis['valid_pairs'])}")
    print(f"  Filename-only matches: {len(wave_analysis['filename_matches'])}")
    print(f"  WAVE-only matches: {len(wave_analysis['wave_matches'])}")
    print(f"  Filename vs WAVE mismatches: {len(wave_analysis['filename_vs_wave_mismatches'])}")
    print(f"  Orphaned TJA files: {len(wave_analysis['orphaned_tja'])}")
    print(f"  Orphaned OGG files: {len(orphaned_ogg)}")
    print(f"  WAVE parse errors: {len(wave_analysis['wave_parse_errors'])}")
    
    # Calculate potential success rate improvement
    current_processed = 2712  # From Phase 1
    
    # Files that could be processed with correct pairing logic
    processable_with_wave = (
        len(wave_analysis['valid_pairs']) +
        len(wave_analysis['filename_matches']) +
        len(wave_analysis['wave_matches']) +
        len(wave_analysis['filename_vs_wave_mismatches'])
    )
    
    potential_success_rate = processable_with_wave / len(ogg_files) * 100
    
    print(f"\n🎯 Success Rate Analysis:")
    print(f"  Current success rate: {current_processed}/{len(ogg_files)} ({current_processed/len(ogg_files)*100:.2f}%)")
    print(f"  Potential with correct pairing: {processable_with_wave}/{len(ogg_files)} ({potential_success_rate:.2f}%)")
    print(f"  Improvement potential: {potential_success_rate - (current_processed/len(ogg_files)*100):.2f} percentage points")
    
    # Save detailed analysis
    analysis_output = {
        'summary': {
            'total_ogg_files': len(ogg_files),
            'total_tja_files': len(tja_files),
            'current_processed': current_processed,
            'current_success_rate': current_processed/len(ogg_files)*100,
            'potential_processable': processable_with_wave,
            'potential_success_rate': potential_success_rate,
            'improvement_potential': potential_success_rate - (current_processed/len(ogg_files)*100)
        },
        'pairing_analysis': wave_analysis,
        'orphaned_ogg_files': orphaned_ogg
    }
    
    output_path = Path("data/processed/tja_audio_pairing_analysis.json")
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(analysis_output, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 Detailed analysis saved to: {output_path}")
    
    # Show examples of mismatches
    if wave_analysis['filename_vs_wave_mismatches']:
        print(f"\n🔍 Examples of filename vs WAVE mismatches:")
        for i, mismatch in enumerate(wave_analysis['filename_vs_wave_mismatches'][:5], 1):
            tja_name = Path(mismatch['tja_file']).name
            wave_param = mismatch['wave_param']
            print(f"  {i}. TJA: {tja_name} → WAVE: {wave_param}")
    
    return analysis_output

if __name__ == "__main__":
    analysis = analyze_dataset_pairing()
