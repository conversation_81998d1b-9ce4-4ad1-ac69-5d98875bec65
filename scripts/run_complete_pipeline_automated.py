"""
Automated Complete TJA Chart Generation Pipeline Execution
Runs Phase 1 and Phase 2 sequentially without user confirmation prompts.
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def run_phase_1():
    """Execute Phase 1 with corrected TJA-WAVE pairing."""
    print("🎯 Phase 1: Audio Loading & Format Standardization")
    print("=" * 60)
    print("🤖 Automated execution - no user prompts")
    
    try:
        # Add src to Python path
        src_path = Path(__file__).parent.parent / "src"
        sys.path.insert(0, str(src_path))
        
        from phases.phase_01_audio_loading import Phase1AudioProcessor
        from utils.tja_pairing_utils import get_audio_files_with_tja_pairing
        
        # Initialize processor
        processor = Phase1AudioProcessor()
        
        # Get properly paired audio files using corrected logic
        print("📁 Scanning for properly paired TJA-audio files...")
        input_root = Path(processor.config['paths']['input_root'])
        supported_formats = processor.config['audio']['supported_formats']
        
        # Use corrected pairing logic
        audio_files = get_audio_files_with_tja_pairing(input_root, supported_formats)
        total_files = len(audio_files)
        
        print(f"Found {total_files} properly paired audio files")
        
        # Check for existing processed files to enable resume
        audio_output_dir = Path(processor.config['paths']['audio_output'])
        existing_files = set()
        if audio_output_dir.exists():
            existing_files = {f.stem for f in audio_output_dir.glob("*.npy")}
            print(f"Found {len(existing_files)} already processed files")
        
        # Filter out already processed files
        remaining_files = [f for f in audio_files if f.stem not in existing_files]
        print(f"Remaining files to process: {len(remaining_files)}")
        
        if not remaining_files:
            print("✅ All properly paired files already processed!")
            
            # Calculate final success rate
            final_success_rate = len(existing_files) / total_files if total_files > 0 else 0
            print(f"📊 Final Success Rate: {len(existing_files)}/{total_files} ({final_success_rate:.2%})")
            
            # Check quality gate
            min_success_rate = processor.config['quality']['min_success_rate']
            if final_success_rate >= min_success_rate:
                print(f"✅ Quality gate passed: {final_success_rate:.1%} >= {min_success_rate:.1%}")
                return True, len(existing_files), total_files
            else:
                print(f"❌ Quality gate failed: {final_success_rate:.1%} < {min_success_rate:.1%}")
                return False, len(existing_files), total_files
        
        # Process remaining files automatically
        print(f"🚀 Processing {len(remaining_files)} remaining files automatically...")
        
        chunk_size = 100
        chunks = [remaining_files[i:i + chunk_size] for i in range(0, len(remaining_files), chunk_size)]
        
        total_successful = len(existing_files)
        total_failed = 0
        
        for chunk_idx, chunk in enumerate(chunks, 1):
            print(f"\n📦 Processing chunk {chunk_idx}/{len(chunks)} ({len(chunk)} files)...")
            
            chunk_successful = 0
            chunk_failed = 0
            
            for file_idx, file_path in enumerate(chunk, 1):
                try:
                    success, metadata = processor.process_single_file(file_path)
                    
                    if success:
                        chunk_successful += 1
                        total_successful += 1
                    else:
                        chunk_failed += 1
                        total_failed += 1
                        error_msg = metadata.get('error_message', 'Unknown error')
                        processor.stats['errors'].append({
                            'file': str(file_path),
                            'error': error_msg
                        })
                    
                    # Update statistics
                    processor.stats['processed_files'] = total_successful + total_failed
                    processor.stats['successful_files'] = total_successful
                    processor.stats['failed_files'] = total_failed
                    
                    # Periodic memory cleanup
                    if file_idx % 10 == 0:
                        processor.memory_manager.force_garbage_collection()
                        
                except Exception as e:
                    chunk_failed += 1
                    total_failed += 1
                    processor.stats['errors'].append({
                        'file': str(file_path),
                        'error': f"Unexpected error: {str(e)}"
                    })
            
            # Chunk summary
            success_rate = total_successful / (total_successful + total_failed) if (total_successful + total_failed) > 0 else 0
            print(f"Chunk {chunk_idx} complete: {chunk_successful}/{len(chunk)} successful")
            print(f"Overall success rate: {success_rate:.1%}")
        
        # Final statistics
        final_success_rate = total_successful / total_files if total_files > 0 else 0
        
        print(f"\n📊 Phase 1 Final Results:")
        print(f"   Total properly paired files: {total_files}")
        print(f"   Successful: {total_successful} ({final_success_rate:.1%})")
        print(f"   Failed: {total_failed}")
        
        # Save final summary
        processor.stats['total_files'] = total_files
        processor.stats['pairing_method'] = 'TJA_WAVE_parameter'
        processor.stats['corrected_pairing'] = True
        processor.save_processing_summary()
        
        # Check quality gates
        min_success_rate = processor.config['quality']['min_success_rate']
        if final_success_rate >= min_success_rate:
            print(f"✅ Quality gate passed: {final_success_rate:.1%} >= {min_success_rate:.1%}")
            return True, total_successful, total_files
        else:
            print(f"❌ Quality gate failed: {final_success_rate:.1%} < {min_success_rate:.1%}")
            return False, total_successful, total_files
            
    except Exception as e:
        print(f"❌ Phase 1 error: {e}")
        return False, 0, 0

def run_phase_2():
    """Execute Phase 2 automated quality assessment."""
    print("\n🎯 Phase 2: Audio Quality Assessment & Filtering")
    print("=" * 60)
    print("🤖 Automated execution - no user prompts")
    
    try:
        # Add src to Python path
        src_path = Path(__file__).parent.parent / "src"
        sys.path.insert(0, str(src_path))
        
        from phases.phase_02_quality_assessment import Phase2QualityProcessor
        
        # Initialize processor
        processor = Phase2QualityProcessor()
        
        # Run validation first
        print("📋 Running validation with subset...")
        validation_stats = processor.process_all_files(validation_mode=True)
        
        validation_success_rate = validation_stats['successful_files'] / max(validation_stats['total_files'], 1)
        validation_quality_rate = validation_stats['quality_pass_rate']
        
        print(f"Validation Results: {validation_stats['successful_files']}/{validation_stats['total_files']} "
              f"files successful ({validation_success_rate:.1%})")
        print(f"Quality Results: {validation_stats['quality_passed_files']}/{validation_stats['successful_files']} "
              f"files passed quality ({validation_quality_rate:.1%})")
        
        if validation_success_rate < 0.8:
            print("❌ Validation failed. Stopping automated execution.")
            return False, 0, 0, 0
        
        if validation_quality_rate < 0.5:
            print("❌ Quality assessment validation failed. Stopping automated execution.")
            return False, 0, 0, 0
        
        # Proceed automatically with full dataset
        print("✅ Validation passed. Proceeding automatically with full dataset...")
        
        # Process full dataset
        print("🚀 Processing full dataset automatically...")
        final_stats = processor.process_all_files(validation_mode=False)
        
        # Save summary
        processor.save_processing_summary()
        
        # Create quality report
        quality_report = processor.create_quality_report()
        
        # Validate outputs
        validation_results = processor.validate_outputs()
        
        # Final results
        success_rate = final_stats['successful_files'] / max(final_stats['total_files'], 1)
        quality_pass_rate = final_stats['quality_pass_rate']
        
        print(f"\n📊 Phase 2 Final Results:")
        print(f"   Total files: {final_stats['total_files']}")
        print(f"   Successful: {final_stats['successful_files']} ({success_rate:.1%})")
        print(f"   Failed: {final_stats['failed_files']}")
        print(f"   Quality passed: {final_stats['quality_passed_files']} ({quality_pass_rate:.1%})")
        print(f"   Quality failed: {final_stats['quality_failed_files']}")
        print(f"   Processing time: {final_stats['total_processing_time']:.1f}s")
        
        # Check quality gates
        min_success_rate = processor.config['quality_gates']['min_success_rate']
        min_quality_pass_rate = processor.config['quality_gates']['min_quality_pass_rate']
        
        gates_passed = 0
        total_gates = 2
        
        if success_rate >= min_success_rate:
            print(f"✅ Success rate gate passed: {success_rate:.1%} >= {min_success_rate:.1%}")
            gates_passed += 1
        else:
            print(f"❌ Success rate gate failed: {success_rate:.1%} < {min_success_rate:.1%}")
        
        if quality_pass_rate >= min_quality_pass_rate:
            print(f"✅ Quality pass rate gate passed: {quality_pass_rate:.1%} >= {min_quality_pass_rate:.1%}")
            gates_passed += 1
        else:
            print(f"❌ Quality pass rate gate failed: {quality_pass_rate:.1%} < {min_quality_pass_rate:.1%}")
        
        success = gates_passed == total_gates
        
        return success, final_stats['successful_files'], final_stats['quality_passed_files'], final_stats['total_files']
        
    except Exception as e:
        print(f"❌ Phase 2 error: {e}")
        return False, 0, 0, 0

def main():
    """Main automated pipeline execution."""
    print("🎼 TJA Chart Generation - Complete Automated Pipeline")
    print("=" * 70)
    print("🤖 Fully automated execution - no user confirmation prompts")
    print("📋 Quality gates will be checked automatically")
    
    start_time = time.time()
    
    # Execute Phase 1
    phase1_success, phase1_processed, phase1_total = run_phase_1()
    
    if not phase1_success:
        print("\n❌ Pipeline stopped: Phase 1 failed quality gates")
        return False
    
    print(f"\n✅ Phase 1 completed successfully: {phase1_processed}/{phase1_total} files")
    
    # Execute Phase 2
    phase2_success, phase2_processed, phase2_quality_passed, phase2_total = run_phase_2()
    
    if not phase2_success:
        print("\n❌ Pipeline stopped: Phase 2 failed quality gates")
        return False
    
    # Final pipeline summary
    total_time = time.time() - start_time
    
    print(f"\n🎉 Complete Pipeline Execution Summary")
    print("=" * 50)
    print(f"📊 Phase 1: {phase1_processed}/{phase1_total} files processed")
    print(f"📊 Phase 2: {phase2_processed}/{phase2_total} files processed")
    print(f"📊 High-quality files: {phase2_quality_passed}")
    print(f"⏱️  Total execution time: {total_time:.1f} seconds")
    print(f"✅ All quality gates passed - pipeline completed successfully!")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
