# 🧩 Phase 11: Go-Go Time & Special Sections

**Status**: 📋 Planned  
**Estimated Duration**: 3 days  
**Dependencies**: [Phase 10: Measure Segmentation & Bar Lines](phase_10_measure_segmentation.md)  
**Next Phase**: [Phase 12: TJA Header Generation](phase_12_tja_headers.md)

---

## 1. **Phase Purpose**

This phase identifies and generates special sections like Go-Go Time, scroll speed changes, and BPM changes for enhanced chart dynamics. This step is isolated because:

- **Special section detection** requires analysis of musical intensity and structure
- **Go-Go Time placement** follows specific musical and gameplay conventions
- **Dynamic effects** enhance player engagement and chart variety
- **TJA command generation** requires precise timing and formatting

**Why Isolated**: Special sections require different analysis techniques than basic note generation, involving musical structure analysis and gameplay design principles. These sections significantly impact the player experience and chart quality.

---

## 2. **Inputs & Outputs**

### **Inputs**
```python
# From Phase 10
measure_segments: List[Dict]        # Segmented measures with timing
bar_lines: List[Dict]               # Bar line positions
measure_validation: Dict            # Measure quality metrics

# From Phase 9
difficulty_patterns: List[Dict]     # Patterns with complexity analysis
complexity_analysis: Dict           # Pattern complexity metrics

# From Phase 5
tempo_alignment: Dict               # BPM and timing information
bpm_validation: Dict                # BPM consistency data

# Musical structure analysis
song_structure: Dict = {
    "sections": List[Dict],         # Verse, chorus, bridge sections
    "intensity_profile": List[float], # Intensity over time
    "climax_points": List[float],   # High-intensity moments
    "energy_changes": List[Dict]    # Energy level transitions
}

# Input directory structure
data\\processed\\phase10\\
├── measure_segments\\*.json        # Measure information
├── bar_lines\\*.json               # Bar line data
data\\processed\\phase9\\
├── difficulty_patterns\\*.json     # Pattern complexity data
```

### **Outputs**
```python
# Go-Go Time sections
gogo_sections: List[Dict] = [
    {
        "gogo_id": int,                 # Sequential Go-Go section ID
        "start_time": float,            # Section start time (seconds)
        "end_time": float,              # Section end time (seconds)
        "start_measure": int,           # Starting measure number
        "end_measure": int,             # Ending measure number
        "duration": float,              # Section duration
        "intensity_score": float,       # Musical intensity (0-1)
        "complexity_score": float,      # Pattern complexity (0-1)
        "placement_confidence": float,  # Confidence in placement (0-1)
        "gogo_type": str,              # "climax", "chorus", "bridge"
        "visual_effects": Dict          # Additional visual effects
    }
]

# Scroll speed changes
scroll_changes: List[Dict] = [
    {
        "change_id": int,               # Sequential change ID
        "time_position": float,         # Change time (seconds)
        "measure_position": int,        # Measure number
        "old_scroll_speed": float,      # Previous scroll speed
        "new_scroll_speed": float,      # New scroll speed
        "change_reason": str,           # "difficulty", "musical", "visual"
        "transition_type": str          # "instant", "gradual"
    }
]

# BPM changes
bpm_changes: List[Dict] = [
    {
        "change_id": int,               # Sequential change ID
        "time_position": float,         # Change time (seconds)
        "measure_position": int,        # Measure number
        "old_bpm": float,               # Previous BPM
        "new_bpm": float,               # New BPM
        "change_confidence": float,     # Confidence in detection (0-1)
        "change_type": str              # "gradual", "sudden", "return"
    }
]

# Special section summary
special_sections_summary: Dict = {
    "total_gogo_sections": int,
    "total_gogo_duration": float,
    "gogo_coverage_percentage": float,
    "scroll_changes_count": int,
    "bmp_changes_count": int,
    "special_effects_density": float,
    "section_quality_score": float
}

# Output directory structure
data\\processed\\phase11\\
├── gogo_sections\\*.json           # Go-Go Time sections
├── scroll_changes\\*.json          # Scroll speed changes
├── bpm_changes\\*.json             # BPM changes
├── special_sections\\*.json        # Combined special sections
├── intensity_analysis\\*.json      # Musical intensity analysis
└── special_sections_report.json    # Overall special sections summary
```

---

## 3. **Implementation Plan**

### **Recommended Tools**
```python
import numpy as np
import pandas as pd
from pathlib import Path
import json
import logging
from typing import List, Dict, Tuple, Optional
from tqdm import tqdm
import matplotlib.pyplot as plt
from scipy.signal import find_peaks, savgol_filter
from sklearn.preprocessing import MinMaxScaler
```

### **Musical Intensity Analysis**
```python
def analyze_musical_intensity(
    measure_segments: List[Dict],
    difficulty_patterns: List[Dict]
) -> Dict:
    """
    Analyze musical intensity across the song.
    
    Args:
        measure_segments: List of measure segments with complexity
        difficulty_patterns: Pattern complexity analysis
        
    Returns:
        Intensity analysis results
    """
    
    if not measure_segments:
        return {"intensity_profile": [], "climax_points": [], "energy_changes": []}
    
    # Extract complexity scores over time
    times = [m["start_time"] for m in measure_segments]
    complexities = [m["measure_complexity"] for m in measure_segments]
    
    # Smooth the complexity profile
    if len(complexities) > 5:
        smoothed_complexity = savgol_filter(complexities, 
                                          window_length=min(5, len(complexities)), 
                                          polyorder=2)
    else:
        smoothed_complexity = complexities
    
    # Normalize intensity to 0-1 range
    scaler = MinMaxScaler()
    intensity_profile = scaler.fit_transform(np.array(smoothed_complexity).reshape(-1, 1)).flatten()
    
    # Find climax points (local maxima with high intensity)
    peaks, properties = find_peaks(intensity_profile, 
                                  height=0.7,  # Minimum intensity
                                  distance=8,  # Minimum distance between peaks
                                  prominence=0.2)  # Minimum prominence
    
    climax_points = [times[i] for i in peaks if i < len(times)]
    
    # Detect energy changes (significant intensity transitions)
    energy_changes = detect_energy_changes(times, intensity_profile)
    
    return {
        "intensity_profile": intensity_profile.tolist(),
        "intensity_times": times,
        "climax_points": climax_points,
        "energy_changes": energy_changes,
        "peak_indices": peaks.tolist(),
        "average_intensity": float(np.mean(intensity_profile))
    }

def detect_energy_changes(times: List[float], intensity_profile: List[float]) -> List[Dict]:
    """Detect significant energy level changes."""
    
    energy_changes = []
    
    if len(intensity_profile) < 3:
        return energy_changes
    
    # Calculate intensity differences
    intensity_diff = np.diff(intensity_profile)
    
    # Find significant changes (threshold-based)
    change_threshold = 0.3
    
    for i, diff in enumerate(intensity_diff):
        if abs(diff) > change_threshold:
            change_type = "increase" if diff > 0 else "decrease"
            
            energy_change = {
                "time": times[i + 1] if i + 1 < len(times) else times[-1],
                "measure": i + 1,
                "intensity_change": float(diff),
                "change_type": change_type,
                "magnitude": float(abs(diff))
            }
            
            energy_changes.append(energy_change)
    
    return energy_changes

def detect_gogo_sections(
    measure_segments: List[Dict],
    intensity_analysis: Dict,
    min_gogo_duration: float = 8.0,  # Minimum 8 seconds
    max_gogo_duration: float = 32.0,  # Maximum 32 seconds
    intensity_threshold: float = 0.6
) -> List[Dict]:
    """
    Detect Go-Go Time sections based on musical intensity.
    
    Args:
        measure_segments: Measure segments with timing
        intensity_analysis: Musical intensity analysis
        min_gogo_duration: Minimum Go-Go section duration
        max_gogo_duration: Maximum Go-Go section duration
        intensity_threshold: Minimum intensity for Go-Go sections
        
    Returns:
        List of detected Go-Go sections
    """
    
    gogo_sections = []
    
    if not measure_segments or not intensity_analysis.get("intensity_profile"):
        return gogo_sections
    
    intensity_profile = intensity_analysis["intensity_profile"]
    times = intensity_analysis["intensity_times"]
    climax_points = intensity_analysis["climax_points"]
    
    # Find high-intensity regions
    high_intensity_regions = find_high_intensity_regions(
        times, intensity_profile, intensity_threshold
    )
    
    # Convert regions to Go-Go sections
    gogo_id = 0
    
    for region in high_intensity_regions:
        start_time, end_time = region["start_time"], region["end_time"]
        duration = end_time - start_time
        
        # Filter by duration constraints
        if min_gogo_duration <= duration <= max_gogo_duration:
            
            # Find corresponding measures
            start_measure = find_measure_at_time(measure_segments, start_time)
            end_measure = find_measure_at_time(measure_segments, end_time)
            
            # Calculate section properties
            avg_intensity = region["average_intensity"]
            complexity_score = calculate_section_complexity(
                measure_segments, start_time, end_time
            )
            
            # Determine Go-Go type based on characteristics
            gogo_type = determine_gogo_type(start_time, climax_points, avg_intensity)
            
            # Calculate placement confidence
            placement_confidence = calculate_placement_confidence(
                region, climax_points, intensity_profile
            )
            
            gogo_section = {
                "gogo_id": gogo_id,
                "start_time": float(start_time),
                "end_time": float(end_time),
                "start_measure": start_measure,
                "end_measure": end_measure,
                "duration": float(duration),
                "intensity_score": float(avg_intensity),
                "complexity_score": float(complexity_score),
                "placement_confidence": float(placement_confidence),
                "gogo_type": gogo_type,
                "visual_effects": {
                    "background_animation": True,
                    "note_effects": True,
                    "screen_flash": avg_intensity > 0.8
                }
            }
            
            gogo_sections.append(gogo_section)
            gogo_id += 1
    
    return gogo_sections

def find_high_intensity_regions(
    times: List[float], 
    intensity_profile: List[float], 
    threshold: float
) -> List[Dict]:
    """Find continuous regions above intensity threshold."""
    
    regions = []
    current_region = None
    
    for i, (time, intensity) in enumerate(zip(times, intensity_profile)):
        if intensity >= threshold:
            if current_region is None:
                # Start new region
                current_region = {
                    "start_time": time,
                    "start_index": i,
                    "intensities": [intensity]
                }
            else:
                # Continue current region
                current_region["intensities"].append(intensity)
        else:
            if current_region is not None:
                # End current region
                current_region["end_time"] = times[i - 1] if i > 0 else time
                current_region["end_index"] = i - 1
                current_region["average_intensity"] = np.mean(current_region["intensities"])
                
                regions.append(current_region)
                current_region = None
    
    # Handle region that extends to end of song
    if current_region is not None:
        current_region["end_time"] = times[-1]
        current_region["end_index"] = len(times) - 1
        current_region["average_intensity"] = np.mean(current_region["intensities"])
        regions.append(current_region)
    
    return regions

def find_measure_at_time(measure_segments: List[Dict], target_time: float) -> int:
    """Find the measure number at a specific time."""
    
    for measure in measure_segments:
        if measure["start_time"] <= target_time < measure["end_time"]:
            return measure["measure_id"]
    
    # Return closest measure if exact match not found
    if measure_segments:
        distances = [abs(m["start_time"] - target_time) for m in measure_segments]
        closest_idx = np.argmin(distances)
        return measure_segments[closest_idx]["measure_id"]
    
    return 0

def calculate_section_complexity(
    measure_segments: List[Dict], 
    start_time: float, 
    end_time: float
) -> float:
    """Calculate average complexity for a time section."""
    
    section_measures = [
        m for m in measure_segments 
        if start_time <= m["start_time"] < end_time
    ]
    
    if not section_measures:
        return 0.0
    
    complexities = [m["measure_complexity"] for m in section_measures]
    return np.mean(complexities)

def determine_gogo_type(start_time: float, climax_points: List[float], intensity: float) -> str:
    """Determine the type of Go-Go section based on characteristics."""
    
    # Check if section contains climax points
    nearby_climax = any(abs(cp - start_time) < 4.0 for cp in climax_points)
    
    if nearby_climax and intensity > 0.8:
        return "climax"
    elif intensity > 0.7:
        return "chorus"
    else:
        return "bridge"

def calculate_placement_confidence(
    region: Dict, 
    climax_points: List[float], 
    intensity_profile: List[float]
) -> float:
    """Calculate confidence in Go-Go section placement."""
    
    # Factors that increase confidence:
    # 1. High average intensity
    # 2. Proximity to climax points
    # 3. Consistent high intensity throughout region
    # 4. Appropriate duration
    
    intensity_confidence = region["average_intensity"]
    
    # Climax proximity confidence
    start_time = region["start_time"]
    climax_distances = [abs(cp - start_time) for cp in climax_points]
    min_climax_distance = min(climax_distances) if climax_distances else float('inf')
    climax_confidence = max(0.0, 1.0 - min_climax_distance / 16.0)  # Within 16 seconds
    
    # Intensity consistency confidence
    intensities = region["intensities"]
    intensity_std = np.std(intensities) if len(intensities) > 1 else 0.0
    consistency_confidence = max(0.0, 1.0 - intensity_std)
    
    # Duration appropriateness confidence
    duration = region["end_time"] - region["start_time"]
    ideal_duration = 16.0  # 16 seconds is ideal
    duration_confidence = max(0.0, 1.0 - abs(duration - ideal_duration) / ideal_duration)
    
    # Weighted combination
    total_confidence = (
        intensity_confidence * 0.4 +
        climax_confidence * 0.3 +
        consistency_confidence * 0.2 +
        duration_confidence * 0.1
    )
    
    return min(1.0, total_confidence)

def detect_scroll_changes(
    measure_segments: List[Dict],
    difficulty_patterns: List[Dict],
    base_scroll_speed: float = 1.0
) -> List[Dict]:
    """Detect appropriate scroll speed changes based on pattern complexity."""
    
    scroll_changes = []
    current_scroll_speed = base_scroll_speed
    change_id = 0
    
    # Analyze complexity changes that warrant scroll speed adjustments
    for i, measure in enumerate(measure_segments):
        measure_complexity = measure["measure_complexity"]
        
        # Determine appropriate scroll speed based on complexity
        if measure_complexity > 0.8:
            target_scroll_speed = 1.2  # Faster for complex sections
        elif measure_complexity > 0.6:
            target_scroll_speed = 1.1  # Slightly faster
        elif measure_complexity < 0.3:
            target_scroll_speed = 0.9  # Slower for simple sections
        else:
            target_scroll_speed = 1.0  # Normal speed
        
        # Only create change if significantly different
        if abs(target_scroll_speed - current_scroll_speed) > 0.1:
            scroll_change = {
                "change_id": change_id,
                "time_position": float(measure["start_time"]),
                "measure_position": measure["measure_id"],
                "old_scroll_speed": float(current_scroll_speed),
                "new_scroll_speed": float(target_scroll_speed),
                "change_reason": "difficulty",
                "transition_type": "gradual"
            }
            
            scroll_changes.append(scroll_change)
            current_scroll_speed = target_scroll_speed
            change_id += 1
    
    return scroll_changes

def process_special_sections(
    input_dir: Path = Path("data\\processed\\phase10"),
    pattern_dir: Path = Path("data\\processed\\phase9"),
    output_dir: Path = Path("data\\processed\\phase11"),
    intensity_threshold: float = 0.6
) -> Dict:
    """Process special sections detection for entire dataset."""
    
    # Setup output directories
    (output_dir / "gogo_sections").mkdir(parents=True, exist_ok=True)
    (output_dir / "scroll_changes").mkdir(parents=True, exist_ok=True)
    (output_dir / "bpm_changes").mkdir(parents=True, exist_ok=True)
    (output_dir / "special_sections").mkdir(parents=True, exist_ok=True)
    (output_dir / "intensity_analysis").mkdir(parents=True, exist_ok=True)
    
    # Find all measure files
    measure_files = list((input_dir / "measure_segments").glob("*.json"))
    
    results = {
        "total_songs": len(measure_files),
        "processed_songs": 0,
        "total_gogo_sections": 0,
        "total_scroll_changes": 0,
        "average_gogo_per_song": 0.0,
        "average_gogo_duration": 0.0,
        "processing_errors": []
    }
    
    all_gogo_counts = []
    all_gogo_durations = []
    
    for measure_file in tqdm(measure_files, desc="Processing special sections"):
        try:
            song_name = measure_file.stem
            
            # Load measure data
            with open(measure_file, 'r') as f:
                measure_segments = json.load(f)
            
            # Load pattern data
            pattern_file = pattern_dir / "difficulty_patterns" / f"{song_name}.json"
            difficulty_patterns = []
            
            if pattern_file.exists():
                with open(pattern_file, 'r') as f:
                    difficulty_patterns = json.load(f)
            
            # Analyze musical intensity
            intensity_analysis = analyze_musical_intensity(measure_segments, difficulty_patterns)
            
            # Detect Go-Go sections
            gogo_sections = detect_gogo_sections(
                measure_segments, intensity_analysis, intensity_threshold=intensity_threshold
            )
            
            # Detect scroll changes
            scroll_changes = detect_scroll_changes(measure_segments, difficulty_patterns)
            
            # Combine all special sections
            special_sections = {
                "gogo_sections": gogo_sections,
                "scroll_changes": scroll_changes,
                "bpm_changes": [],  # Placeholder for BPM changes
                "intensity_analysis": intensity_analysis
            }
            
            # Save results
            gogo_file = output_dir / "gogo_sections" / f"{song_name}.json"
            with open(gogo_file, 'w') as f:
                json.dump(gogo_sections, f, indent=2)
            
            scroll_file = output_dir / "scroll_changes" / f"{song_name}.json"
            with open(scroll_file, 'w') as f:
                json.dump(scroll_changes, f, indent=2)
            
            special_file = output_dir / "special_sections" / f"{song_name}.json"
            with open(special_file, 'w') as f:
                json.dump(special_sections, f, indent=2)
            
            intensity_file = output_dir / "intensity_analysis" / f"{song_name}.json"
            with open(intensity_file, 'w') as f:
                json.dump(intensity_analysis, f, indent=2)
            
            # Update statistics
            results["processed_songs"] += 1
            results["total_gogo_sections"] += len(gogo_sections)
            results["total_scroll_changes"] += len(scroll_changes)
            
            all_gogo_counts.append(len(gogo_sections))
            if gogo_sections:
                song_gogo_duration = sum(g["duration"] for g in gogo_sections)
                all_gogo_durations.append(song_gogo_duration)
            
        except Exception as e:
            error_info = {"song": song_name, "error": str(e)}
            results["processing_errors"].append(error_info)
            logging.error(f"Error processing {song_name}: {e}")
    
    # Calculate final statistics
    if all_gogo_counts:
        results["average_gogo_per_song"] = float(np.mean(all_gogo_counts))
    if all_gogo_durations:
        results["average_gogo_duration"] = float(np.mean(all_gogo_durations))
    
    # Save overall results
    with open(output_dir / "special_sections_report.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    return results
```

---

## 4. **Best Practices**

### **Musical Structure Awareness**
- Align Go-Go sections with natural musical climaxes
- Consider song structure (verse, chorus, bridge)
- Respect musical phrasing and boundaries
- Avoid abrupt transitions that break musical flow

### **Intensity Analysis**
```python
# Multi-factor intensity calculation
def calculate_comprehensive_intensity(measure: Dict, context: Dict) -> float:
    """Calculate intensity using multiple musical factors."""
    
    # Pattern complexity
    complexity_score = measure["measure_complexity"]
    
    # Note density
    note_count = len([n for n in measure["notes_in_measure"] if n["note_type"] != "rest"])
    density_score = min(1.0, note_count / 16.0)
    
    # Rhythmic activity
    rhythmic_score = calculate_rhythmic_activity(measure["notes_in_measure"])
    
    # Dynamic context (surrounding measures)
    context_score = calculate_dynamic_context(measure, context)
    
    # Weighted combination
    intensity = (
        complexity_score * 0.3 +
        density_score * 0.3 +
        rhythmic_score * 0.2 +
        context_score * 0.2
    )
    
    return min(1.0, intensity)
```

### **Go-Go Section Quality**
- Ensure minimum and maximum duration constraints
- Validate musical appropriateness of placement
- Check for reasonable spacing between sections
- Consider player fatigue and recovery time

### **Dynamic Effects Balance**
- Don't overuse special effects (diminishing returns)
- Coordinate scroll changes with musical changes
- Ensure effects enhance rather than distract from gameplay
- Test effects for visual clarity and readability

---

## 5. **Challenges & Pitfalls**

### **Subjective Musical Interpretation**
- **Issue**: Musical intensity and climax points are subjective
- **Example**: Different listeners may perceive different sections as climactic
- **Mitigation**: Use multiple intensity metrics and validation
- **Solution**: Implement user feedback systems for refinement

### **Over-Application of Effects**
- **Issue**: Too many special sections can overwhelm players
- **Symptoms**: Constant Go-Go Time, excessive scroll changes
- **Mitigation**: Use conservative thresholds and spacing rules
- **Solution**: Implement effect density limits and cooldown periods

### **Timing Precision**
- **Issue**: Special section boundaries must align precisely with musical structure
- **Symptoms**: Go-Go Time starting/ending mid-phrase
- **Mitigation**: Snap boundaries to measure boundaries
- **Solution**: Use musical structure analysis for boundary refinement

### **Performance Impact**
- **Issue**: Complex visual effects may impact game performance
- **Consideration**: Balance visual appeal with performance requirements
- **Mitigation**: Provide performance-based effect scaling
- **Solution**: Implement adaptive quality settings

---

## 6. **Dependencies & Unlocks**

### **Prerequisites**
- **Phase 10 Complete**: Measure segmentation and timing structure
- **Required Files**:
  - `data\\processed\\phase10\\measure_segments\\*.json`
  - `data\\processed\\phase9\\difficulty_patterns\\*.json`
- **Libraries**: `scipy`, `sklearn` for signal processing and analysis

### **What This Phase Unlocks**
- **Phase 13**: Special sections provide dynamic elements for chart assembly
- **Enhanced Gameplay**: Go-Go Time and effects improve player engagement
- **Visual Polish**: Dynamic effects create professional-quality charts
- **Difficulty Variation**: Special sections add complexity variation within charts

### **Output Dependencies**
Subsequent phases depend on these Phase 11 outputs:
- `data\\processed\\phase11\\special_sections\\*.json` - Combined special sections data
- `data\\processed\\phase11\\gogo_sections\\*.json` - Go-Go Time section definitions
- `data\\processed\\phase11\\scroll_changes\\*.json` - Scroll speed change commands

---

## 7. **Validation Strategy**

### **Unit Tests**
```python
def test_intensity_analysis():
    """Test musical intensity analysis."""
    # Create test measures with varying complexity
    measures = [
        {"start_time": 0.0, "measure_complexity": 0.3},
        {"start_time": 2.0, "measure_complexity": 0.8},
        {"start_time": 4.0, "measure_complexity": 0.9},
        {"start_time": 6.0, "measure_complexity": 0.4}
    ]
    
    intensity = analyze_musical_intensity(measures, [])
    
    assert len(intensity["intensity_profile"]) == len(measures)
    assert len(intensity["climax_points"]) >= 0
    assert 0.0 <= intensity["average_intensity"] <= 1.0

def test_gogo_detection():
    """Test Go-Go section detection."""
    measures = create_test_measures_with_intensity()
    intensity_analysis = {"intensity_profile": [0.3, 0.8, 0.9, 0.8, 0.4], 
                         "intensity_times": [0, 2, 4, 6, 8],
                         "climax_points": [4.0]}
    
    gogo_sections = detect_gogo_sections(measures, intensity_analysis)
    
    assert len(gogo_sections) >= 0
    for section in gogo_sections:
        assert section["duration"] >= 8.0  # Minimum duration
        assert 0.0 <= section["intensity_score"] <= 1.0
```

### **Quality Metrics**
- **Go-Go Appropriateness**: >80% of Go-Go sections align with musical climaxes
- **Section Duration**: 90% of Go-Go sections between 8-32 seconds
- **Intensity Accuracy**: >85% correlation between detected intensity and musical energy
- **Effect Balance**: <30% of total song duration in Go-Go Time

### **Musical Validation**
```python
def validate_special_sections_musicality(
    gogo_sections: List[Dict],
    measure_segments: List[Dict],
    song_duration: float
) -> Dict:
    """Validate musical appropriateness of special sections."""
    
    validation_metrics = {
        "gogo_coverage_appropriate": False,
        "section_spacing_good": False,
        "intensity_alignment": 0.0,
        "musical_coherence": 0.0
    }
    
    if not gogo_sections:
        return validation_metrics
    
    # Check Go-Go coverage (should be 15-40% of song)
    total_gogo_duration = sum(g["duration"] for g in gogo_sections)
    gogo_coverage = total_gogo_duration / song_duration
    validation_metrics["gogo_coverage_appropriate"] = 0.15 <= gogo_coverage <= 0.40
    
    # Check spacing between sections (should have breaks)
    if len(gogo_sections) > 1:
        gaps = []
        for i in range(1, len(gogo_sections)):
            gap = gogo_sections[i]["start_time"] - gogo_sections[i-1]["end_time"]
            gaps.append(gap)
        
        # Good spacing: gaps of at least 8 seconds
        validation_metrics["section_spacing_good"] = all(gap >= 8.0 for gap in gaps)
    else:
        validation_metrics["section_spacing_good"] = True
    
    # Check intensity alignment
    intensity_scores = [g["intensity_score"] for g in gogo_sections]
    validation_metrics["intensity_alignment"] = float(np.mean(intensity_scores))
    
    # Overall musical coherence
    coherence_factors = [
        validation_metrics["gogo_coverage_appropriate"],
        validation_metrics["section_spacing_good"],
        validation_metrics["intensity_alignment"] > 0.7
    ]
    validation_metrics["musical_coherence"] = sum(coherence_factors) / len(coherence_factors)
    
    return validation_metrics
```

### **Example Success Case**
```python
# Expected Go-Go section detection
gogo_sections = [
    {
        "gogo_id": 0,
        "start_time": 32.0,
        "end_time": 48.0,
        "start_measure": 16,
        "end_measure": 24,
        "duration": 16.0,
        "intensity_score": 0.85,
        "complexity_score": 0.78,
        "placement_confidence": 0.92,
        "gogo_type": "chorus",
        "visual_effects": {
            "background_animation": True,
            "note_effects": True,
            "screen_flash": True
        }
    }
]

# Expected processing results
processing_results = {
    "total_songs": 150,
    "processed_songs": 147,
    "total_gogo_sections": 294,
    "total_scroll_changes": 441,
    "average_gogo_per_song": 2.0,
    "average_gogo_duration": 14.5,
    "processing_errors": []
}

# Expected intensity analysis
intensity_analysis = {
    "intensity_profile": [0.3, 0.4, 0.7, 0.9, 0.8, 0.5, 0.3],
    "climax_points": [6.0, 18.0],
    "energy_changes": [
        {"time": 4.0, "change_type": "increase", "magnitude": 0.4},
        {"time": 10.0, "change_type": "decrease", "magnitude": 0.3}
    ],
    "average_intensity": 0.56
}
```

---

**Phase 11 Complete**. This phase adds dynamic special sections like Go-Go Time and scroll changes that enhance player engagement and create professional-quality chart experiences.

**Next**: [Phase 12: TJA Header Generation](phase_12_tja_headers.md)
