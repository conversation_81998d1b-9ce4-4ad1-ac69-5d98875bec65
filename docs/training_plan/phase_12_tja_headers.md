# 🧩 Phase 12: TJA Header Generation

**Status**: 📋 Planned  
**Estimated Duration**: 2 days  
**Dependencies**: [Phase 1: Audio Loading](phase_01_audio_loading.md)  
**Next Phase**: [Phase 13: Chart Assembly & Formatting](phase_13_chart_assembly.md)

---

## 1. **Phase Purpose**

This phase generates complete TJA header sections with metadata, course information, and configuration parameters. This step is isolated because:

- **Header generation** requires different data sources than chart content
- **Metadata extraction** involves audio file analysis and user input
- **Course configuration** depends on difficulty and scoring parameters
- **Format compliance** ensures headers meet TJA specification requirements

**Why Isolated**: Header generation involves metadata processing and format compliance different from musical content generation. Headers are essential for TJA file validity and proper game integration.

---

## 2. **Inputs & Outputs**

### **Inputs**
```python
# From Phase 1
audio_metadata: Dict                # Audio file metadata and properties
file_info: Dict                     # File paths and naming information

# From Phase 5
tempo_alignment: Dict               # BPM and timing information
bpm_validation: Dict                # Validated BPM data

# User-provided metadata
song_metadata: Dict = {
    "title": str,                   # Song title
    "subtitle": str,                # Artist or subtitle
    "genre": str,                   # Music genre
    "maker": str,                   # Chart creator
    "lyrics": str,                  # Lyrics file path (optional)
    "music": str,                   # Music file path
    "preview_start": float,         # Preview start time (seconds)
    "preview_length": float         # Preview duration (seconds)
}

# Difficulty configuration
difficulty_config: Dict = {
    "target_difficulty": int,       # 8, 9, or 10
    "course_type": str,            # "Oni", "Edit"
    "scoring_config": Dict,         # Scoring parameters
    "balloon_notes": List[int]      # Balloon hit counts (if any)
}

# Input directory structure
data\\processed\\phase1\\
├── metadata\\*.json                # Audio metadata
data\\processed\\phase5\\
├── tempo_alignment\\*.json         # BPM information
```

### **Outputs**
```python
# Complete TJA headers
tja_headers: Dict = {
    "global_headers": {
        "TITLE": str,               # Song title
        "SUBTITLE": str,            # Artist/subtitle
        "BPM": float,               # Base BPM
        "WAVE": str,                # Audio file name
        "OFFSET": float,            # Timing offset (seconds)
        "DEMOSTART": float,         # Demo start time
        "GENRE": str,               # Music genre
        "SCOREMODE": int,           # Scoring mode (0-2)
        "MAKER": str,               # Chart creator
        "LYRICS": str,              # Lyrics file (optional)
        "SONGVOL": int,             # Song volume (0-100)
        "SEVOL": int                # Sound effect volume (0-100)
    },
    "course_headers": {
        "COURSE": str,              # Course type (Oni, Edit, etc.)
        "LEVEL": int,               # Difficulty level (1-10)
        "BALLOON": str,             # Balloon note counts
        "SCOREINIT": int,           # Initial score
        "SCOREDIFF": int,           # Score difference
        "TTROWBEAT": float          # Drumroll beat interval (optional)
    },
    "header_validation": {
        "required_headers_present": bool,
        "format_compliance": bool,
        "value_ranges_valid": bool,
        "encoding_correct": bool
    }
}

# Generated header content
header_content: str = """TITLE:Song Title
SUBTITLE:Artist Name
BPM:128.0
WAVE:song.ogg
OFFSET:-1.5
DEMOSTART:30.0
GENRE:Pop
SCOREMODE:1
MAKER:AI Generator
SONGVOL:100
SEVOL:100

COURSE:Oni
LEVEL:9
BALLOON:
SCOREINIT:450
SCOREDIFF:110
"""

# Header metadata
header_metadata: Dict = {
    "generation_timestamp": str,
    "source_audio_file": str,
    "detected_bpm": float,
    "calculated_offset": float,
    "header_version": str,
    "encoding": str,
    "total_headers": int,
    "validation_status": str
}

# Output directory structure
data\\processed\\phase12\\
├── tja_headers\\*.json             # Complete header data
├── header_content\\*.txt           # Raw header text content
├── header_metadata\\*.json         # Header generation metadata
├── validation_reports\\*.json      # Header validation results
└── header_generation_report.json   # Overall generation statistics
```

---

## 3. **Implementation Plan**

### **Recommended Tools**
```python
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import re
from datetime import datetime
import unicodedata
```

### **Core Header Generation Function**
```python
def generate_tja_headers(
    song_metadata: Dict,
    audio_metadata: Dict,
    tempo_data: Dict,
    difficulty_config: Dict
) -> Dict:
    """
    Generate complete TJA headers from input data.
    
    Args:
        song_metadata: User-provided song information
        audio_metadata: Audio file metadata from Phase 1
        tempo_data: BPM and timing data from Phase 5
        difficulty_config: Difficulty and course configuration
        
    Returns:
        Complete TJA headers dictionary
    """
    
    # Generate global headers
    global_headers = generate_global_headers(
        song_metadata, audio_metadata, tempo_data
    )
    
    # Generate course-specific headers
    course_headers = generate_course_headers(difficulty_config)
    
    # Validate headers
    validation_results = validate_headers(global_headers, course_headers)
    
    return {
        "global_headers": global_headers,
        "course_headers": course_headers,
        "header_validation": validation_results
    }

def generate_global_headers(
    song_metadata: Dict,
    audio_metadata: Dict,
    tempo_data: Dict
) -> Dict:
    """Generate global TJA headers."""
    
    # Extract and validate basic information
    title = sanitize_header_text(song_metadata.get("title", "Unknown Title"))
    subtitle = sanitize_header_text(song_metadata.get("subtitle", "Unknown Artist"))
    
    # BPM from tempo analysis
    bpm = tempo_data.get("aligned_bpm", 120.0)
    
    # Audio file name (extract from path)
    audio_file = audio_metadata.get("file_path", "song.ogg")
    wave_filename = Path(audio_file).name
    
    # Calculate timing offset
    offset = calculate_timing_offset(tempo_data, audio_metadata)
    
    # Demo start time (typically 30 seconds or 25% into song)
    demo_start = calculate_demo_start(
        audio_metadata.get("duration", 120.0),
        song_metadata.get("preview_start")
    )
    
    # Genre classification
    genre = classify_genre(song_metadata.get("genre", ""), audio_metadata)
    
    # Other standard headers
    global_headers = {
        "TITLE": title,
        "SUBTITLE": subtitle,
        "BPM": float(bpm),
        "WAVE": wave_filename,
        "OFFSET": float(offset),
        "DEMOSTART": float(demo_start),
        "GENRE": genre,
        "SCOREMODE": 1,  # Standard scoring mode
        "MAKER": song_metadata.get("maker", "AI Generator"),
        "SONGVOL": 100,  # Full volume
        "SEVOL": 100     # Full sound effects volume
    }
    
    # Optional headers
    if song_metadata.get("lyrics"):
        global_headers["LYRICS"] = song_metadata["lyrics"]
    
    return global_headers

def generate_course_headers(difficulty_config: Dict) -> Dict:
    """Generate course-specific headers."""
    
    target_difficulty = difficulty_config.get("target_difficulty", 9)
    
    # Map difficulty to course type
    course_mapping = {
        8: "Oni",
        9: "Oni", 
        10: "Edit"
    }
    
    course = difficulty_config.get("course_type") or course_mapping.get(target_difficulty, "Oni")
    
    # Calculate scoring parameters
    scoring_params = calculate_scoring_parameters(target_difficulty)
    
    # Balloon notes (empty for basic charts)
    balloon_notes = difficulty_config.get("balloon_notes", [])
    balloon_string = ",".join(map(str, balloon_notes)) if balloon_notes else ""
    
    course_headers = {
        "COURSE": course,
        "LEVEL": target_difficulty,
        "BALLOON": balloon_string,
        "SCOREINIT": scoring_params["init"],
        "SCOREDIFF": scoring_params["diff"]
    }
    
    # Optional course headers
    if difficulty_config.get("ttrowbeat"):
        course_headers["TTROWBEAT"] = float(difficulty_config["ttrowbeat"])
    
    return course_headers

def sanitize_header_text(text: str) -> str:
    """Sanitize text for TJA header compatibility."""
    
    if not text:
        return ""
    
    # Remove or replace problematic characters
    # TJA format is sensitive to certain characters
    sanitized = text.strip()
    
    # Remove control characters
    sanitized = ''.join(char for char in sanitized if unicodedata.category(char)[0] != 'C')
    
    # Replace problematic characters
    replacements = {
        ':': '：',  # Replace colon with full-width colon
        '\n': ' ',  # Replace newlines with spaces
        '\r': ' ',  # Replace carriage returns with spaces
        '\t': ' '   # Replace tabs with spaces
    }
    
    for old, new in replacements.items():
        sanitized = sanitized.replace(old, new)
    
    # Limit length (TJA headers should be reasonable length)
    max_length = 100
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length].strip()
    
    return sanitized

def calculate_timing_offset(tempo_data: Dict, audio_metadata: Dict) -> float:
    """Calculate timing offset for TJA file."""
    
    # Start with alignment offset from Phase 5
    base_offset = tempo_data.get("alignment_offset", 0.0)
    
    # Add any additional processing delays
    processing_delay = 0.0  # Could be calculated from audio processing
    
    # TJA offset is typically negative (audio starts before first beat)
    total_offset = base_offset + processing_delay
    
    # Clamp to reasonable range
    return max(-10.0, min(10.0, total_offset))

def calculate_demo_start(song_duration: float, user_preview_start: Optional[float] = None) -> float:
    """Calculate demo start time."""
    
    if user_preview_start is not None:
        # Use user-specified preview start
        return max(0.0, min(user_preview_start, song_duration - 10.0))
    
    # Default: start at 25% of song duration, but at least 10 seconds in
    default_start = max(10.0, song_duration * 0.25)
    
    # Ensure we don't start too close to the end
    return min(default_start, song_duration - 10.0)

def classify_genre(user_genre: str, audio_metadata: Dict) -> str:
    """Classify music genre for TJA header."""
    
    # If user provided genre, use it (with validation)
    if user_genre:
        return sanitize_genre(user_genre)
    
    # Try to infer genre from audio characteristics
    # This is a simplified classification - could be enhanced with ML
    
    # Default genres commonly used in rhythm games
    default_genres = [
        "Pop", "Rock", "Electronic", "Classical", "Jazz", 
        "Anime", "Game Music", "Variety", "Namco Original"
    ]
    
    # Simple heuristic based on file path or name
    file_path = audio_metadata.get("file_path", "").lower()
    
    for genre in default_genres:
        if genre.lower() in file_path:
            return genre
    
    # Default fallback
    return "Variety"

def sanitize_genre(genre: str) -> str:
    """Sanitize and validate genre string."""
    
    # Common TJA genres
    valid_genres = [
        "Pop", "Anime", "Kids", "Variety", "Vocaloid", "Game Music",
        "Namco Original", "Classical", "Jazz", "Rock", "Electronic"
    ]
    
    # Check if genre matches a valid one (case insensitive)
    for valid_genre in valid_genres:
        if genre.lower() == valid_genre.lower():
            return valid_genre
    
    # If not found, return sanitized version
    return sanitize_header_text(genre) or "Variety"

def calculate_scoring_parameters(difficulty: int) -> Dict:
    """Calculate scoring parameters based on difficulty."""
    
    # Standard scoring parameters for different difficulties
    scoring_table = {
        8: {"init": 400, "diff": 100},
        9: {"init": 450, "diff": 110},
        10: {"init": 500, "diff": 120}
    }
    
    return scoring_table.get(difficulty, {"init": 450, "diff": 110})

def validate_headers(global_headers: Dict, course_headers: Dict) -> Dict:
    """Validate TJA headers for compliance and correctness."""
    
    validation_results = {
        "required_headers_present": True,
        "format_compliance": True,
        "value_ranges_valid": True,
        "encoding_correct": True,
        "validation_issues": []
    }
    
    # Check required global headers
    required_global = ["TITLE", "BPM", "WAVE"]
    for header in required_global:
        if header not in global_headers or not global_headers[header]:
            validation_results["required_headers_present"] = False
            validation_results["validation_issues"].append(f"Missing required header: {header}")
    
    # Check required course headers
    required_course = ["COURSE", "LEVEL"]
    for header in required_course:
        if header not in course_headers or course_headers[header] is None:
            validation_results["required_headers_present"] = False
            validation_results["validation_issues"].append(f"Missing required course header: {header}")
    
    # Validate value ranges
    if "BPM" in global_headers:
        bpm = global_headers["BPM"]
        if not (30.0 <= bpm <= 300.0):
            validation_results["value_ranges_valid"] = False
            validation_results["validation_issues"].append(f"BPM out of range: {bpm}")
    
    if "LEVEL" in course_headers:
        level = course_headers["LEVEL"]
        if not (1 <= level <= 10):
            validation_results["value_ranges_valid"] = False
            validation_results["validation_issues"].append(f"Level out of range: {level}")
    
    if "OFFSET" in global_headers:
        offset = global_headers["OFFSET"]
        if not (-10.0 <= offset <= 10.0):
            validation_results["value_ranges_valid"] = False
            validation_results["validation_issues"].append(f"Offset out of range: {offset}")
    
    # Check text encoding (simplified check)
    text_headers = ["TITLE", "SUBTITLE", "MAKER", "GENRE"]
    for header in text_headers:
        if header in global_headers:
            try:
                # Try to encode as UTF-8
                global_headers[header].encode('utf-8')
            except UnicodeEncodeError:
                validation_results["encoding_correct"] = False
                validation_results["validation_issues"].append(f"Encoding issue in {header}")
    
    # Overall format compliance
    validation_results["format_compliance"] = (
        validation_results["required_headers_present"] and
        validation_results["value_ranges_valid"] and
        validation_results["encoding_correct"]
    )
    
    return validation_results

def format_headers_as_text(global_headers: Dict, course_headers: Dict) -> str:
    """Format headers as TJA text content."""
    
    lines = []
    
    # Global headers
    global_order = [
        "TITLE", "SUBTITLE", "BPM", "WAVE", "OFFSET", "DEMOSTART",
        "GENRE", "SCOREMODE", "MAKER", "LYRICS", "SONGVOL", "SEVOL"
    ]
    
    for header in global_order:
        if header in global_headers and global_headers[header] is not None:
            value = global_headers[header]
            lines.append(f"{header}:{value}")
    
    # Empty line separator
    lines.append("")
    
    # Course headers
    course_order = ["COURSE", "LEVEL", "BALLOON", "SCOREINIT", "SCOREDIFF", "TTROWBEAT"]
    
    for header in course_order:
        if header in course_headers and course_headers[header] is not None:
            value = course_headers[header]
            lines.append(f"{header}:{value}")
    
    # Final empty line
    lines.append("")
    
    return "\n".join(lines)

def process_header_generation(
    input_dir: Path = Path("data\\processed\\phase1"),
    tempo_dir: Path = Path("data\\processed\\phase5"),
    output_dir: Path = Path("data\\processed\\phase12"),
    default_metadata: Dict = None
) -> Dict:
    """Process header generation for entire dataset."""
    
    # Setup output directories
    (output_dir / "tja_headers").mkdir(parents=True, exist_ok=True)
    (output_dir / "header_content").mkdir(parents=True, exist_ok=True)
    (output_dir / "header_metadata").mkdir(parents=True, exist_ok=True)
    (output_dir / "validation_reports").mkdir(parents=True, exist_ok=True)
    
    # Default metadata if not provided
    if default_metadata is None:
        default_metadata = {
            "maker": "AI Generator",
            "genre": "Variety",
            "target_difficulty": 9
        }
    
    # Find all audio metadata files
    metadata_files = list((input_dir / "metadata").glob("*.json"))
    
    results = {
        "total_songs": len(metadata_files),
        "processed_songs": 0,
        "validation_passed": 0,
        "validation_failed": 0,
        "average_bpm": 0.0,
        "processing_errors": []
    }
    
    all_bpms = []
    
    for metadata_file in tqdm(metadata_files, desc="Generating TJA headers"):
        try:
            song_name = metadata_file.stem
            
            # Load audio metadata
            with open(metadata_file, 'r') as f:
                audio_metadata = json.load(f)
            
            # Load tempo data
            tempo_file = tempo_dir / "tempo_alignment" / f"{song_name}.json"
            tempo_data = {"aligned_bpm": 120.0, "alignment_offset": 0.0}
            
            if tempo_file.exists():
                with open(tempo_file, 'r') as f:
                    tempo_data = json.load(f)
            
            # Create song metadata (could be enhanced with user input)
            song_metadata = {
                "title": song_name.replace("_", " ").title(),
                "subtitle": "Unknown Artist",
                "maker": default_metadata["maker"],
                "genre": default_metadata["genre"]
            }
            
            # Create difficulty configuration
            difficulty_config = {
                "target_difficulty": default_metadata["target_difficulty"],
                "balloon_notes": []
            }
            
            # Generate headers
            tja_headers = generate_tja_headers(
                song_metadata, audio_metadata, tempo_data, difficulty_config
            )
            
            # Format as text
            header_text = format_headers_as_text(
                tja_headers["global_headers"],
                tja_headers["course_headers"]
            )
            
            # Create metadata
            header_metadata = {
                "generation_timestamp": datetime.now().isoformat(),
                "source_audio_file": audio_metadata.get("file_path", ""),
                "detected_bpm": tempo_data.get("aligned_bpm", 120.0),
                "calculated_offset": tja_headers["global_headers"].get("OFFSET", 0.0),
                "header_version": "1.0",
                "encoding": "utf-8",
                "total_headers": len(tja_headers["global_headers"]) + len(tja_headers["course_headers"]),
                "validation_status": "passed" if tja_headers["header_validation"]["format_compliance"] else "failed"
            }
            
            # Save results
            headers_file = output_dir / "tja_headers" / f"{song_name}.json"
            with open(headers_file, 'w', encoding='utf-8') as f:
                json.dump(tja_headers, f, indent=2, ensure_ascii=False)
            
            content_file = output_dir / "header_content" / f"{song_name}.txt"
            with open(content_file, 'w', encoding='utf-8') as f:
                f.write(header_text)
            
            metadata_file_out = output_dir / "header_metadata" / f"{song_name}.json"
            with open(metadata_file_out, 'w') as f:
                json.dump(header_metadata, f, indent=2)
            
            validation_file = output_dir / "validation_reports" / f"{song_name}.json"
            with open(validation_file, 'w') as f:
                json.dump(tja_headers["header_validation"], f, indent=2)
            
            # Update statistics
            results["processed_songs"] += 1
            
            if tja_headers["header_validation"]["format_compliance"]:
                results["validation_passed"] += 1
            else:
                results["validation_failed"] += 1
            
            all_bpms.append(tempo_data.get("aligned_bpm", 120.0))
            
        except Exception as e:
            error_info = {"song": song_name, "error": str(e)}
            results["processing_errors"].append(error_info)
            logging.error(f"Error processing {song_name}: {e}")
    
    # Calculate final statistics
    if all_bpms:
        results["average_bpm"] = float(sum(all_bpms) / len(all_bpms))
    
    # Save overall results
    with open(output_dir / "header_generation_report.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    return results
```

---

## 4. **Best Practices**

### **Text Sanitization**
- Handle Unicode characters properly for international song titles
- Replace problematic characters that break TJA parsing
- Validate text length limits for different header fields
- Ensure proper encoding (UTF-8 with BOM for TJA compatibility)

### **Metadata Accuracy**
```python
# Comprehensive metadata validation
def validate_metadata_accuracy(headers: Dict, audio_file: Path) -> Dict:
    """Validate metadata accuracy against audio file."""
    
    validation = {
        "title_appropriate": True,
        "bpm_reasonable": True,
        "offset_calculated": True,
        "file_references_valid": True
    }
    
    # Check if WAVE file exists
    wave_file = audio_file.parent / headers["global_headers"]["WAVE"]
    validation["file_references_valid"] = wave_file.exists()
    
    # Validate BPM range
    bpm = headers["global_headers"]["BPM"]
    validation["bpm_reasonable"] = 60.0 <= bpm <= 200.0
    
    return validation
```

### **Course Configuration**
- Use appropriate scoring parameters for each difficulty level
- Set reasonable demo start times (avoid silence or fade-ins)
- Configure balloon notes only when actually used in charts
- Ensure course type matches difficulty level conventions

### **Format Compliance**
- Follow TJA specification exactly for header names and formats
- Use proper line endings and encoding
- Validate all numeric ranges and constraints
- Test headers with reference TJA parsers

---

## 5. **Challenges & Pitfalls**

### **Character Encoding Issues**
- **Issue**: TJA files require specific encoding (UTF-8 with BOM)
- **Symptoms**: Garbled text, parsing errors in TJA readers
- **Mitigation**: Use proper encoding and test with various TJA parsers
- **Solution**: Implement encoding validation and conversion

### **Metadata Extraction**
- **Issue**: Limited metadata available from audio files alone
- **Example**: Artist names, proper song titles not in filename
- **Mitigation**: Use filename parsing and user input when available
- **Solution**: Implement metadata lookup services (MusicBrainz, etc.)

### **Timing Precision**
- **Issue**: Incorrect OFFSET values cause timing misalignment
- **Symptoms**: Notes not aligned with audio beats
- **Mitigation**: Use high-precision timing calculations from Phase 5
- **Solution**: Implement offset validation against beat detection

### **Regional Compatibility**
- **Issue**: Different TJA implementations may have slight format differences
- **Example**: Some parsers more strict about header order or values
- **Mitigation**: Follow most conservative format requirements
- **Solution**: Test with multiple TJA parsers and simulators

---

## 6. **Dependencies & Unlocks**

### **Prerequisites**
- **Phase 1 Complete**: Audio metadata and file information
- **Phase 5 Complete**: BPM and timing alignment data
- **Required Files**:
  - `data\\processed\\phase1\\metadata\\*.json`
  - `data\\processed\\phase5\\tempo_alignment\\*.json`
- **User Input**: Song titles, artist names, genre information (optional)

### **What This Phase Unlocks**
- **Phase 13**: Complete headers enable final chart assembly
- **TJA Compatibility**: Headers ensure generated files work with TJA parsers
- **Metadata Completeness**: Professional-quality chart information
- **User Experience**: Proper song information display in rhythm games

### **Output Dependencies**
Subsequent phases depend on these Phase 12 outputs:
- `data\\processed\\phase12\\tja_headers\\*.json` - Complete header data
- `data\\processed\\phase12\\header_content\\*.txt` - Formatted header text
- `data\\processed\\phase12\\validation_reports\\*.json` - Header validation results

---

## 7. **Validation Strategy**

### **Unit Tests**
```python
def test_header_generation():
    """Test basic header generation functionality."""
    song_metadata = {
        "title": "Test Song",
        "subtitle": "Test Artist",
        "maker": "Test Creator"
    }
    
    audio_metadata = {
        "file_path": "test_song.ogg",
        "duration": 120.0
    }
    
    tempo_data = {
        "aligned_bpm": 128.0,
        "alignment_offset": -1.5
    }
    
    difficulty_config = {
        "target_difficulty": 9
    }
    
    headers = generate_tja_headers(song_metadata, audio_metadata, tempo_data, difficulty_config)
    
    assert headers["global_headers"]["TITLE"] == "Test Song"
    assert headers["global_headers"]["BPM"] == 128.0
    assert headers["course_headers"]["LEVEL"] == 9
    assert headers["header_validation"]["format_compliance"] == True

def test_text_sanitization():
    """Test text sanitization for TJA compatibility."""
    problematic_text = "Song: Title\nWith\tProblems"
    sanitized = sanitize_header_text(problematic_text)
    
    assert ":" not in sanitized or "：" in sanitized  # Colon should be replaced
    assert "\n" not in sanitized
    assert "\t" not in sanitized
```

### **Quality Metrics**
- **Header Completeness**: 100% of required headers present
- **Format Compliance**: >99% of headers pass validation
- **Encoding Correctness**: 100% proper UTF-8 encoding
- **Processing Speed**: <1 second per song on RTX 3070

### **TJA Parser Validation**
```python
def validate_with_tja_parser(header_content: str) -> Dict:
    """Validate headers using reference TJA parser."""
    
    validation_results = {
        "parser_accepts": False,
        "parsing_errors": [],
        "warnings": []
    }
    
    try:
        # This would use a reference TJA parser
        # Implementation depends on available TJA parsing library
        
        # Placeholder validation
        required_patterns = [
            r"TITLE:.+",
            r"BPM:\d+\.?\d*",
            r"WAVE:.+\.(ogg|wav|mp3)",
            r"COURSE:(Easy|Normal|Hard|Oni|Edit)",
            r"LEVEL:[1-9]|10"
        ]
        
        for pattern in required_patterns:
            if not re.search(pattern, header_content):
                validation_results["parsing_errors"].append(f"Missing or invalid: {pattern}")
        
        validation_results["parser_accepts"] = len(validation_results["parsing_errors"]) == 0
        
    except Exception as e:
        validation_results["parsing_errors"].append(str(e))
    
    return validation_results
```

### **Example Success Case**
```python
# Expected header generation results
tja_headers = {
    "global_headers": {
        "TITLE": "Example Song",
        "SUBTITLE": "Example Artist",
        "BPM": 128.0,
        "WAVE": "example_song.ogg",
        "OFFSET": -1.5,
        "DEMOSTART": 30.0,
        "GENRE": "Pop",
        "SCOREMODE": 1,
        "MAKER": "AI Generator",
        "SONGVOL": 100,
        "SEVOL": 100
    },
    "course_headers": {
        "COURSE": "Oni",
        "LEVEL": 9,
        "BALLOON": "",
        "SCOREINIT": 450,
        "SCOREDIFF": 110
    },
    "header_validation": {
        "required_headers_present": True,
        "format_compliance": True,
        "value_ranges_valid": True,
        "encoding_correct": True,
        "validation_issues": []
    }
}

# Expected formatted header content
header_content = """TITLE:Example Song
SUBTITLE:Example Artist
BPM:128.0
WAVE:example_song.ogg
OFFSET:-1.5
DEMOSTART:30.0
GENRE:Pop
SCOREMODE:1
MAKER:AI Generator
SONGVOL:100
SEVOL:100

COURSE:Oni
LEVEL:9
BALLOON:
SCOREINIT:450
SCOREDIFF:110

"""

# Expected processing results
processing_results = {
    "total_songs": 150,
    "processed_songs": 150,
    "validation_passed": 148,
    "validation_failed": 2,
    "average_bpm": 125.3,
    "processing_errors": []
}
```

---

**Phase 12 Complete**. This phase generates complete, compliant TJA headers with proper metadata, timing information, and course configuration, ensuring generated charts integrate properly with TJA-compatible rhythm games.

**Next**: [Phase 13: Chart Assembly & Formatting](phase_13_chart_assembly.md) (already created)
