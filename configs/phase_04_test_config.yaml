data:
  audio_channels: 1
  augmentation:
    enabled: true
    noise_level: 0.01
    pitch_shift_range:
    - -2
    - 2
    time_stretch_range:
    - 0.9
    - 1.1
    volume_range:
    - 0.8
    - 1.2
  hop_length: 512
  input_sequence_length: 1024
  n_fft: 2048
  n_mels: 80
  num_workers: 2
  output_sequence_length: 512
  overlap_ratio: 0.5
  pin_memory: true
  sample_rate: 22050
  shuffle_train: true
  shuffle_val: false
  win_length: 2048
error_handling:
  auto_resume: true
  continue_on_error: false
  max_retry_attempts: 3
  recovery_checkpoint_frequency: 100
  save_on_interrupt: true
experiment:
  description: TJA chart generation training pipeline - Phase 4
  name: tja_generation_phase4
  random_seed: 42
  save_config: true
  save_model_summary: true
  save_training_history: true
  tags:
  - tja
  - audio
  - rhythm
  - generation
  - phase4
  version: v1.0
hardware:
  benchmark: true
  cuda_device_id: 0
  deterministic: false
  device: cuda
  distributed: false
  multi_gpu: false
loss:
  class_weights:
  - 1.0
  - 1.2
  - 1.5
  - 1.5
  - 1.2
  confidence_penalty_weight: 0.05
  label_smoothing: 0.1
  loss_type: cross_entropy
  temporal_consistency_weight: 0.1
memory:
  activation_checkpointing: true
  dynamic_batching: false
  empty_cache_frequency: 10
  gradient_checkpointing: true
  max_ram_usage_gb: 8.0
  max_sequence_length: 2048
  max_vram_usage_gb: 7.0
monitoring:
  log_frequency: 5
  primary_metric: accuracy
  progress_bar: true
  save_attention_maps: false
  save_predictions: true
  tensorboard_enabled: true
  tensorboard_log_frequency: 50
  track_metrics:
  - accuracy
  - loss
  - f1_score
  - precision
  - recall
  verbose_logging: true
output:
  create_training_report: true
  export_onnx: false
  export_torchscript: false
  include_model_analysis: true
  save_best_model: true
  save_classification_report: true
  save_confusion_matrix: true
  save_final_model: true
  save_training_plots: true
paths:
  architecture_specs: data\processed\phase3\architecture_specs
  input_audio: data\processed\phase2\filtered_audio
  input_metadata: data\processed\phase1\metadata
  input_quality: data\processed\phase2\quality_metrics
  input_timing: data\processed\phase1\timing_data
  model_checkpoints: data\processed\phase4\checkpoints
  model_configs: data\processed\phase3\model_configs
  output_root: data\processed\phase4
  tensorboard_logs: data\processed\phase4\tensorboard
  training_logs: data\processed\phase4\training_logs
  validation_results: data\processed\phase4\validation_results
quality_gates:
  max_overfitting_gap: 0.15
  max_training_time_hours: 24
  max_vram_usage_gb: 7.0
  min_f1_score: 0.5
  min_training_success_rate: 0.95
  min_validation_accuracy: 0.6
training:
  alternative_models:
  - lightweight
  - enhanced
  batch_size: 8
  beta1: 0.9
  beta2: 0.999
  early_stopping_min_delta: 0.001
  early_stopping_patience: 10
  epochs: 2
  epsilon: 1.0e-08
  gradient_accumulation_steps: 4
  gradient_checkpointing: true
  gradient_clip_norm: 1.0
  learning_rate: 1.0e-05
  max_checkpoints: 5
  min_lr_ratio: 0.01
  mixed_precision: true
  optimizer: adamw
  primary_model: standard
  save_best_only: true
  save_frequency: 1
  scheduler: cosine
  validation_frequency: 1
  validation_split: 0.2
  warmup_steps: 1000
  weight_decay: 0.001
