"""
Test script for Phase 4 training pipeline.
Runs a short training test to validate the complete pipeline.
"""

import sys
import os
import torch
import yaml
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.phases.phase_04_training_pipeline import <PERSON><PERSON><PERSON><PERSON><PERSON>


def test_short_training():
    """Run a short training test with limited epochs."""
    
    print("🧪 TESTING PHASE 4 TRAINING PIPELINE")
    print("=" * 60)
    
    # Load and modify configuration for testing
    config_path = "configs/phase_04_config.yaml"
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Modify for quick testing
    original_epochs = config['training']['epochs']
    config['training']['epochs'] = 2  # Only 2 epochs for testing
    config['training']['save_frequency'] = 1  # Save every epoch
    config['training']['validation_frequency'] = 1  # Validate every epoch
    config['monitoring']['log_frequency'] = 5  # Log more frequently
    
    print(f"Modified configuration for testing:")
    print(f"  Epochs: {original_epochs} → {config['training']['epochs']}")
    print(f"  Model: {config['training']['primary_model']}")
    print(f"  Batch size: {config['training']['batch_size']}")
    
    # Save test configuration
    test_config_path = "configs/phase_04_test_config.yaml"
    with open(test_config_path, 'w') as f:
        yaml.dump(config, f, default_flow_style=False)
    
    try:
        # Initialize training pipeline
        print("\n🚀 Initializing test training pipeline...")
        pipeline = TrainingPipeline(test_config_path)
        
        # Run training
        print("🎯 Starting test training...")
        summary = pipeline.run_training_pipeline()
        
        # Validate results
        print("\n📊 Test training results:")
        stats = summary['statistics']
        
        print(f"  Training success: {'✅' if stats['training_success'] else '❌'}")
        print(f"  Epochs completed: {stats['epochs_completed']}")
        print(f"  Training time: {stats['total_training_time']:.2f} seconds")
        
        if stats['best_validation_metrics']:
            print(f"  Best validation metrics:")
            for metric, value in stats['best_validation_metrics'].items():
                print(f"    {metric}: {value:.4f}")
        
        # Check if model checkpoint was saved
        if stats['final_model_path'] and os.path.exists(stats['final_model_path']):
            print(f"  ✅ Model checkpoint saved: {os.path.basename(stats['final_model_path'])}")
        else:
            print(f"  ⚠️  Model checkpoint not found")
        
        # Test model loading
        print("\n🔧 Testing model checkpoint loading...")
        if stats['final_model_path'] and os.path.exists(stats['final_model_path']):
            checkpoint = torch.load(stats['final_model_path'], map_location='cpu')
            print(f"  ✅ Checkpoint loaded successfully")
            print(f"  ✅ Epoch: {checkpoint['epoch']}")
            print(f"  ✅ Global step: {checkpoint['global_step']}")
        
        print("\n🎉 TEST TRAINING COMPLETED SUCCESSFULLY!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test training failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # Cleanup test configuration
        if os.path.exists(test_config_path):
            os.remove(test_config_path)


def main():
    """Main test execution."""
    
    print("🧪 STARTING PHASE 4 TRAINING TEST")
    print("=" * 80)
    
    try:
        success = test_short_training()
        
        if success:
            print("\n" + "=" * 80)
            print("✅ PHASE 4 TRAINING TEST PASSED!")
            print("🚀 Ready for full training execution")
            print("💡 Run: python scripts/run_phase_04.py")
            print("=" * 80)
            return 0
        else:
            print("\n" + "=" * 80)
            print("❌ PHASE 4 TRAINING TEST FAILED!")
            print("🔧 Check error messages above for troubleshooting")
            print("=" * 80)
            return 1
            
    except Exception as e:
        print(f"\n❌ Test execution failed: {str(e)}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
