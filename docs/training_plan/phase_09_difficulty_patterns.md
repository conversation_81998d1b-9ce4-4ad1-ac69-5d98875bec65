# 🧩 Phase 9: Difficulty-Aware Pattern Modeling

**Status**: 📋 Planned  
**Estimated Duration**: 4 days  
**Dependencies**: [Phase 8: Note Sequence Pattern Learning](phase_08_sequence_patterns.md)  
**Next Phase**: [Phase 10: Measure Segmentation & Bar Lines](phase_10_measure_segmentation.md)

---

## 1. **Phase Purpose**

This phase models how note patterns relate to difficulty levels (8-10) and generates patterns appropriate for target difficulty. This step is isolated because:

- **Difficulty modeling** requires understanding the relationship between patterns and player skill
- **Pattern complexity analysis** quantifies what makes patterns challenging
- **Adaptive generation** produces patterns matching specific difficulty targets
- **Quality control** ensures generated patterns are appropriate for the target skill level

**Why Isolated**: Difficulty modeling requires different expertise than general sequence learning, involving game design principles and player skill analysis. The difficulty-aware patterns are essential for generating playable charts at the correct skill level.

---

## 2. **Inputs & Outputs**

### **Inputs**
```python
# From Phase 8
generated_sequences: List[Dict]     # Generated note sequences
pattern_analysis: Dict              # Common patterns and transitions
sequence_model: torch.nn.Module     # Trained sequence model

# Difficulty training data (from TJA analysis)
difficulty_patterns: List[Dict] = [
    {
        "pattern": List[str],           # Note sequence pattern
        "difficulty_level": int,        # Chart difficulty (8-10)
        "complexity_metrics": {
            "note_density": float,      # Notes per second
            "pattern_length": int,      # Pattern length
            "hand_alternation": float,  # L-R hand alternation rate
            "subdivision_complexity": float, # Rhythmic complexity
            "big_note_ratio": float     # Ratio of big notes
        },
        "context": {
            "bpm": float,
            "position_in_song": float,  # 0-1, position in song
            "surrounding_patterns": List[str]
        }
    }
]

# Input directory structure
data\\processed\\phase8\\
├── generated_sequences\\*.json     # Generated sequences
├── pattern_analysis\\*.json        # Pattern analysis
├── models\\sequence_model.pth      # Sequence model
```

### **Outputs**
```python
# Difficulty prediction model
difficulty_model: Dict = {
    "model": torch.nn.Module,           # Difficulty classifier/regressor
    "feature_extractor": callable,      # Pattern feature extraction
    "difficulty_scaler": StandardScaler, # Difficulty score normalization
    "model_metadata": {
        "architecture": str,            # Model type
        "input_features": int,          # Feature dimensions
        "target_difficulties": List[int], # [8, 9, 10]
        "training_patterns": int,       # Training dataset size
        "accuracy": float,              # Classification accuracy
        "mae": float                   # Mean absolute error
    }
}

# Difficulty-aware generator
difficulty_generator: Dict = {
    "generator": torch.nn.Module,       # Conditional generation model
    "difficulty_embeddings": torch.nn.Embedding, # Difficulty conditioning
    "generation_params": {
        "temperature_by_difficulty": Dict, # Temperature settings per difficulty
        "pattern_constraints": Dict,    # Constraints per difficulty level
        "complexity_targets": Dict      # Target complexity metrics
    }
}

# Generated difficulty patterns (for Phase 13 compatibility)
difficulty_patterns: Dict = {
    "patterns": List[Dict],             # List of generated patterns
    "metadata": {
        "total_patterns": int,          # Total number of patterns generated
        "target_difficulty": int,       # Target difficulty level (8-10)
        "generation_timestamp": str,    # When patterns were generated
        "model_version": str,           # Model version used
        "quality_summary": {
            "average_quality_score": float,
            "patterns_above_threshold": int,
            "generation_success_rate": float
        }
    }
}

# Individual pattern structure within difficulty_patterns["patterns"]
pattern_structure: Dict = {
    "pattern_id": str,
    "target_difficulty": int,           # Requested difficulty (8-10)
    "generated_pattern": List[str],     # Generated note pattern
    "predicted_difficulty": float,     # Model's difficulty prediction
    "time_positions": List[float],      # Time positions for each note
    "complexity_analysis": {
        "note_density": float,
        "pattern_complexity": float,
        "hand_coordination": float,
        "rhythmic_difficulty": float,
        "overall_score": float
    },
    "quality_metrics": {
        "playability_score": float,     # How playable the pattern is
        "musical_coherence": float,     # Musical quality
        "difficulty_accuracy": float    # How close to target difficulty
    }
}

# Note sequences (for Phase 13 compatibility)
note_sequences: List[Dict] = [
    {
        "sequence_id": str,
        "notes": List[Dict],            # Sequential note data with timing
        "difficulty_level": int,        # Difficulty level for this sequence
        "total_duration": float,        # Sequence duration in seconds
        "note_count": int,              # Total notes in sequence
        "sequence_quality": float       # Overall sequence quality score
    }
]

# Output directory structure
data\\processed\\phase9\\
├── models\\
│   ├── difficulty_model.pth        # Difficulty prediction model
│   ├── difficulty_generator.pth    # Conditional generator
│   └── model_metadata.json         # Model configurations
├── difficulty_patterns\\*.json     # Generated patterns by difficulty
├── complexity_analysis\\*.json     # Pattern complexity analysis
├── training_data\\                 # Processed training data
│   ├── pattern_features.npy        # Pattern feature matrix
│   ├── difficulty_labels.npy       # Difficulty labels
│   └── difficulty_mapping.json     # Difficulty level mapping
└── difficulty_modeling_report.json # Training and evaluation results
```

---

## 3. **Implementation Plan**

### **Recommended Tools**
```python
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import classification_report, mean_absolute_error
from sklearn.ensemble import RandomForestRegressor
import json
import pickle
from pathlib import Path
from tqdm import tqdm
from typing import List, Dict, Tuple, Optional
from collections import Counter
import matplotlib.pyplot as plt
```

### **Pattern Complexity Analysis**
```python
def analyze_pattern_complexity(pattern: List[str], bpm: float = 120.0) -> Dict:
    """
    Analyze the complexity of a note pattern.
    
    Args:
        pattern: List of note types
        bpm: Beats per minute for timing context
        
    Returns:
        Dictionary with complexity metrics
    """
    
    if not pattern:
        return {
            "note_density": 0.0,
            "pattern_length": 0,
            "hand_alternation": 0.0,
            "subdivision_complexity": 0.0,
            "big_note_ratio": 0.0,
            "rest_ratio": 0.0,
            "pattern_entropy": 0.0
        }
    
    # 1. Basic metrics
    pattern_length = len(pattern)
    non_rest_notes = [note for note in pattern if note != "rest"]
    note_count = len(non_rest_notes)
    
    # Note density (notes per second, assuming 16th note subdivisions)
    pattern_duration = pattern_length / (4 * bmp / 60)  # 16th notes per beat
    note_density = note_count / pattern_duration if pattern_duration > 0 else 0
    
    # 2. Hand alternation analysis
    hand_alternation = calculate_hand_alternation(pattern)
    
    # 3. Subdivision complexity
    subdivision_complexity = calculate_subdivision_complexity(pattern)
    
    # 4. Note type ratios
    note_counts = Counter(pattern)
    total_notes = len(pattern)
    
    big_note_ratio = (note_counts.get("big_don", 0) + note_counts.get("big_ka", 0)) / total_notes
    rest_ratio = note_counts.get("rest", 0) / total_notes
    
    # 5. Pattern entropy (measure of unpredictability)
    pattern_entropy = calculate_pattern_entropy(pattern)
    
    # 6. Rhythmic complexity
    rhythmic_complexity = calculate_rhythmic_complexity(pattern)
    
    return {
        "note_density": float(note_density),
        "pattern_length": int(pattern_length),
        "hand_alternation": float(hand_alternation),
        "subdivision_complexity": float(subdivision_complexity),
        "big_note_ratio": float(big_note_ratio),
        "rest_ratio": float(rest_ratio),
        "pattern_entropy": float(pattern_entropy),
        "rhythmic_complexity": float(rhythmic_complexity)
    }

def calculate_hand_alternation(pattern: List[str]) -> float:
    """Calculate hand alternation rate (don=right, ka=left)."""
    
    # Map notes to hands (simplified)
    hand_mapping = {
        "don": "R", "big_don": "R",
        "ka": "L", "big_ka": "L",
        "rest": None
    }
    
    hands = [hand_mapping.get(note) for note in pattern if hand_mapping.get(note) is not None]
    
    if len(hands) < 2:
        return 0.0
    
    # Count alternations
    alternations = 0
    for i in range(1, len(hands)):
        if hands[i] != hands[i-1]:
            alternations += 1
    
    return alternations / (len(hands) - 1)

def calculate_subdivision_complexity(pattern: List[str]) -> float:
    """Calculate rhythmic subdivision complexity."""
    
    # Count consecutive rests to identify subdivision patterns
    rest_groups = []
    current_rest_count = 0
    
    for note in pattern:
        if note == "rest":
            current_rest_count += 1
        else:
            if current_rest_count > 0:
                rest_groups.append(current_rest_count)
                current_rest_count = 0
    
    if current_rest_count > 0:
        rest_groups.append(current_rest_count)
    
    # Analyze subdivision patterns
    if not rest_groups:
        return 1.0  # All notes, high complexity
    
    # Irregular rest patterns indicate higher complexity
    rest_variance = np.var(rest_groups) if len(rest_groups) > 1 else 0
    return min(1.0, rest_variance / 4.0)  # Normalize to 0-1

def calculate_pattern_entropy(pattern: List[str]) -> float:
    """Calculate Shannon entropy of the pattern."""
    
    if not pattern:
        return 0.0
    
    # Count occurrences
    counts = Counter(pattern)
    total = len(pattern)
    
    # Calculate entropy
    entropy = 0.0
    for count in counts.values():
        probability = count / total
        if probability > 0:
            entropy -= probability * np.log2(probability)
    
    # Normalize by maximum possible entropy
    max_entropy = np.log2(len(counts)) if len(counts) > 1 else 1.0
    return entropy / max_entropy

def calculate_rhythmic_complexity(pattern: List[str]) -> float:
    """Calculate rhythmic complexity based on note placement."""
    
    # Identify syncopated patterns (notes on weak beats)
    syncopation_score = 0.0
    
    for i, note in enumerate(pattern):
        if note != "rest":
            # Position within 4-beat measure (assuming 16th note subdivisions)
            beat_position = (i % 16) / 4.0
            
            # Weak beat positions (off-beats) increase complexity
            if beat_position % 1.0 != 0:  # Not on main beats
                syncopation_score += 1.0
            elif beat_position % 0.5 != 0:  # Not on half beats
                syncopation_score += 0.5
    
    # Normalize by pattern length
    return syncopation_score / len(pattern) if pattern else 0.0

class OptimizedDifficultyModel(nn.Module):
    """
    Optimized difficulty model with uncertainty estimation.

    Features:
    - Attention pooling for variable-length patterns
    - Uncertainty quantification with Gaussian NLL loss
    - Context encoder for musical features
    - Memory-efficient architecture for RTX 3070
    """

    def __init__(self, pattern_embed_dim: int = 64, context_dim: int = 32,
                 num_difficulties: int = 3, dropout_rates: List[float] = [0.2, 0.1, 0.3, 0.2]):
        super(OptimizedDifficultyModel, self).__init__()

        self.num_difficulties = num_difficulties

        # Pattern encoder with attention pooling
        self.pattern_encoder = nn.Sequential(
            nn.Linear(pattern_embed_dim, 128),
            nn.LayerNorm(128),
            nn.ReLU(),
            nn.Dropout(dropout_rates[0]),

            nn.Linear(128, 64),
            nn.LayerNorm(64),
            nn.ReLU(),
            nn.Dropout(dropout_rates[1])
        )

        # Context encoder for musical features
        self.context_encoder = nn.Sequential(
            nn.Linear(context_dim, 64),
            nn.LayerNorm(64),
            nn.ReLU(),
            nn.Dropout(dropout_rates[1]),

            nn.Linear(64, 32),
            nn.LayerNorm(32),
            nn.ReLU()
        )

        # Attention pooling for variable-length patterns
        self.attention_pool = nn.MultiheadAttention(
            embed_dim=64,
            num_heads=4,
            dropout=0.1,
            batch_first=True
        )

        # Difficulty predictor with uncertainty estimation
        self.difficulty_head = nn.Sequential(
            nn.Linear(64 + 32, 128),  # Pattern + context features
            nn.LayerNorm(128),
            nn.ReLU(),
            nn.Dropout(dropout_rates[2]),

            nn.Linear(128, 64),
            nn.LayerNorm(64),
            nn.ReLU(),
            nn.Dropout(dropout_rates[3]),

            nn.Linear(64, num_difficulties * 2)  # Mean and log variance for each difficulty
        )

        # Initialize weights
        self.apply(self._init_weights)

    def _init_weights(self, module):
        """Initialize model weights for better convergence."""
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)

    def forward(self, pattern_features, context_features, pattern_mask=None):
        # Encode patterns
        pattern_encoded = self.pattern_encoder(pattern_features)

        # Apply attention pooling
        if pattern_mask is not None:
            pattern_pooled, attention_weights = self.attention_pool(
                pattern_encoded, pattern_encoded, pattern_encoded,
                key_padding_mask=~pattern_mask
            )
        else:
            pattern_pooled, attention_weights = self.attention_pool(
                pattern_encoded, pattern_encoded, pattern_encoded
            )

        # Pool across sequence dimension
        pattern_pooled = pattern_pooled.mean(dim=1)

        # Encode context
        context_encoded = self.context_encoder(context_features)

        # Combine features
        combined_features = torch.cat([pattern_pooled, context_encoded], dim=-1)

        # Predict difficulty with uncertainty
        difficulty_output = self.difficulty_head(combined_features)

        # Split into mean and log variance
        difficulty_mean = difficulty_output[:, :self.num_difficulties]
        difficulty_log_var = difficulty_output[:, self.num_difficulties:]

        return difficulty_mean, difficulty_log_var, attention_weights

class ConditionalPatternGenerator(nn.Module):
    """Conditional generator for difficulty-aware patterns."""
    
    def __init__(self, vocab_size: int, difficulty_levels: int = 3, 
                 embedding_dim: int = 128, hidden_size: int = 256):
        super(ConditionalPatternGenerator, self).__init__()
        
        self.vocab_size = vocab_size
        self.difficulty_levels = difficulty_levels
        
        # Embeddings
        self.note_embedding = nn.Embedding(vocab_size, embedding_dim)
        self.difficulty_embedding = nn.Embedding(difficulty_levels, embedding_dim)
        
        # LSTM with conditional input
        self.lstm = nn.LSTM(embedding_dim * 2, hidden_size, batch_first=True)
        self.output_layer = nn.Linear(hidden_size, vocab_size)
    
    def forward(self, notes, difficulty, hidden=None):
        # Embed notes and difficulty
        note_emb = self.note_embedding(notes)
        diff_emb = self.difficulty_embedding(difficulty)
        
        # Expand difficulty embedding to match sequence length
        diff_emb = diff_emb.unsqueeze(1).expand(-1, note_emb.size(1), -1)
        
        # Concatenate embeddings
        combined_input = torch.cat([note_emb, diff_emb], dim=2)
        
        # LSTM forward pass
        lstm_out, hidden = self.lstm(combined_input, hidden)
        
        # Output layer
        output = self.output_layer(lstm_out)
        
        return output, hidden

def train_difficulty_model(
    pattern_features: np.ndarray,
    difficulty_labels: np.ndarray,
    validation_split: float = 0.2,
    batch_size: int = 32,
    num_epochs: int = 50,
    learning_rate: float = 0.001
) -> Dict:
    """
    Train difficulty prediction model.
    
    Args:
        pattern_features: Feature matrix for patterns
        difficulty_labels: Difficulty labels (0=level8, 1=level9, 2=level10)
        validation_split: Validation data fraction
        batch_size: Training batch size
        num_epochs: Number of training epochs
        learning_rate: Learning rate
        
    Returns:
        Trained model and results
    """
    
    # Split data
    X_train, X_val, y_train, y_val = train_test_split(
        pattern_features, difficulty_labels, 
        test_size=validation_split, random_state=42, stratify=difficulty_labels
    )
    
    # Normalize features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_val_scaled = scaler.transform(X_val)
    
    # Convert to tensors
    X_train_tensor = torch.FloatTensor(X_train_scaled)
    y_train_tensor = torch.LongTensor(y_train)
    X_val_tensor = torch.FloatTensor(X_val_scaled)
    y_val_tensor = torch.LongTensor(y_val)
    
    # Create datasets
    train_dataset = torch.utils.data.TensorDataset(X_train_tensor, y_train_tensor)
    val_dataset = torch.utils.data.TensorDataset(X_val_tensor, y_val_tensor)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False)
    
    # Initialize model
    input_size = pattern_features.shape[1]
    num_classes = len(np.unique(difficulty_labels))
    model = DifficultyClassifier(input_size, num_classes)
    
    # Loss and optimizer
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5)
    
    # Training loop
    train_losses = []
    val_losses = []
    val_accuracies = []
    best_val_acc = 0.0
    
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        
        for batch_features, batch_labels in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_features)
            loss = criterion(outputs, batch_labels)
            loss.backward()
            optimizer.step()
            train_loss += loss.item()
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        correct = 0
        total = 0
        
        with torch.no_grad():
            for batch_features, batch_labels in val_loader:
                outputs = model(batch_features)
                loss = criterion(outputs, batch_labels)
                val_loss += loss.item()
                
                _, predicted = torch.max(outputs.data, 1)
                total += batch_labels.size(0)
                correct += (predicted == batch_labels).sum().item()
        
        # Calculate metrics
        avg_train_loss = train_loss / len(train_loader)
        avg_val_loss = val_loss / len(val_loader)
        val_accuracy = correct / total
        
        train_losses.append(avg_train_loss)
        val_losses.append(avg_val_loss)
        val_accuracies.append(val_accuracy)
        
        # Learning rate scheduling
        scheduler.step(avg_val_loss)
        
        # Save best model
        if val_accuracy > best_val_acc:
            best_val_acc = val_accuracy
            best_model_state = model.state_dict().copy()
        
        # Print progress
        if (epoch + 1) % 10 == 0:
            print(f"Epoch [{epoch+1}/{num_epochs}], "
                  f"Train Loss: {avg_train_loss:.4f}, "
                  f"Val Loss: {avg_val_loss:.4f}, "
                  f"Val Acc: {val_accuracy:.4f}")
    
    # Load best model
    model.load_state_dict(best_model_state)
    
    return {
        "model": model,
        "scaler": scaler,
        "best_accuracy": best_val_acc,
        "training_history": {
            "train_losses": train_losses,
            "val_losses": val_losses,
            "val_accuracies": val_accuracies
        }
    }

def generate_difficulty_aware_pattern(
    generator: ConditionalPatternGenerator,
    tokenizer,
    target_difficulty: int,
    seed_pattern: List[str],
    pattern_length: int = 16,
    temperature: float = 0.8,
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
) -> List[str]:
    """
    Generate a pattern for a specific difficulty level.
    
    Args:
        generator: Trained conditional generator
        tokenizer: Note tokenizer
        target_difficulty: Target difficulty (8, 9, or 10)
        seed_pattern: Initial pattern to start generation
        pattern_length: Length of pattern to generate
        temperature: Generation temperature
        device: Generation device
        
    Returns:
        Generated pattern for target difficulty
    """
    
    generator.eval()
    
    # Map difficulty to class index
    difficulty_mapping = {8: 0, 9: 1, 10: 2}
    difficulty_class = difficulty_mapping.get(target_difficulty, 1)
    
    # Encode seed pattern
    current_sequence = tokenizer.encode(seed_pattern)
    generated_notes = []
    
    # Prepare difficulty tensor
    difficulty_tensor = torch.LongTensor([difficulty_class]).to(device)
    
    with torch.no_grad():
        for _ in range(pattern_length):
            # Prepare input
            input_seq = current_sequence[-16:]  # Last 16 tokens
            input_tensor = torch.LongTensor([input_seq]).to(device)
            
            # Generate next token
            output, _ = generator(input_tensor, difficulty_tensor)
            
            # Apply temperature and sample
            last_output = output[0, -1, :] / temperature
            probabilities = torch.softmax(last_output, dim=0)
            next_token = torch.multinomial(probabilities, 1).item()
            
            # Stop if END token
            if next_token == tokenizer.END_TOKEN:
                break
            
            # Add to sequence
            current_sequence.append(next_token)
            
            # Decode and add to generated notes
            if next_token not in [tokenizer.PAD_TOKEN, tokenizer.START_TOKEN, tokenizer.UNK_TOKEN]:
                note = tokenizer.token_to_note[next_token]
                generated_notes.append(note)
    
    return generated_notes

def validate_difficulty_appropriateness(
    pattern: List[str],
    target_difficulty: int,
    difficulty_model,
    feature_scaler
) -> Dict:
    """
    Validate if a generated pattern matches the target difficulty.
    
    Args:
        pattern: Generated note pattern
        target_difficulty: Target difficulty level
        difficulty_model: Trained difficulty classifier
        feature_scaler: Feature normalization scaler
        
    Returns:
        Validation results
    """
    
    # Extract pattern features
    complexity_metrics = analyze_pattern_complexity(pattern)
    features = np.array(list(complexity_metrics.values())).reshape(1, -1)
    
    # Normalize features
    features_scaled = feature_scaler.transform(features)
    
    # Predict difficulty
    with torch.no_grad():
        features_tensor = torch.FloatTensor(features_scaled)
        output = difficulty_model(features_tensor)
        probabilities = torch.softmax(output, dim=1)
        predicted_class = torch.argmax(probabilities, dim=1).item()
        confidence = torch.max(probabilities, dim=1)[0].item()
    
    # Map class back to difficulty
    class_to_difficulty = {0: 8, 1: 9, 2: 10}
    predicted_difficulty = class_to_difficulty[predicted_class]
    
    # Calculate accuracy
    difficulty_accuracy = 1.0 - abs(predicted_difficulty - target_difficulty) / 2.0
    
    return {
        "predicted_difficulty": predicted_difficulty,
        "target_difficulty": target_difficulty,
        "prediction_confidence": float(confidence),
        "difficulty_accuracy": float(difficulty_accuracy),
        "complexity_metrics": complexity_metrics
    }
```

---

## 4. **Best Practices**

### **Difficulty Calibration**
- Use expert-validated difficulty ratings for training
- Consider multiple difficulty aspects (technical, physical, musical)
- Implement difficulty consistency checks across patterns
- Validate against human player performance data

### **Feature Engineering**
```python
# Comprehensive difficulty features
def extract_difficulty_features(pattern: List[str], context: Dict) -> np.ndarray:
    """Extract features relevant to difficulty assessment."""
    
    # Basic complexity
    complexity = analyze_pattern_complexity(pattern, context.get("bpm", 120))
    
    # Contextual features
    contextual_features = [
        context.get("bpm", 120) / 200.0,  # Normalized BPM
        context.get("position_in_song", 0.5),  # Song position
        len(pattern) / 32.0  # Normalized pattern length
    ]
    
    # Combine all features
    all_features = list(complexity.values()) + contextual_features
    return np.array(all_features, dtype=np.float32)
```

### **Generation Control**
- Use different temperature settings per difficulty level
- Implement pattern constraints (max consecutive notes, etc.)
- Add post-processing filters for difficulty validation
- Consider player fatigue and recovery patterns

### **Quality Assurance**
- Validate generated patterns against difficulty targets
- Check for playability issues (impossible patterns)
- Ensure musical coherence is maintained
- Test with actual players when possible

---

## 5. **Challenges & Pitfalls**

### **Subjective Difficulty**
- **Issue**: Difficulty perception varies between players
- **Example**: Some players find fast alternating patterns harder than complex rhythms
- **Mitigation**: Use multiple difficulty metrics and expert consensus
- **Solution**: Implement player-adaptive difficulty assessment

### **Difficulty Consistency**
- **Issue**: Generated patterns may have inconsistent difficulty within a chart
- **Symptoms**: Easy patterns mixed with very hard patterns randomly
- **Mitigation**: Use difficulty progression curves and context awareness
- **Solution**: Implement chart-level difficulty planning

### **Pattern Playability**
- **Issue**: Technically correct patterns may be unplayable or uncomfortable
- **Example**: Rapid hand movements that are physically impossible
- **Mitigation**: Include ergonomic constraints in generation
- **Solution**: Use biomechanical models for playability validation

### **Overfitting to Training Data**
- **Issue**: Model may memorize specific patterns rather than learning difficulty principles
- **Symptoms**: Generated patterns too similar to training examples
- **Mitigation**: Use regularization and diverse training data
- **Solution**: Implement pattern novelty metrics and encourage diversity

---

## 5.5. **Iterative Training Strategy**

### **Multi-Execution Framework for Difficulty-Aware Pattern Modeling**

Difficulty-aware pattern modeling benefits from specialized training approaches for different difficulty ranges, with iterative refinement to handle extreme difficulty charts and edge cases.

#### **Iterative Difficulty Modeling Architecture**
```python
class IterativeDifficultyTrainer:
    """
    Iterative training framework for difficulty-aware pattern modeling.

    Features:
    - Multi-head architecture for difficulty-specific fine-tuning
    - Separate sub-models for different difficulty ranges
    - Embedding-based difficulty representation
    - Extreme difficulty (Level 10+) specialized handling
    """

    def __init__(self, config: Dict):
        self.config = config
        self.paths = TJAGenPaths()
        self.logger = TJAGenLogger(9)

        # Iteration management
        self.current_iteration = 0
        self.max_iterations = config.get("max_iterations", 4)
        self.improvement_threshold = config.get("improvement_threshold", 0.02)

        # Multi-head model architecture
        self.models = {
            "shared_encoder": None,        # Shared feature encoder
            "difficulty_heads": {},        # Difficulty-specific heads
            "extreme_difficulty_model": None, # Specialized model for Level 10+
            "ensemble_combiner": None      # Ensemble combination model
        }

        # Difficulty range organization
        self.difficulty_ranges = {
            "beginner": {"range": [1, 3], "focus": "basic_patterns"},
            "intermediate": {"range": [4, 6], "focus": "moderate_complexity"},
            "advanced": {"range": [7, 8], "focus": "complex_patterns"},
            "expert": {"range": [9, 10], "focus": "extreme_patterns"},
            "extreme": {"range": [10, 11], "focus": "inhuman_patterns"}
        }

        # Training data organization
        self.training_data = {
            "by_difficulty_range": {},     # Data organized by difficulty ranges
            "extreme_difficulty": [],      # Level 10+ charts
            "difficulty_transitions": [], # Charts spanning multiple difficulties
            "embedding_data": []           # Data for difficulty embedding learning
        }

        # Performance tracking by difficulty
        self.difficulty_performance = {}
        for range_name in self.difficulty_ranges:
            self.difficulty_performance[range_name] = {
                "mae": float('inf'),
                "correlation": 0.0,
                "samples": 0
            }

        # Iteration tracking
        self.iteration_history = []
        self.best_overall_mae = float('inf')
        self.best_correlation = 0.0

        # Setup iteration directories
        self._setup_iteration_directories()

    def _setup_iteration_directories(self):
        """Create directories for iterative difficulty training."""
        base_dir = self.paths.get_phase_output_dir(9)

        directories = [
            base_dir / "iterations",
            base_dir / "iterations" / "difficulty_heads",
            base_dir / "iterations" / "extreme_models",
            base_dir / "iterations" / "embeddings",
            base_dir / "iterations" / "ensemble_models",
            base_dir / "iterations" / "difficulty_analysis"
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)

    def execute_iterative_difficulty_training(self, initial_data: Dict) -> Dict:
        """
        Execute complete iterative training pipeline for difficulty modeling.

        Args:
            initial_data: Initial difficulty training data

        Returns:
            Final training results with difficulty-specific performance
        """

        self.logger.info("🔄 Starting iterative difficulty modeling training")

        # Organize data by difficulty ranges
        self._organize_difficulty_data(initial_data)

        iteration_results = []

        for iteration in range(self.max_iterations):
            self.current_iteration = iteration
            self.logger.info(f"🔄 Starting iteration {iteration + 1}/{self.max_iterations}")

            # Execute single iteration
            iteration_result = self._execute_difficulty_iteration()
            iteration_results.append(iteration_result)

            # Update iteration history
            self.iteration_history.append(iteration_result)

            # Check for convergence
            if self._check_difficulty_convergence(iteration_result):
                self.logger.info(f"✅ Difficulty modeling converged after {iteration + 1} iterations")
                break

            # Prepare for next iteration
            self._prepare_next_difficulty_iteration(iteration_result)

        # Generate final results
        final_results = self._generate_final_difficulty_results(iteration_results)

        self.logger.info("🎉 Iterative difficulty training completed")
        return final_results

    def _execute_difficulty_iteration(self) -> Dict:
        """Execute a single difficulty modeling iteration."""

        iteration_start_time = time.time()

        # 1. Train difficulty embedding representation
        embedding_results = self._train_difficulty_embeddings()

        # 2. Train shared encoder with difficulty conditioning
        encoder_results = self._train_shared_encoder(embedding_results)

        # 3. Train difficulty-specific heads
        head_results = self._train_difficulty_heads(encoder_results)

        # 4. Train extreme difficulty specialist model
        extreme_results = self._train_extreme_difficulty_model()

        # 5. Train ensemble combiner
        ensemble_results = self._train_ensemble_combiner(head_results, extreme_results)

        # 6. Evaluate difficulty-specific performance
        performance_metrics = self._evaluate_difficulty_performance(ensemble_results)

        # 7. Analyze difficulty prediction errors
        error_analysis = self._analyze_difficulty_errors(performance_metrics)

        # 8. Save iteration artifacts
        self._save_difficulty_iteration_artifacts(
            embedding_results, head_results, extreme_results,
            ensemble_results, performance_metrics
        )

        iteration_time = time.time() - iteration_start_time

        return {
            "iteration": self.current_iteration,
            "training_time": iteration_time,
            "embedding_results": embedding_results,
            "head_results": head_results,
            "extreme_results": extreme_results,
            "ensemble_results": ensemble_results,
            "performance_metrics": performance_metrics,
            "error_analysis": error_analysis,
            "overall_mae": performance_metrics.get("overall_mae", float('inf')),
            "overall_correlation": performance_metrics.get("overall_correlation", 0.0)
        }

    def _train_difficulty_embeddings(self) -> Dict:
        """Train difficulty embedding representations."""

        self.logger.info("🧠 Training difficulty embeddings")

        # Create difficulty embedding model
        embedding_model = DifficultyEmbeddingModel(
            num_difficulties=11,  # Difficulties 1-11
            embedding_dim=self.config.get("embedding_dim", 64),
            context_features=self.config.get("context_features", 32)
        )

        # Prepare embedding training data
        embedding_data = []

        for range_name, range_config in self.difficulty_ranges.items():
            range_data = self.training_data["by_difficulty_range"].get(range_name, [])

            for sample in range_data:
                embedding_sample = {
                    "difficulty_level": sample["difficulty"],
                    "pattern_features": sample["pattern_features"],
                    "context_features": sample["context_features"],
                    "range_label": range_name
                }
                embedding_data.append(embedding_sample)

        # Train embedding model
        embedding_results = self._execute_embedding_training(embedding_model, embedding_data)

        # Store trained embedding model
        self.models["difficulty_embeddings"] = embedding_model

        return embedding_results

    def _train_difficulty_heads(self, encoder_results: Dict) -> Dict:
        """Train difficulty-specific prediction heads."""

        head_results = {}

        for range_name, range_config in self.difficulty_ranges.items():
            self.logger.info(f"🎯 Training {range_name} difficulty head")

            # Get data for this difficulty range
            range_data = self.training_data["by_difficulty_range"].get(range_name, [])

            if not range_data:
                self.logger.warning(f"No data available for {range_name} range")
                continue

            # Create difficulty-specific head
            difficulty_head = DifficultySpecificHead(
                input_dim=encoder_results["encoder_output_dim"],
                difficulty_range=range_config["range"],
                focus_type=range_config["focus"]
            )

            # Train head with range-specific data
            head_training_results = self._train_single_difficulty_head(
                difficulty_head, range_data, range_name
            )

            # Store trained head
            self.models["difficulty_heads"][range_name] = difficulty_head
            head_results[range_name] = head_training_results

        return head_results

    def _train_extreme_difficulty_model(self) -> Dict:
        """Train specialized model for extreme difficulty charts (Level 10+)."""

        self.logger.info("🔥 Training extreme difficulty specialist model")

        # Get extreme difficulty data
        extreme_data = self.training_data.get("extreme_difficulty", [])

        if len(extreme_data) < 50:  # Need minimum samples for training
            self.logger.warning("Insufficient extreme difficulty data for specialized training")
            return {"trained": False, "reason": "insufficient_data"}

        # Create specialized extreme difficulty model
        extreme_model = ExtremeDifficultyModel(
            input_features=self.config.get("input_features", 128),
            specialized_layers=self.config.get("extreme_model_layers", 4),
            attention_heads=self.config.get("extreme_attention_heads", 8)
        )

        # Apply specialized training techniques for extreme difficulty
        extreme_training_config = {
            "loss_function": "huber_loss",  # More robust to outliers
            "learning_rate": 0.0001,        # Lower LR for stability
            "batch_size": 16,               # Smaller batches
            "epochs": 200,                  # More epochs for convergence
            "early_stopping_patience": 30,
            "gradient_clipping": 1.0
        }

        # Execute extreme difficulty training
        extreme_results = self._execute_extreme_training(
            extreme_model, extreme_data, extreme_training_config
        )

        # Store extreme model
        self.models["extreme_difficulty_model"] = extreme_model

        return extreme_results

    def _train_ensemble_combiner(self, head_results: Dict, extreme_results: Dict) -> Dict:
        """Train ensemble model to combine difficulty-specific predictions."""

        self.logger.info("🎭 Training ensemble combiner")

        # Create ensemble combiner model
        ensemble_model = DifficultyEnsembleCombiner(
            num_heads=len(self.models["difficulty_heads"]),
            has_extreme_model=extreme_results.get("trained", False),
            combination_strategy=self.config.get("ensemble_strategy", "weighted_average")
        )

        # Prepare ensemble training data
        ensemble_data = self._prepare_ensemble_training_data(head_results, extreme_results)

        # Train ensemble combiner
        ensemble_results = self._execute_ensemble_training(ensemble_model, ensemble_data)

        # Store ensemble model
        self.models["ensemble_combiner"] = ensemble_model

        return ensemble_results

    def _evaluate_difficulty_performance(self, ensemble_results: Dict) -> Dict:
        """Evaluate performance across different difficulty ranges."""

        performance_metrics = {
            "overall_mae": 0.0,
            "overall_correlation": 0.0,
            "range_specific_performance": {},
            "extreme_difficulty_performance": {},
            "prediction_distribution": {},
            "error_patterns": []
        }

        # Evaluate each difficulty range
        total_samples = 0
        total_mae = 0.0
        all_predictions = []
        all_targets = []

        for range_name, range_config in self.difficulty_ranges.items():
            if range_name not in self.models["difficulty_heads"]:
                continue

            # Get validation data for this range
            range_val_data = self._get_range_validation_data(range_name)

            if not range_val_data:
                continue

            # Evaluate range-specific performance
            range_metrics = self._evaluate_range_performance(range_name, range_val_data)

            performance_metrics["range_specific_performance"][range_name] = range_metrics

            # Accumulate for overall metrics
            total_samples += range_metrics["sample_count"]
            total_mae += range_metrics["mae"] * range_metrics["sample_count"]
            all_predictions.extend(range_metrics["predictions"])
            all_targets.extend(range_metrics["targets"])

        # Calculate overall metrics
        if total_samples > 0:
            performance_metrics["overall_mae"] = total_mae / total_samples
            performance_metrics["overall_correlation"] = np.corrcoef(
                all_predictions, all_targets
            )[0, 1] if len(all_predictions) > 1 else 0.0

        # Evaluate extreme difficulty performance separately
        if self.models["extreme_difficulty_model"] is not None:
            extreme_metrics = self._evaluate_extreme_difficulty_performance()
            performance_metrics["extreme_difficulty_performance"] = extreme_metrics

        return performance_metrics

# Iterative difficulty training configuration
ITERATIVE_DIFFICULTY_CONFIG = {
    "max_iterations": 4,
    "improvement_threshold": 0.02,  # 2% improvement threshold
    "target_mae": 0.5,              # Target MAE threshold
    "target_correlation": 0.75,     # Target correlation threshold

    # Difficulty range configuration
    "difficulty_ranges": {
        "beginner": {"range": [1, 3], "weight": 1.0},
        "intermediate": {"range": [4, 6], "weight": 1.2},
        "advanced": {"range": [7, 8], "weight": 1.5},
        "expert": {"range": [9, 10], "weight": 2.0},
        "extreme": {"range": [10, 11], "weight": 3.0}
    },

    # Model architecture configuration
    "model_architecture": {
        "shared_encoder_layers": 3,
        "difficulty_head_layers": 2,
        "extreme_model_layers": 4,
        "embedding_dim": 64,
        "attention_heads": 8
    },

    # Training configuration per range
    "range_training_configs": {
        "beginner": {"lr": 0.001, "batch_size": 64, "epochs": 100},
        "intermediate": {"lr": 0.0008, "batch_size": 48, "epochs": 120},
        "advanced": {"lr": 0.0005, "batch_size": 32, "epochs": 150},
        "expert": {"lr": 0.0003, "batch_size": 24, "epochs": 200},
        "extreme": {"lr": 0.0001, "batch_size": 16, "epochs": 300}
    },

    # Retraining triggers
    "retraining_triggers": {
        "mae_increase": 0.1,            # Retrain if MAE increases by 0.1
        "correlation_drop": 0.05,       # Retrain if correlation drops by 5%
        "extreme_difficulty_threshold": 50, # Retrain when 50+ extreme samples available
        "range_imbalance_threshold": 0.2 # Retrain if range performance differs by >20%
    }
}
```

#### **Multi-Execution Workflow for Difficulty Modeling**

```mermaid
graph TD
    A[Initial Difficulty Data] --> B[Iteration 0: Basic Range Training]
    B --> C[Train Difficulty Embeddings]
    C --> D[Train Shared Encoder]
    D --> E[Train Range-Specific Heads]
    E --> F[Train Extreme Difficulty Model]
    F --> G[Train Ensemble Combiner]
    G --> H[Evaluate Range Performance]
    H --> I{Convergence Check}
    I -->|No| J[Iteration N+1: Specialized Training]
    I -->|Yes| K[Final Model Selection]
    J --> L[Update Training Data]
    L --> M[Refine Range-Specific Models]
    M --> C
    K --> N[Deploy Best Model]

    style A fill:#e1f5fe
    style K fill:#c8e6c9
    style N fill:#c8e6c9
```

#### **Difficulty Modeling Performance Targets**

```python
# Expected improvement targets per iteration
DIFFICULTY_ITERATION_TARGETS = {
    "iteration_0": {
        "baseline_mae": 0.8,
        "target_mae": 0.65,
        "focus": "Basic difficulty range separation"
    },
    "iteration_1": {
        "baseline_mae": 0.65,
        "target_mae": 0.55,
        "focus": "Range-specific head optimization"
    },
    "iteration_2": {
        "baseline_mae": 0.55,
        "target_mae": 0.50,
        "focus": "Extreme difficulty handling"
    },
    "iteration_3": {
        "baseline_mae": 0.50,
        "target_mae": 0.45,
        "focus": "Ensemble refinement and edge cases"
    }
}

# Quality gates for iterative difficulty training
DIFFICULTY_QUALITY_GATES = {
    "max_overall_mae": 0.5,            # Maximum acceptable MAE
    "min_overall_correlation": 0.75,   # Minimum correlation required
    "max_range_mae_variance": 0.2,     # Max variance in MAE across ranges
    "min_extreme_accuracy": 0.7,       # Min accuracy for extreme difficulty
    "max_prediction_bias": 0.1         # Max systematic prediction bias
}
```

---

## 6. **Dependencies & Unlocks**

### **Prerequisites**
- **Phase 8 Complete**: Sequence patterns and generation models
- **Required Files**:
  - `data\\processed\\phase8\\generated_sequences\\*.json`
  - `data\\processed\\phase8\\pattern_analysis\\*.json`
  - `data\\raw\\ese\\**\\*.tja` (for difficulty labels)
- **Libraries**: `torch`, `sklearn` for difficulty modeling

### **What This Phase Unlocks**
- **Phase 10**: Difficulty-aware patterns improve measure segmentation
- **Phase 13**: Appropriate difficulty patterns for chart assembly
- **Quality Control**: Ensures generated charts match target difficulty
- **User Experience**: Players receive charts appropriate for their skill level

### **Output Dependencies**
Subsequent phases depend on these Phase 9 outputs:
- `data\\processed\\phase9\\models\\difficulty_model.pth` - Difficulty prediction model
- `data\\processed\\phase9\\difficulty_patterns\\*.json` - Difficulty-appropriate patterns
- `data\\processed\\phase9\\complexity_analysis\\*.json` - Pattern complexity analysis

---

## 7. **Validation Strategy**

### **Unit Tests**
```python
def test_complexity_analysis():
    """Test pattern complexity analysis."""
    # Simple pattern
    simple_pattern = ["don", "rest", "ka", "rest"]
    complexity = analyze_pattern_complexity(simple_pattern, 120)
    
    assert 0 <= complexity["note_density"] <= 10
    assert 0 <= complexity["hand_alternation"] <= 1
    assert complexity["pattern_length"] == 4

def test_difficulty_prediction():
    """Test difficulty prediction functionality."""
    # Create test features
    features = np.random.randn(10, 8).astype(np.float32)
    labels = np.array([0, 1, 2, 0, 1, 2, 0, 1, 2, 0])  # Difficulty classes
    
    # Train simple model
    results = train_difficulty_model(features, labels, num_epochs=5)
    
    assert results["best_accuracy"] >= 0.0
    assert results["model"] is not None
```

### **Quality Metrics**
- **Difficulty Accuracy**: >80% of generated patterns match target difficulty ±0.5 levels
- **Pattern Playability**: >95% of patterns pass playability validation
- **Complexity Consistency**: Pattern complexity variance <0.2 within difficulty level
- **Generation Speed**: <100ms per pattern on RTX 3070

### **Expert Validation**
```python
def expert_validation_study(generated_patterns: List[Dict], expert_ratings: List[Dict]) -> Dict:
    """Compare generated patterns with expert difficulty ratings."""
    
    results = {
        "correlation": 0.0,
        "mean_absolute_error": 0.0,
        "agreement_rate": 0.0,
        "expert_feedback": []
    }
    
    # Calculate correlation between predicted and expert ratings
    predicted_difficulties = [p["predicted_difficulty"] for p in generated_patterns]
    expert_difficulties = [r["difficulty"] for r in expert_ratings]
    
    correlation = np.corrcoef(predicted_difficulties, expert_difficulties)[0, 1]
    mae = np.mean(np.abs(np.array(predicted_difficulties) - np.array(expert_difficulties)))
    
    # Agreement within ±0.5 difficulty levels
    agreements = sum(1 for p, e in zip(predicted_difficulties, expert_difficulties) 
                    if abs(p - e) <= 0.5)
    agreement_rate = agreements / len(predicted_difficulties)
    
    results.update({
        "correlation": float(correlation),
        "mean_absolute_error": float(mae),
        "agreement_rate": float(agreement_rate)
    })
    
    return results
```

### **Example Success Case**
```python
# Expected difficulty_patterns format (for Phase 13 compatibility)
difficulty_patterns = {
    "patterns": [
        {
            "pattern_id": "diff9_001",
            "target_difficulty": 9,
            "generated_pattern": ["don", "ka", "don", "don", "ka", "rest", "don", "ka", "ka", "don"],
            "predicted_difficulty": 8.7,
            "time_positions": [0.0, 0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0, 2.25],
            "complexity_analysis": {
                "note_density": 3.2,
                "pattern_complexity": 0.75,
                "hand_coordination": 0.68,
                "rhythmic_difficulty": 0.71,
                "overall_score": 0.73
            },
            "quality_metrics": {
                "playability_score": 0.92,
                "musical_coherence": 0.85,
                "difficulty_accuracy": 0.85
            }
        }
    ],
    "metadata": {
        "total_patterns": 1,
        "target_difficulty": 9,
        "generation_timestamp": "2024-01-15T10:30:00Z",
        "model_version": "v1.0",
        "quality_summary": {
            "average_quality_score": 0.87,
            "patterns_above_threshold": 1,
            "generation_success_rate": 1.0
        }
    }
}

# Expected note_sequences format (for Phase 13 compatibility)
note_sequences = [
    {
        "sequence_id": "seq_001",
        "notes": [
            {"time": 0.0, "note_type": "don", "confidence": 0.92},
            {"time": 0.25, "note_type": "ka", "confidence": 0.88},
            {"time": 0.5, "note_type": "don", "confidence": 0.91}
        ],
        "difficulty_level": 9,
        "total_duration": 2.5,
        "note_count": 10,
        "sequence_quality": 0.87
    }
]

# Expected file outputs:
# data\\processed\\phase9\\difficulty_patterns\\Lemon.json - Complete difficulty_patterns Dict
# data\\processed\\phase9\\note_sequences\\Lemon.json - List of note_sequences Dicts

# Expected training results
training_results = {
    "best_accuracy": 0.82,
    "training_patterns": 8420,
    "difficulty_distribution": {8: 0.35, 9: 0.40, 10: 0.25},
    "feature_importance": {
        "note_density": 0.28,
        "hand_alternation": 0.22,
        "rhythmic_complexity": 0.19,
        "pattern_entropy": 0.15,
        "subdivision_complexity": 0.16
    }
}
```

---

**Phase 9 Complete**. This phase enables the generation of patterns appropriate for specific difficulty levels, ensuring that generated charts provide the right challenge level for target players.

**Next**: [Phase 10: Measure Segmentation & Bar Lines](phase_10_measure_segmentation.md)
