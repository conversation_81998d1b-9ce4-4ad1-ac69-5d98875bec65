"""
Phase 4 Setup Validation Script
Validates that all components are ready for training pipeline execution.
"""

import sys
import os
import torch
import yaml
import json
import numpy as np
from pathlib import Path
import logging

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.phases.phase_04_training_pipeline import TrainingPipeline
from src.data.tja_dataset import TJADataset, create_data_loaders


def validate_configuration():
    """Validate Phase 4 configuration file."""
    
    print("🔍 VALIDATING PHASE 4 CONFIGURATION")
    print("=" * 60)
    
    config_path = "configs/phase_04_config.yaml"
    
    if not os.path.exists(config_path):
        print(f"❌ Configuration file not found: {config_path}")
        return False
    
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Check required sections
        required_sections = [
            'paths', 'training', 'loss', 'data', 'memory',
            'monitoring', 'quality_gates', 'hardware', 'experiment'
        ]
        
        for section in required_sections:
            if section not in config:
                print(f"❌ Missing configuration section: {section}")
                return False
        
        print("✅ Configuration file structure valid")
        
        # Validate specific parameters
        training_config = config['training']
        
        # Check model variant
        primary_model = training_config['primary_model']
        if primary_model not in ['lightweight', 'standard', 'enhanced']:
            print(f"❌ Invalid primary model: {primary_model}")
            return False
        
        print(f"✅ Primary model: {primary_model}")
        
        # Check batch size
        batch_size = training_config['batch_size']
        if batch_size <= 0 or batch_size > 32:
            print(f"⚠️  Unusual batch size: {batch_size}")
        else:
            print(f"✅ Batch size: {batch_size}")
        
        # Check memory constraints
        max_vram = config['memory']['max_vram_usage_gb']
        if max_vram > 8.0:
            print(f"⚠️  VRAM limit exceeds RTX 3070 capacity: {max_vram}GB")
        else:
            print(f"✅ VRAM limit: {max_vram}GB")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {str(e)}")
        return False


def validate_data_pipeline():
    """Validate data loading and processing pipeline."""
    
    print("\n🔍 VALIDATING DATA PIPELINE")
    print("=" * 60)
    
    try:
        # Load configuration
        config_path = "configs/phase_04_config.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Test dataset creation
        print("Testing dataset creation...")
        
        # Create small test dataset
        train_dataset = TJADataset(config, split='train')
        val_dataset = TJADataset(config, split='val')
        
        print(f"✅ Train dataset: {len(train_dataset)} samples")
        print(f"✅ Validation dataset: {len(val_dataset)} samples")
        
        # Test sample loading
        print("Testing sample loading...")
        
        if len(train_dataset) > 0:
            sample = train_dataset[0]
            
            print(f"✅ Sample input shape: {sample['input'].shape}")
            print(f"✅ Sample target shape: {sample['target'].shape}")
            print(f"✅ Sample metadata: {sample['metadata']['filename']}")
            
            # Validate shapes
            expected_input_shape = (
                config['data']['input_sequence_length'],
                config['data']['n_mels']
            )
            expected_target_shape = (config['data']['output_sequence_length'],)
            
            if sample['input'].shape != expected_input_shape:
                print(f"❌ Input shape mismatch: {sample['input'].shape} != {expected_input_shape}")
                return False
            
            if sample['target'].shape != expected_target_shape:
                print(f"❌ Target shape mismatch: {sample['target'].shape} != {expected_target_shape}")
                return False
            
            print("✅ Sample shapes validated")
        else:
            print("❌ No samples in training dataset")
            return False
        
        # Test data loaders
        print("Testing data loaders...")
        
        train_loader, val_loader = create_data_loaders(config)
        
        print(f"✅ Train loader: {len(train_loader)} batches")
        print(f"✅ Validation loader: {len(val_loader)} batches")
        
        # Test batch loading
        if len(train_loader) > 0:
            batch = next(iter(train_loader))
            
            batch_size = config['training']['batch_size']
            expected_input_shape = (
                batch_size,
                config['data']['input_sequence_length'],
                config['data']['n_mels']
            )
            expected_target_shape = (
                batch_size,
                config['data']['output_sequence_length']
            )
            
            print(f"✅ Batch input shape: {batch['input'].shape}")
            print(f"✅ Batch target shape: {batch['target'].shape}")
            
            if batch['input'].shape != expected_input_shape:
                print(f"❌ Batch input shape mismatch: {batch['input'].shape} != {expected_input_shape}")
                return False
            
            if batch['target'].shape != expected_target_shape:
                print(f"❌ Batch target shape mismatch: {batch['target'].shape} != {expected_target_shape}")
                return False
            
            print("✅ Batch shapes validated")
        
        return True
        
    except Exception as e:
        print(f"❌ Data pipeline validation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def validate_model_integration():
    """Validate model loading and integration."""
    
    print("\n🔍 VALIDATING MODEL INTEGRATION")
    print("=" * 60)
    
    try:
        # Load configuration
        config_path = "configs/phase_04_config.yaml"
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Initialize pipeline
        pipeline = TrainingPipeline(config_path)
        
        # Test model loading
        primary_model = config['training']['primary_model']
        print(f"Testing {primary_model} model loading...")
        
        model = pipeline.load_model(primary_model)
        
        print(f"✅ Model loaded successfully")
        print(f"✅ Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # Test model forward pass
        print("Testing model forward pass...")
        
        # Create dummy input
        batch_size = config['training']['batch_size']
        seq_len = config['data']['input_sequence_length']
        n_mels = config['data']['n_mels']
        
        dummy_input = torch.randn(batch_size, seq_len, n_mels)
        
        model.eval()
        with torch.no_grad():
            output = model(dummy_input)
        
        expected_output_shape = (
            batch_size,
            config['data']['output_sequence_length'],
            5  # Number of TJA note classes
        )
        
        print(f"✅ Model output shape: {output.shape}")
        
        if output.shape != expected_output_shape:
            print(f"❌ Output shape mismatch: {output.shape} != {expected_output_shape}")
            return False
        
        print("✅ Model forward pass validated")
        
        # Test memory usage
        if torch.cuda.is_available():
            model = model.cuda()
            dummy_input = dummy_input.cuda()
            
            torch.cuda.reset_peak_memory_stats()
            
            with torch.no_grad():
                output = model(dummy_input)
            
            peak_memory = torch.cuda.max_memory_allocated() / 1e9
            print(f"✅ Peak GPU memory usage: {peak_memory:.3f} GB")
            
            max_vram = config['memory']['max_vram_usage_gb']
            if peak_memory > max_vram:
                print(f"⚠️  Memory usage exceeds limit: {peak_memory:.3f}GB > {max_vram}GB")
            else:
                print(f"✅ Memory usage within limits")
        
        return True
        
    except Exception as e:
        print(f"❌ Model integration validation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def validate_hardware_setup():
    """Validate hardware configuration."""
    
    print("\n🔍 VALIDATING HARDWARE SETUP")
    print("=" * 60)
    
    # Check CUDA availability
    if torch.cuda.is_available():
        device_count = torch.cuda.device_count()
        print(f"✅ CUDA available with {device_count} device(s)")
        
        for i in range(device_count):
            props = torch.cuda.get_device_properties(i)
            print(f"  Device {i}: {props.name}")
            print(f"    Memory: {props.total_memory / 1e9:.1f} GB")
            print(f"    Compute Capability: {props.major}.{props.minor}")
        
        # Test CUDA operations
        try:
            x = torch.randn(100, 100).cuda()
            y = torch.randn(100, 100).cuda()
            z = torch.matmul(x, y)
            print("✅ CUDA operations working")
        except Exception as e:
            print(f"❌ CUDA operations failed: {str(e)}")
            return False
    else:
        print("⚠️  CUDA not available - training will use CPU")
    
    # Check PyTorch version
    print(f"✅ PyTorch version: {torch.__version__}")
    
    # Check available memory
    if torch.cuda.is_available():
        free_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
        print(f"✅ GPU memory available: {free_memory:.1f} GB")
    
    return True


def main():
    """Main validation execution."""
    
    print("🚀 STARTING PHASE 4 SETUP VALIDATION")
    print("=" * 80)
    
    validation_results = []
    
    # Run all validations
    validation_results.append(("Configuration", validate_configuration()))
    validation_results.append(("Data Pipeline", validate_data_pipeline()))
    validation_results.append(("Model Integration", validate_model_integration()))
    validation_results.append(("Hardware Setup", validate_hardware_setup()))
    
    # Summary
    print("\n" + "=" * 80)
    print("PHASE 4 SETUP VALIDATION SUMMARY")
    print("=" * 80)
    
    all_passed = True
    for test_name, result in validation_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 ALL VALIDATIONS PASSED - READY FOR PHASE 4 TRAINING!")
        print("🚀 Run: python scripts/run_phase_04.py")
        return 0
    else:
        print("⚠️  SOME VALIDATIONS FAILED - RESOLVE ISSUES BEFORE TRAINING")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
