# Phase 5 Configuration: Tempo Alignment & BPM Validation
# Aligns detected beats with TJA BPM references and validates tempo consistency

# Input/Output Paths (Windows format)
paths:
  # Input data from previous phases
  input_audio: "data\\processed\\phase1\\audio"
  input_metadata: "data\\processed\\phase1\\metadata"
  input_quality: "data\\processed\\phase2\\quality_metrics"
  
  # TJA reference data
  tja_data_dir: "data\\raw\\ese"
  
  # Phase 5 outputs
  output_root: "data\\processed\\phase5"
  tempo_alignment: "data\\processed\\phase5\\tempo_alignment"
  bpm_validation: "data\\processed\\phase5\\bmp_validation"
  aligned_beats: "data\\processed\\phase5\\aligned_beats"
  timing_analysis: "data\\processed\\phase5\\timing_analysis"
  processing_logs: "data\\processed\\phase5\\processing_logs"

# Beat Detection Configuration
beat_detection:
  # Librosa beat tracking parameters
  hop_length: 512
  sr: 22050
  units: "time"  # Return beat times in seconds
  
  # Beat tracking algorithm
  method: "ellis"  # Options: "ellis", "crf", "degara"
  
  # Onset detection parameters
  onset_method: "default"  # Options: "default", "energy", "hfc", "complex"
  onset_threshold: 0.3
  onset_pre_max: 0.03
  onset_post_max: 0.00
  onset_pre_avg: 0.10
  onset_post_avg: 0.10
  onset_delta: 0.07
  onset_wait: 0.03
  
  # Tempo estimation
  tempo_method: "beat_track"  # Options: "beat_track", "tempo"
  tempo_prior: null  # Prior tempo estimate (BPM), null for auto
  tempo_ac_size: 8.0  # Autocorrelation window size
  tempo_max_tempo: 320.0  # Maximum tempo (BPM)
  
  # Beat tracking refinement
  tightness: 100  # Beat tracking tightness (higher = more regular)
  trim: true  # Trim beats to audio length
  start_bpm: null  # Starting BPM estimate, null for auto

# Tempo Alignment Configuration
tempo_alignment:
  # BPM tolerance for alignment
  bpm_tolerance: 0.05  # 5% tolerance
  
  # Phase alignment parameters
  phase_search_resolution: 50  # Number of phase offsets to test
  max_phase_correction: 0.5  # Maximum phase correction (seconds)
  
  # Alignment optimization
  alignment_method: "least_squares"  # Options: "least_squares", "robust"
  max_iterations: 100
  convergence_threshold: 0.001
  
  # Tempo change detection
  tempo_change_window: 8  # Number of beats for tempo change detection
  tempo_change_threshold: 0.1  # 10% change threshold
  min_tempo_change_confidence: 0.7
  
  # Grid generation
  grid_extension: 1.0  # Extend grid beyond audio (seconds)
  grid_interpolation: "linear"  # Options: "linear", "cubic"

# BPM Validation Configuration
bpm_validation:
  # Validation thresholds
  max_bpm_error_percent: 5.0  # Maximum acceptable BPM error (%)
  min_alignment_confidence: 0.7  # Minimum alignment confidence
  min_beat_consistency: 0.8  # Minimum beat consistency score
  
  # Segment analysis
  segment_length: 8.0  # Segment length for consistency analysis (seconds)
  segment_overlap: 0.5  # Segment overlap ratio
  min_segments_for_validation: 3  # Minimum segments required
  
  # Quality metrics
  timing_precision_threshold: 0.05  # Maximum timing error (seconds)
  tempo_stability_threshold: 0.05  # Maximum tempo variation (%)

# TJA Parsing Configuration
tja_parsing:
  # File encoding
  encoding: "utf-8-sig"  # Handle BOM in TJA files
  fallback_encoding: "shift_jis"  # Fallback for Japanese files
  
  # Header parsing
  required_headers: ["BPM", "OFFSET"]
  optional_headers: ["TITLE", "SUBTITLE", "WAVE", "DEMOSTART"]
  
  # BPM and timing
  default_bpm: 120.0  # Default BPM if not specified
  default_offset: 0.0  # Default offset if not specified
  
  # Chart parsing (for validation)
  parse_chart_data: false  # Only parse headers for Phase 5
  validate_chart_format: true  # Validate TJA format

# Processing Configuration
processing:
  # Batch processing
  batch_size: 10  # Number of files to process in parallel
  max_workers: 4  # Maximum number of worker processes
  
  # Memory management
  max_audio_length: 600.0  # Maximum audio length (seconds)
  chunk_size: 30.0  # Audio chunk size for processing (seconds)
  chunk_overlap: 2.0  # Chunk overlap (seconds)
  
  # Error handling
  continue_on_error: true  # Continue processing if individual files fail
  max_retry_attempts: 3  # Maximum retry attempts for failed files
  retry_delay: 1.0  # Delay between retries (seconds)
  
  # Progress tracking
  progress_update_frequency: 10  # Update progress every N files
  save_intermediate_results: true  # Save results after each batch

# Quality Gates
quality_gates:
  # Success rate requirements
  min_processing_success_rate: 0.95  # >95% files must process successfully
  min_bpm_validation_pass_rate: 0.85  # >85% files must pass BPM validation
  
  # Accuracy requirements
  max_avg_bpm_error: 3.0  # Average BPM error must be <3%
  max_avg_timing_error: 0.03  # Average timing error must be <30ms
  
  # Consistency requirements
  min_avg_beat_consistency: 0.8  # Average beat consistency >80%
  min_avg_alignment_confidence: 0.7  # Average alignment confidence >70%

# Hardware Configuration
hardware:
  # Memory constraints (RTX 3070 environment)
  max_ram_usage_gb: 8.0  # Maximum RAM usage
  max_vram_usage_gb: 7.0  # Maximum VRAM usage (if using GPU)
  
  # Processing optimization
  use_gpu: false  # GPU not needed for beat detection
  num_threads: 4  # Number of CPU threads
  
  # Caching
  enable_caching: true  # Cache intermediate results
  cache_dir: "data\\processed\\phase5\\cache"
  max_cache_size_gb: 2.0  # Maximum cache size

# Logging Configuration
logging:
  # Log levels
  console_level: "INFO"  # Console log level
  file_level: "DEBUG"  # File log level
  
  # Log formatting
  include_timestamps: true
  include_process_info: true
  include_memory_usage: true
  
  # Log rotation
  max_log_size_mb: 100  # Maximum log file size
  backup_count: 5  # Number of backup log files

# Validation Configuration
validation:
  # Output validation
  validate_output_format: true  # Validate output JSON format
  validate_output_completeness: true  # Check all expected outputs exist
  
  # Data validation
  validate_beat_positions: true  # Validate beat position data
  validate_timing_consistency: true  # Check timing consistency
  validate_bpm_ranges: true  # Check BPM values are reasonable
  
  # Quality checks
  generate_quality_report: true  # Generate comprehensive quality report
  include_visualizations: false  # Generate alignment visualizations (disabled for speed)
  save_debug_info: true  # Save debug information

# Experiment Configuration
experiment:
  name: "phase5_tempo_alignment"
  version: "v1.0"
  description: "Tempo alignment and BPM validation for TJA chart generation"
  tags: ["tempo", "alignment", "bpm", "validation", "phase5"]
  
  # Reproducibility
  random_seed: 42
  deterministic: true
  
  # Output
  save_config: true  # Save configuration with results
  save_processing_stats: true  # Save detailed processing statistics
  create_summary_report: true  # Create summary report
