"""
Phase 2 execution with corrected dataset from Phase 1 TJA-WAVE pairing.
Processes the complete corrected dataset of 2,732 files.
"""

import os
import sys
import subprocess
from pathlib import Path

def check_environment():
    """Check if the environment is properly set up."""
    print("🔍 Checking environment...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    
    # Check if we're in the correct directory
    if not Path("configs/phase_02_config.yaml").exists():
        print("❌ Configuration file not found. Please run from TJAGen root directory.")
        return False
    
    # Check if Phase 1 outputs exist
    if not Path("data/processed/phase1/audio").exists():
        print("❌ Phase 1 outputs not found: data/processed/phase1/audio")
        print("Please run Phase 1 first.")
        return False
    
    if not Path("data/processed/phase1/metadata").exists():
        print("❌ Phase 1 metadata not found: data/processed/phase1/metadata")
        print("Please run Phase 1 first.")
        return False
    
    # Check if Phase 1 has processed files
    audio_files = list(Path("data/processed/phase1/audio").glob("*.npy"))
    metadata_files = list(Path("data/processed/phase1/metadata").glob("*.json"))
    
    if len(audio_files) == 0:
        print("❌ No audio files found in Phase 1 outputs")
        return False
    
    if len(metadata_files) == 0:
        print("❌ No metadata files found in Phase 1 outputs")
        return False
    
    print(f"✅ Environment check passed - Found {len(audio_files)} audio files and {len(metadata_files)} metadata files")
    return True

def main():
    """Main execution function."""
    print("🎼 TJA Chart Generation - Phase 2 Automated Execution")
    print("=" * 60)
    print("🤖 Automated mode: No user confirmation prompts")
    
    # Check environment
    if not check_environment():
        return False
    
    # Add src to Python path
    src_path = Path(__file__).parent.parent / "src"
    sys.path.insert(0, str(src_path))
    
    try:
        # Import and run Phase 2
        from phases.phase_02_quality_assessment import main as phase2_main
        
        print("\n🚀 Starting automated Phase 2 execution with corrected dataset...")
        print("📋 Processing will proceed automatically without user prompts")
        success = phase2_main()
        
        if success:
            print("\n✅ Phase 2 completed successfully!")
            print("📁 Outputs saved to: data/processed/phase2/")
            print("📊 Quality report: data/processed/phase2/quality_report.json")
            print("📋 Processing summary: data/processed/phase2/processing_logs/phase2_summary.json")
            
            # Check final file counts
            filtered_audio_dir = Path("data/processed/phase2/filtered_audio")
            filtered_metadata_dir = Path("data/processed/phase2/filtered_metadata")
            
            if filtered_audio_dir.exists() and filtered_metadata_dir.exists():
                filtered_audio_count = len(list(filtered_audio_dir.glob("*.npy")))
                filtered_metadata_count = len(list(filtered_metadata_dir.glob("*.json")))
                
                print(f"📈 Final Results:")
                print(f"   Filtered audio files: {filtered_audio_count}")
                print(f"   Filtered metadata files: {filtered_metadata_count}")
                
                # Calculate quality pass rate
                input_audio_dir = Path("data/processed/phase1/audio")
                total_input_files = len(list(input_audio_dir.glob("*.npy")))
                quality_pass_rate = filtered_audio_count / total_input_files if total_input_files > 0 else 0
                
                print(f"   Quality pass rate: {quality_pass_rate:.1%} ({filtered_audio_count}/{total_input_files})")
                
                if quality_pass_rate >= 0.90:
                    print(f"✅ Quality pass rate gate passed: {quality_pass_rate:.1%} >= 90%")
                else:
                    print(f"⚠️  Quality pass rate below 90%: {quality_pass_rate:.1%}")
            
            return True
        else:
            print("\n❌ Phase 2 failed. Check logs for details.")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all dependencies are installed correctly.")
        return False
    except Exception as e:
        print(f"❌ Execution error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
