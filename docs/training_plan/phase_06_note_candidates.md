# 🧩 Phase 6: Note Candidate Window Detection

**Status**: 📋 Planned  
**Estimated Duration**: 3 days  
**Dependencies**: [Phase 4: Beat Position Estimation](phase_04_beat_estimation.md), [Phase 5: Tempo Alignment](phase_05_tempo_alignment.md)  
**Next Phase**: [Phase 6.5: Advanced Feature Extraction](phase_06_5_feature_extraction.md)

---

## 1. **Phase Purpose**

This phase identifies time windows where notes are likely to occur based on aligned beat positions and onset detection. This step is isolated because:

- **Note candidate detection** requires combining beat timing with onset analysis
- **Window-based analysis** focuses computational resources on relevant time regions
- **Multi-resolution detection** captures both on-beat and off-beat note possibilities
- **Candidate filtering** reduces false positives before expensive classification

**Why Isolated**: Note candidate detection bridges low-level audio analysis with high-level note classification. It requires different algorithms than beat detection and forms the input pipeline for all note-level machine learning.

---

## 2. **Inputs & Outputs**

### **Inputs**
```python
# From Phase 5 (exact format match)
aligned_beats: List[Dict] = [       # Tempo-aligned beat positions
    {
        "beat_id": int,                 # Sequential beat identifier
        "beat_time": float,             # Corrected beat time (seconds)
        "original_time": float,         # Original detected time
        "correction": float,            # Applied correction (seconds)
        "grid_position": int,           # Position in regular grid
        "confidence": float,            # Alignment confidence (0-1)
        "beat_strength": float,         # Beat detection strength
        "is_downbeat": bool,            # Whether this is a downbeat
        "measure_position": int,        # Position within measure (0-3 for 4/4)
        "bpm_at_beat": float           # Local BPM at this beat
    }
]

bpm_validation: Dict = {            # BPM validation results (matching Phase 5)
    "tja_bpm": float,              # Expected BPM from TJA
    "detected_bpm": float,         # Average detected BPM
    "bpm_error": float,            # Absolute error in BPM
    "bpm_error_percentage": float, # Relative error percentage
    "validation_passed": bool,     # Whether validation passed
    "validation_threshold": float, # Error threshold used
    "segment_consistency": float   # BPM consistency across segments
}

tempo_alignment: Dict = {          # Comprehensive alignment data (matching Phase 5)
    "aligned_bpm": float,           # Final aligned BPM
    "bpm_confidence": float,        # Confidence in BPM alignment
    "tempo_drift": float,           # Detected tempo variation (%)
    "alignment_offset": float,      # Time offset correction (seconds)
    "beat_grid": List[Dict],        # Aligned beat grid (same as aligned_beats)
    "tempo_changes": List[Dict]     # Detected tempo changes
}

# From Phase 4
onset_positions: List[Dict]         # Detected onset positions
beat_confidence: List[float]        # Beat detection confidence scores

# From Phase 3
audio_segments: List[np.ndarray]    # Clean musical segments
energy_profiles: List[np.ndarray]   # RMS energy profiles

# Input directory structure
data\\processed\\phase5\\
├── aligned_beats\\*.json           # Tempo-corrected beat positions
├── bpm_validation\\*.json          # BPM validation results
└── tempo_alignment\\*.json         # Alignment metadata
```

### **Outputs**
```python
# Note candidate windows
note_candidates: List[Dict] = [
    {
        "window_id": int,               # Unique window identifier
        "start_time": float,            # Window start time (seconds)
        "end_time": float,              # Window end time (seconds)
        "center_time": float,           # Window center time
        "duration": float,              # Window duration
        "beat_position": float,         # Position relative to nearest beat (0-1)
        "beat_subdivision": str,        # "quarter", "eighth", "sixteenth", etc.
        "onset_strength": float,        # Maximum onset strength in window
        "energy_profile": np.ndarray,   # Energy profile within window
        "spectral_features": Dict,      # Spectral characteristics
        "candidate_confidence": float,  # Likelihood of containing a note
        "candidate_type": str,          # "strong", "weak", "subdivision"
        "audio_snippet": np.ndarray     # Short audio excerpt
    }
]

# Candidate detection statistics
detection_stats: Dict = {
    "total_candidates": int,            # Total candidates detected
    "candidates_per_beat": float,       # Average candidates per beat
    "strong_candidates": int,           # High-confidence candidates
    "weak_candidates": int,             # Low-confidence candidates
    "subdivision_candidates": int,      # Off-beat candidates
    "detection_coverage": float,        # Percentage of beats with candidates
    "false_positive_estimate": float    # Estimated false positive rate
}

# Output directory structure
data\\processed\\phase6\\
├── note_candidates\\*.json         # Candidate windows per segment
├── candidate_audio\\               # Audio snippets for candidates
│   ├── [song]_[segment]_[window].npy
├── candidate_features\\*.json      # Extracted features per candidate
├── detection_stats\\*.json         # Detection statistics per song
└── candidate_detection_report.json # Overall detection summary
```

---

## 3. **Implementation Plan**

### **Recommended Tools**
```python
import librosa
import numpy as np
import scipy.signal
from pathlib import Path
import json
import logging
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import matplotlib.pyplot as plt
from sklearn.preprocessing import StandardScaler
from scipy.stats import zscore
```

### **Core Candidate Detection Function**
```python
def detect_note_candidates(
    audio: np.ndarray,
    sr: int,
    aligned_beats: List[Dict],
    onset_positions: List[Dict],
    window_size: float = 0.15,  # seconds
    subdivision_levels: List[str] = ["quarter", "eighth", "sixteenth"],
    confidence_threshold: float = 0.3
) -> Tuple[List[Dict], Dict]:
    """
    Detect note candidate windows using beat alignment and onset detection.
    
    Args:
        audio: Input audio segment
        sr: Sample rate
        aligned_beats: List of aligned beat positions
        onset_positions: List of detected onsets
        window_size: Size of candidate windows in seconds
        subdivision_levels: Beat subdivisions to consider
        confidence_threshold: Minimum confidence for candidates
        
    Returns:
        (note_candidates, detection_stats)
    """
    
    candidates = []
    window_id = 0
    
    if not aligned_beats:
        return [], create_empty_detection_stats()
    
    # Extract beat times and calculate average beat interval
    beat_times = [beat["beat_time"] for beat in aligned_beats]
    if len(beat_times) > 1:
        avg_beat_interval = np.mean(np.diff(beat_times))
    else:
        avg_beat_interval = 0.5  # Default 120 BPM
    
    # 1. Generate candidate windows at beat positions
    for beat in aligned_beats:
        beat_time = beat["beat_time"]
        
        # Main beat candidate (strongest)
        main_candidate = create_candidate_window(
            audio, sr, beat_time, window_size, window_id,
            beat_position=0.0, subdivision="quarter",
            onset_positions=onset_positions
        )
        
        if main_candidate["candidate_confidence"] >= confidence_threshold:
            candidates.append(main_candidate)
        window_id += 1
        
        # 2. Generate subdivision candidates
        for subdivision in subdivision_levels[1:]:  # Skip quarter (already done)
            subdivision_positions = get_subdivision_positions(
                beat_time, avg_beat_interval, subdivision
            )
            
            for sub_pos in subdivision_positions:
                # Only create candidates within audio bounds
                if 0 <= sub_pos <= len(audio) / sr:
                    sub_candidate = create_candidate_window(
                        audio, sr, sub_pos, window_size * 0.8,  # Smaller windows for subdivisions
                        window_id,
                        beat_position=calculate_beat_position(sub_pos, beat_time, avg_beat_interval),
                        subdivision=subdivision,
                        onset_positions=onset_positions
                    )
                    
                    if sub_candidate["candidate_confidence"] >= confidence_threshold * 0.7:  # Lower threshold for subdivisions
                        candidates.append(sub_candidate)
                    window_id += 1
    
    # 3. Add onset-based candidates (for syncopated rhythms)
    onset_candidates = detect_onset_candidates(
        audio, sr, onset_positions, beat_times, window_size, window_id, confidence_threshold
    )
    candidates.extend(onset_candidates)
    
    # 4. Remove overlapping candidates (keep highest confidence)
    candidates = remove_overlapping_candidates(candidates, overlap_threshold=0.5)
    
    # 5. Calculate detection statistics
    stats = calculate_detection_stats(candidates, beat_times)
    
    return candidates, stats

def create_candidate_window(
    audio: np.ndarray,
    sr: int,
    center_time: float,
    window_size: float,
    window_id: int,
    beat_position: float,
    subdivision: str,
    onset_positions: List[Dict]
) -> Dict:
    """Create a single candidate window with features."""
    
    # Calculate window boundaries
    half_window = window_size / 2
    start_time = max(0, center_time - half_window)
    end_time = min(len(audio) / sr, center_time + half_window)
    
    # Extract audio snippet
    start_sample = int(start_time * sr)
    end_sample = int(end_time * sr)
    audio_snippet = audio[start_sample:end_sample]
    
    # Calculate onset strength in window
    onset_strength = calculate_onset_strength_in_window(
        onset_positions, start_time, end_time
    )
    
    # Extract spectral features
    spectral_features = extract_spectral_features(audio_snippet, sr)
    
    # Calculate energy profile
    if len(audio_snippet) > 0:
        energy_profile = librosa.feature.rms(
            y=audio_snippet, 
            frame_length=min(512, len(audio_snippet)), 
            hop_length=min(256, len(audio_snippet)//4)
        )[0]
    else:
        energy_profile = np.array([0.0])
    
    # Calculate candidate confidence
    confidence = calculate_candidate_confidence(
        onset_strength, spectral_features, energy_profile, subdivision
    )
    
    # Determine candidate type
    if confidence > 0.8:
        candidate_type = "strong"
    elif confidence > 0.5:
        candidate_type = "weak"
    else:
        candidate_type = "subdivision"
    
    return {
        "window_id": window_id,
        "start_time": float(start_time),
        "end_time": float(end_time),
        "center_time": float(center_time),
        "duration": float(end_time - start_time),
        "beat_position": float(beat_position),
        "beat_subdivision": subdivision,
        "onset_strength": float(onset_strength),
        "energy_profile": energy_profile,
        "spectral_features": spectral_features,
        "candidate_confidence": float(confidence),
        "candidate_type": candidate_type,
        "audio_snippet": audio_snippet
    }

def get_subdivision_positions(beat_time: float, beat_interval: float, subdivision: str) -> List[float]:
    """Get time positions for beat subdivisions."""
    positions = []
    
    if subdivision == "eighth":
        # Add eighth note position (halfway between beats)
        positions.append(beat_time + beat_interval / 2)
    elif subdivision == "sixteenth":
        # Add sixteenth note positions
        quarter_interval = beat_interval / 4
        for i in [1, 2, 3]:  # Skip 0 (that's the beat) and 4 (that's next beat)
            positions.append(beat_time + i * quarter_interval)
    elif subdivision == "triplet":
        # Add triplet positions
        triplet_interval = beat_interval / 3
        for i in [1, 2]:  # Skip 0 and 3
            positions.append(beat_time + i * triplet_interval)
    
    return positions

def calculate_beat_position(time: float, nearest_beat: float, beat_interval: float) -> float:
    """Calculate position relative to nearest beat (0-1)."""
    if beat_interval <= 0:
        return 0.0
    
    offset = time - nearest_beat
    # Normalize to 0-1 range within beat interval
    position = (offset % beat_interval) / beat_interval
    return position

def calculate_onset_strength_in_window(
    onset_positions: List[Dict], 
    start_time: float, 
    end_time: float
) -> float:
    """Calculate maximum onset strength within time window."""
    max_strength = 0.0
    
    for onset in onset_positions:
        if start_time <= onset["time"] <= end_time:
            max_strength = max(max_strength, onset["strength"])
    
    return max_strength

def extract_spectral_features(audio: np.ndarray, sr: int) -> Dict:
    """Extract spectral features from audio snippet."""
    if len(audio) == 0:
        return {
            "spectral_centroid": 0.0,
            "spectral_rolloff": 0.0,
            "spectral_bandwidth": 0.0,
            "zero_crossing_rate": 0.0,
            "mfcc_mean": [0.0] * 13
        }
    
    # Spectral features
    spectral_centroid = np.mean(librosa.feature.spectral_centroid(y=audio, sr=sr))
    spectral_rolloff = np.mean(librosa.feature.spectral_rolloff(y=audio, sr=sr))
    spectral_bandwidth = np.mean(librosa.feature.spectral_bandwidth(y=audio, sr=sr))
    zero_crossing_rate = np.mean(librosa.feature.zero_crossing_rate(audio))
    
    # MFCC features
    mfcc = librosa.feature.mfcc(y=audio, sr=sr, n_mfcc=13)
    mfcc_mean = np.mean(mfcc, axis=1).tolist()
    
    return {
        "spectral_centroid": float(spectral_centroid),
        "spectral_rolloff": float(spectral_rolloff),
        "spectral_bandwidth": float(spectral_bandwidth),
        "zero_crossing_rate": float(zero_crossing_rate),
        "mfcc_mean": mfcc_mean
    }

def calculate_candidate_confidence(
    onset_strength: float,
    spectral_features: Dict,
    energy_profile: np.ndarray,
    subdivision: str
) -> float:
    """Calculate confidence that window contains a note."""
    
    # Base confidence from onset strength
    onset_confidence = min(1.0, onset_strength * 2.0)
    
    # Energy-based confidence
    if len(energy_profile) > 0:
        energy_confidence = min(1.0, np.max(energy_profile) * 5.0)
    else:
        energy_confidence = 0.0
    
    # Spectral complexity confidence
    spectral_confidence = min(1.0, spectral_features["spectral_bandwidth"] / 2000.0)
    
    # Subdivision penalty (off-beat notes are less likely)
    subdivision_weights = {
        "quarter": 1.0,
        "eighth": 0.8,
        "sixteenth": 0.6,
        "triplet": 0.7
    }
    subdivision_weight = subdivision_weights.get(subdivision, 0.5)
    
    # Combine confidences
    combined_confidence = (
        onset_confidence * 0.4 +
        energy_confidence * 0.3 +
        spectral_confidence * 0.3
    ) * subdivision_weight
    
    return min(1.0, combined_confidence)

def detect_onset_candidates(
    audio: np.ndarray,
    sr: int,
    onset_positions: List[Dict],
    beat_times: List[float],
    window_size: float,
    start_window_id: int,
    confidence_threshold: float
) -> List[Dict]:
    """Detect additional candidates based on strong onsets not aligned with beats."""
    
    candidates = []
    window_id = start_window_id
    
    for onset in onset_positions:
        onset_time = onset["time"]
        onset_strength = onset["strength"]
        
        # Check if onset is far from any beat (syncopated)
        min_beat_distance = float('inf')
        if beat_times:
            min_beat_distance = min(abs(onset_time - bt) for bt in beat_times)
        
        # Only consider onsets that are not close to beats and are strong
        if min_beat_distance > 0.1 and onset_strength > confidence_threshold * 1.5:
            
            # Find nearest beat for position calculation
            if beat_times:
                nearest_beat = min(beat_times, key=lambda bt: abs(bt - onset_time))
                beat_interval = 0.5  # Default
                if len(beat_times) > 1:
                    beat_intervals = np.diff(sorted(beat_times))
                    beat_interval = np.mean(beat_intervals)
                
                beat_position = calculate_beat_position(onset_time, nearest_beat, beat_interval)
            else:
                beat_position = 0.5
            
            # Create onset-based candidate
            onset_candidate = create_candidate_window(
                audio, sr, onset_time, window_size * 0.7,  # Smaller window
                window_id, beat_position, "syncopated", []
            )
            
            # Boost confidence for strong onsets
            onset_candidate["candidate_confidence"] *= 1.2
            onset_candidate["candidate_type"] = "syncopated"
            
            candidates.append(onset_candidate)
            window_id += 1
    
    return candidates

def remove_overlapping_candidates(candidates: List[Dict], overlap_threshold: float = 0.5) -> List[Dict]:
    """Remove overlapping candidates, keeping highest confidence ones."""
    
    if not candidates:
        return []
    
    # Sort by confidence (highest first)
    sorted_candidates = sorted(candidates, key=lambda c: c["candidate_confidence"], reverse=True)
    
    filtered_candidates = []
    
    for candidate in sorted_candidates:
        # Check overlap with already selected candidates
        overlaps = False
        
        for selected in filtered_candidates:
            overlap = calculate_time_overlap(
                candidate["start_time"], candidate["end_time"],
                selected["start_time"], selected["end_time"]
            )
            
            if overlap > overlap_threshold:
                overlaps = True
                break
        
        if not overlaps:
            filtered_candidates.append(candidate)
    
    return filtered_candidates

def calculate_time_overlap(start1: float, end1: float, start2: float, end2: float) -> float:
    """Calculate overlap ratio between two time intervals."""
    overlap_start = max(start1, start2)
    overlap_end = min(end1, end2)
    
    if overlap_end <= overlap_start:
        return 0.0
    
    overlap_duration = overlap_end - overlap_start
    total_duration = max(end1 - start1, end2 - start2)
    
    return overlap_duration / total_duration if total_duration > 0 else 0.0

def calculate_detection_stats(candidates: List[Dict], beat_times: List[float]) -> Dict:
    """Calculate detection statistics."""
    
    total_candidates = len(candidates)
    
    if total_candidates == 0:
        return create_empty_detection_stats()
    
    # Count by type
    strong_candidates = sum(1 for c in candidates if c["candidate_type"] == "strong")
    weak_candidates = sum(1 for c in candidates if c["candidate_type"] == "weak")
    subdivision_candidates = sum(1 for c in candidates if c["candidate_type"] in ["subdivision", "syncopated"])
    
    # Calculate coverage
    candidates_per_beat = total_candidates / len(beat_times) if beat_times else 0
    
    # Estimate detection coverage (percentage of beats with nearby candidates)
    coverage_count = 0
    for beat_time in beat_times:
        has_nearby_candidate = any(
            abs(c["center_time"] - beat_time) < 0.2 for c in candidates
        )
        if has_nearby_candidate:
            coverage_count += 1
    
    detection_coverage = coverage_count / len(beat_times) if beat_times else 0
    
    # Rough false positive estimate (candidates with very low confidence)
    low_confidence_candidates = sum(1 for c in candidates if c["candidate_confidence"] < 0.4)
    false_positive_estimate = low_confidence_candidates / total_candidates if total_candidates > 0 else 0
    
    return {
        "total_candidates": total_candidates,
        "candidates_per_beat": float(candidates_per_beat),
        "strong_candidates": strong_candidates,
        "weak_candidates": weak_candidates,
        "subdivision_candidates": subdivision_candidates,
        "detection_coverage": float(detection_coverage),
        "false_positive_estimate": float(false_positive_estimate)
    }

def create_empty_detection_stats() -> Dict:
    """Create empty detection statistics."""
    return {
        "total_candidates": 0,
        "candidates_per_beat": 0.0,
        "strong_candidates": 0,
        "weak_candidates": 0,
        "subdivision_candidates": 0,
        "detection_coverage": 0.0,
        "false_positive_estimate": 0.0
    }
```

---

## 4. **Best Practices**

### **Multi-Resolution Detection**
- Use different window sizes for different subdivision levels
- Apply subdivision-specific confidence thresholds
- Consider musical context (genre-specific patterns)
- Balance recall vs precision based on downstream requirements

### **Feature Engineering**
- Extract both temporal and spectral features
- Use onset strength as primary indicator
- Include energy profile for dynamics information
- Consider harmonic content for note type hints

### **Overlap Handling**
```python
# Intelligent overlap resolution
def resolve_overlaps_intelligently(candidates: List[Dict]) -> List[Dict]:
    """Resolve overlaps using musical knowledge."""
    # Prefer candidates on strong beats
    # Prefer candidates with higher onset strength
    # Consider subdivision hierarchy (quarter > eighth > sixteenth)
    pass
```

### **Quality Control**
- Validate candidate density (not too sparse or dense)
- Check for reasonable distribution across beat positions
- Monitor false positive rates through validation
- Implement confidence calibration

---

## 5. **Challenges & Pitfalls**

### **Syncopated Rhythms**
- **Issue**: Off-beat notes may be missed by beat-based detection
- **Example**: Jazz, funk, Latin music with complex rhythms
- **Mitigation**: Use onset-based detection in addition to beat-based
- **Solution**: Implement rhythm pattern recognition

### **Weak Onsets**
- **Issue**: Soft notes or sustained notes may not trigger onset detection
- **Example**: Legato passages, ambient music
- **Mitigation**: Use energy-based detection with lower thresholds
- **Solution**: Multi-feature candidate detection

### **False Positives**
- **Issue**: Non-musical events detected as note candidates
- **Example**: Noise, artifacts, reverb tails
- **Mitigation**: Use spectral features to filter non-musical content
- **Solution**: Implement candidate validation using multiple features

### **Subdivision Ambiguity**
- **Issue**: Difficulty determining correct subdivision level
- **Example**: Swing rhythms, complex polyrhythms
- **Mitigation**: Use multiple subdivision hypotheses
- **Solution**: Let downstream classification resolve ambiguity

---

## 6. **Dependencies & Unlocks**

### **Prerequisites**
- **Phase 4 & 5 Complete**: Aligned beats and validated tempo
- **Required Files**:
  - `data\\processed\\phase5\\aligned_beats\\*.json`
  - `data\\processed\\phase4\\onset_positions\\*.json`
  - `data\\processed\\phase3\\audio_segments\\*.npy`
- **Libraries**: `librosa`, `scipy`, `sklearn` for feature extraction

### **What This Phase Unlocks**
- **Phase 7**: Note candidates provide focused input for classification
- **Phase 8**: Candidate windows enable sequence pattern learning
- **Phase 9**: Subdivision information supports difficulty modeling
- **All Training Phases**: Candidate detection reduces computational load by focusing on relevant time regions

### **Output Dependencies**
Subsequent phases depend on these Phase 6 outputs:
- `data\\processed\\phase6\\note_candidates\\*.json` - Candidate windows with features
- `data\\processed\\phase6\\candidate_audio\\*.npy` - Audio snippets for classification
- `data\\processed\\phase6\\candidate_features\\*.json` - Extracted features for ML

---

## 7. **Validation Strategy**

### **Unit Tests**
```python
def test_candidate_detection():
    """Test candidate detection on synthetic rhythmic audio."""
    sr = 22050
    # Create audio with clear note positions
    audio = create_test_audio_with_notes(sr)
    
    # Create aligned beats
    aligned_beats = [
        {"beat_time": 0.5, "confidence": 0.9},
        {"beat_time": 1.0, "confidence": 0.9},
        {"beat_time": 1.5, "confidence": 0.9}
    ]
    
    # Create onsets
    onset_positions = [
        {"time": 0.5, "strength": 0.8},
        {"time": 1.0, "strength": 0.9},
        {"time": 1.25, "strength": 0.6}  # Eighth note
    ]
    
    candidates, stats = detect_note_candidates(
        audio, sr, aligned_beats, onset_positions
    )
    
    assert len(candidates) >= 3  # Should detect main beats + subdivision
    assert stats["detection_coverage"] > 0.8
    assert any(c["beat_subdivision"] == "eighth" for c in candidates)

def test_overlap_removal():
    """Test overlap removal functionality."""
    # Create overlapping candidates
    candidates = [
        {"start_time": 0.0, "end_time": 0.2, "candidate_confidence": 0.9},
        {"start_time": 0.1, "end_time": 0.3, "candidate_confidence": 0.7},  # Overlaps with first
        {"start_time": 0.5, "end_time": 0.7, "candidate_confidence": 0.8}
    ]
    
    filtered = remove_overlapping_candidates(candidates, overlap_threshold=0.5)
    
    assert len(filtered) == 2  # Should remove the lower confidence overlapping one
    assert filtered[0]["candidate_confidence"] == 0.9  # Highest confidence kept
```

### **Quality Metrics**
- **Detection Recall**: >85% of actual notes have nearby candidates
- **Detection Precision**: <30% false positive rate
- **Coverage**: >90% of beats have associated candidates
- **Processing Speed**: >100 candidates per second on RTX 3070

### **Visual Validation**
```python
def visualize_candidate_detection(
    audio: np.ndarray,
    sr: int,
    candidates: List[Dict],
    aligned_beats: List[Dict],
    output_path: Path
):
    """Visualize detected note candidates."""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 8))
    
    # Plot waveform with candidates
    time = np.linspace(0, len(audio)/sr, len(audio))
    ax1.plot(time, audio, alpha=0.7, color='blue', linewidth=0.5)
    
    # Mark aligned beats
    for beat in aligned_beats:
        ax1.axvline(beat["beat_time"], color='red', alpha=0.8, linewidth=2, label='Beat')
    
    # Mark candidates by type
    colors = {"strong": "green", "weak": "orange", "subdivision": "purple", "syncopated": "brown"}
    for candidate in candidates:
        color = colors.get(candidate["candidate_type"], "gray")
        ax1.axvspan(
            candidate["start_time"], candidate["end_time"],
            alpha=0.3, color=color, label=candidate["candidate_type"]
        )
    
    ax1.set_title('Audio Waveform with Note Candidates')
    ax1.set_xlabel('Time (seconds)')
    ax1.set_ylabel('Amplitude')
    ax1.legend()
    
    # Plot candidate confidence over time
    candidate_times = [c["center_time"] for c in candidates]
    candidate_confidences = [c["candidate_confidence"] for c in candidates]
    
    ax2.scatter(candidate_times, candidate_confidences, 
               c=[colors.get(c["candidate_type"], "gray") for c in candidates],
               alpha=0.7, s=50)
    ax2.axhline(0.5, color='red', linestyle='--', alpha=0.5, label='Confidence Threshold')
    ax2.set_title('Candidate Confidence Distribution')
    ax2.set_xlabel('Time (seconds)')
    ax2.set_ylabel('Confidence')
    ax2.set_ylim(0, 1)
    ax2.legend()
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()
```

### **Example Success Case**
```python
# Expected output for typical song segment
candidates, stats = detect_note_candidates(segment_audio, 22050, aligned_beats, onsets)

# Expected results:
# candidates = [
#     {
#         "window_id": 0,
#         "start_time": 0.425, "end_time": 0.575, "center_time": 0.5,
#         "duration": 0.15, "beat_position": 0.0, "beat_subdivision": "quarter",
#         "onset_strength": 0.85, "candidate_confidence": 0.92,
#         "candidate_type": "strong", "spectral_features": {...}
#     },
#     {
#         "window_id": 1,
#         "start_time": 0.675, "end_time": 0.825, "center_time": 0.75,
#         "duration": 0.15, "beat_position": 0.5, "beat_subdivision": "eighth",
#         "onset_strength": 0.65, "candidate_confidence": 0.73,
#         "candidate_type": "weak", "spectral_features": {...}
#     }
# ]
# 
# stats = {
#     "total_candidates": 12,
#     "candidates_per_beat": 3.0,
#     "strong_candidates": 4,
#     "weak_candidates": 5,
#     "subdivision_candidates": 3,
#     "detection_coverage": 0.95,
#     "false_positive_estimate": 0.15
# }
```

---

**Phase 6 Complete**. This phase identifies time windows where notes are likely to occur, providing focused input regions for note classification and reducing computational complexity.

**Next**: [Phase 7: Basic Note Type Classification](phase_07_note_classification.md)
