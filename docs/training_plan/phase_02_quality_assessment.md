# 🧩 Phase 2: Audio Quality Assessment & Filtering

**Status**: 📋 Planned  
**Estimated Duration**: 1 day  
**Dependencies**: [Phase 1: Audio Loading](phase_01_audio_loading.md)  
**Next Phase**: [Phase 3: Silence Detection](phase_03_silence_detection.md)

---

## 1. **Phase Purpose**

This phase analyzes the quality of standardized audio files and filters out problematic recordings that would negatively impact model training. This step is isolated because:

- **Quality variations** in the dataset can introduce noise into training
- **Corrupted or low-quality audio** needs to be identified before feature extraction
- **Consistent quality standards** ensure reliable downstream processing
- **Early filtering** saves computational resources on unusable files

**Why Isolated**: Quality assessment requires different algorithms than loading/preprocessing, and the filtering decisions affect dataset composition for all subsequent phases.

---

## 2. **Inputs & Outputs**

### **Inputs**
```python
# From Phase 1 (exact format match)
audio_data: np.ndarray          # Shape: (n_samples,) - mono, float32
sample_rate: int = 22050        # Standardized sample rate
duration: float                 # Duration in seconds

# Audio metadata (matching Phase 1 output format)
audio_metadata: Dict = {
    "file_path": str,               # Full Windows path to original audio file
    "filename": str,                # Filename without extension
    "file_extension": str,          # File extension (.wav, .mp3, etc.)
    "original_sr": int,             # Original sample rate (Hz)
    "original_channels": int,       # Original channel count
    "original_duration": float,     # Original duration in seconds
    "file_size_mb": float,          # File size in megabytes
    "standardized_sr": int,         # Standardized sample rate (22050)
    "standardized_channels": int,   # Standardized channels (1=mono)
    "conversion_applied": bool,     # Whether format conversion was needed
    "load_success": bool,           # Whether file loaded successfully
    "error_message": Optional[str], # Error message if loading failed
    "processing_time": float,       # Processing time in seconds
    "audio_format": str,            # Detected audio format
    "bit_depth": Optional[int],     # Original bit depth if available
    "encoding": Optional[str]       # Audio encoding type if available
}

# Input directory structure (matching Phase 1 output)
data\\processed\\phase1\\
├── audio\\                     # Standardized audio files
│   ├── {filename}.npy         # Audio data as numpy array (float32)
│   └── ...
├── metadata\\                  # Audio metadata files
│   ├── {filename}.json        # Audio metadata in JSON format
│   └── ...
└── processing_logs\\           # Processing logs
```

### **Outputs**
```python
# Quality assessment results
quality_metrics: dict = {
    "snr_db": float,              # Signal-to-noise ratio
    "dynamic_range_db": float,    # Dynamic range
    "clipping_percentage": float, # Percentage of clipped samples
    "silence_percentage": float,  # Percentage of silent samples
    "spectral_centroid_mean": float,  # Average spectral centroid
    "spectral_rolloff_mean": float,   # Average spectral rolloff
    "zero_crossing_rate": float,  # Average zero crossing rate
    "quality_score": float,       # Overall quality score (0-1)
    "quality_pass": bool,         # Passes quality threshold
    "quality_issues": list        # List of detected issues
}

# Output directory structure
data\\processed\\phase2\\
├── quality_metrics\\*.json    # Quality assessment per file
├── filtered_audio\\*.npy      # High-quality audio files only
├── filtered_metadata\\*.json  # Metadata for filtered files
├── quality_report.json        # Overall quality statistics
└── rejected_files.json        # List of rejected files with reasons
```

---

## 3. **Implementation Plan**

### **Recommended Tools**
```python
import librosa
import numpy as np
import scipy.stats
from pathlib import Path
import json
import logging
from typing import Dict, List, Tuple
from tqdm import tqdm
import matplotlib.pyplot as plt
```

### **Core Quality Assessment Function**
```python
def assess_audio_quality(audio: np.ndarray, sr: int = 22050) -> Dict:
    """
    Comprehensive audio quality assessment.
    
    Args:
        audio: Audio signal array
        sr: Sample rate
        
    Returns:
        Dictionary with quality metrics and pass/fail decision
    """
    metrics = {}
    issues = []
    
    # 1. Signal-to-Noise Ratio (SNR)
    # Estimate noise from quietest 10% of signal
    sorted_abs = np.sort(np.abs(audio))
    noise_threshold = sorted_abs[int(0.1 * len(sorted_abs))]
    signal_power = np.mean(audio ** 2)
    noise_power = np.mean((audio[np.abs(audio) <= noise_threshold]) ** 2)
    
    if noise_power > 0:
        snr_db = 10 * np.log10(signal_power / noise_power)
    else:
        snr_db = 100.0  # Very clean signal
    
    metrics["snr_db"] = float(snr_db)
    if snr_db < 20:
        issues.append(f"Low SNR: {snr_db:.1f}dB")
    
    # 2. Dynamic Range
    max_amplitude = np.max(np.abs(audio))
    min_amplitude = np.min(np.abs(audio[audio != 0]))  # Exclude true zeros
    
    if min_amplitude > 0:
        dynamic_range_db = 20 * np.log10(max_amplitude / min_amplitude)
    else:
        dynamic_range_db = 0.0
    
    metrics["dynamic_range_db"] = float(dynamic_range_db)
    if dynamic_range_db < 30:
        issues.append(f"Low dynamic range: {dynamic_range_db:.1f}dB")
    
    # 3. Clipping Detection
    clipping_threshold = 0.99
    clipped_samples = np.sum(np.abs(audio) >= clipping_threshold)
    clipping_percentage = (clipped_samples / len(audio)) * 100
    
    metrics["clipping_percentage"] = float(clipping_percentage)
    if clipping_percentage > 1.0:
        issues.append(f"Clipping detected: {clipping_percentage:.2f}%")
    
    # 4. Silence Analysis
    silence_threshold = 0.01
    silent_samples = np.sum(np.abs(audio) < silence_threshold)
    silence_percentage = (silent_samples / len(audio)) * 100
    
    metrics["silence_percentage"] = float(silence_percentage)
    if silence_percentage > 50:
        issues.append(f"Too much silence: {silence_percentage:.1f}%")
    
    # 5. Spectral Features
    # Spectral centroid (brightness)
    spectral_centroid = librosa.feature.spectral_centroid(y=audio, sr=sr)[0]
    metrics["spectral_centroid_mean"] = float(np.mean(spectral_centroid))
    metrics["spectral_centroid_std"] = float(np.std(spectral_centroid))
    
    # Spectral rolloff (frequency content)
    spectral_rolloff = librosa.feature.spectral_rolloff(y=audio, sr=sr)[0]
    metrics["spectral_rolloff_mean"] = float(np.mean(spectral_rolloff))
    
    # Zero crossing rate (noisiness)
    zcr = librosa.feature.zero_crossing_rate(audio)[0]
    metrics["zero_crossing_rate"] = float(np.mean(zcr))
    
    # 6. Overall Quality Score (0-1)
    quality_score = calculate_quality_score(metrics)
    metrics["quality_score"] = quality_score
    
    # 7. Pass/Fail Decision
    quality_pass = (
        snr_db >= 20 and
        dynamic_range_db >= 30 and
        clipping_percentage <= 1.0 and
        silence_percentage <= 50 and
        quality_score >= 0.7
    )
    
    metrics["quality_pass"] = quality_pass
    metrics["quality_issues"] = issues
    
    return metrics

def calculate_quality_score(metrics: Dict) -> float:
    """Calculate overall quality score from individual metrics."""
    # Normalize metrics to 0-1 scale
    snr_score = min(1.0, max(0.0, (metrics["snr_db"] - 10) / 40))  # 10-50dB range
    dr_score = min(1.0, max(0.0, (metrics["dynamic_range_db"] - 20) / 60))  # 20-80dB range
    clip_score = max(0.0, 1.0 - metrics["clipping_percentage"] / 5.0)  # 0-5% range
    silence_score = max(0.0, 1.0 - metrics["silence_percentage"] / 60.0)  # 0-60% range
    
    # Weighted average
    weights = [0.3, 0.3, 0.2, 0.2]  # SNR, DR, Clipping, Silence
    scores = [snr_score, dr_score, clip_score, silence_score]
    
    return sum(w * s for w, s in zip(weights, scores))
```

### **Batch Processing Pipeline**
```python
def process_quality_assessment(
    input_dir: Path = Path("data\\processed\\phase1"),
    output_dir: Path = Path("data\\processed\\phase2"),
    quality_threshold: float = 0.7
) -> Dict:
    """Process quality assessment for entire dataset."""
    
    # Setup output directories
    (output_dir / "quality_metrics").mkdir(parents=True, exist_ok=True)
    (output_dir / "filtered_audio").mkdir(parents=True, exist_ok=True)
    (output_dir / "filtered_metadata").mkdir(parents=True, exist_ok=True)
    
    # Load audio files
    audio_files = list((input_dir / "audio").glob("*.npy"))
    
    results = {
        "total_files": len(audio_files),
        "passed_quality": 0,
        "failed_quality": 0,
        "quality_stats": {},
        "rejected_files": []
    }
    
    all_metrics = []
    
    for audio_file in tqdm(audio_files, desc="Assessing audio quality"):
        try:
            # Load audio and metadata
            audio = np.load(audio_file)
            metadata_file = input_dir / "metadata" / f"{audio_file.stem}.json"
            
            with open(metadata_file, 'r') as f:
                metadata = json.load(f)
            
            # Assess quality
            quality_metrics = assess_audio_quality(audio, sr=22050)
            
            # Save quality metrics
            quality_file = output_dir / "quality_metrics" / f"{audio_file.stem}.json"
            with open(quality_file, 'w') as f:
                json.dump(quality_metrics, f, indent=2)
            
            all_metrics.append(quality_metrics)
            
            # Filter based on quality
            if quality_metrics["quality_pass"]:
                # Copy high-quality files
                np.save(output_dir / "filtered_audio" / audio_file.name, audio)
                
                # Update metadata with quality info
                metadata.update(quality_metrics)
                with open(output_dir / "filtered_metadata" / f"{audio_file.stem}.json", 'w') as f:
                    json.dump(metadata, f, indent=2)
                
                results["passed_quality"] += 1
            else:
                results["failed_quality"] += 1
                results["rejected_files"].append({
                    "file": str(audio_file),
                    "quality_score": quality_metrics["quality_score"],
                    "issues": quality_metrics["quality_issues"]
                })
                
        except Exception as e:
            logging.error(f"Error processing {audio_file}: {e}")
            results["failed_quality"] += 1
    
    # Calculate overall statistics
    if all_metrics:
        results["quality_stats"] = {
            "mean_snr": np.mean([m["snr_db"] for m in all_metrics]),
            "mean_dynamic_range": np.mean([m["dynamic_range_db"] for m in all_metrics]),
            "mean_quality_score": np.mean([m["quality_score"] for m in all_metrics]),
            "pass_rate": results["passed_quality"] / results["total_files"]
        }
    
    # Save results
    with open(output_dir / "quality_report.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    with open(output_dir / "rejected_files.json", 'w') as f:
        json.dump(results["rejected_files"], f, indent=2)
    
    return results
```

---

## 4. **Best Practices**

### **Quality Threshold Tuning**
- Start with conservative thresholds (SNR > 20dB, DR > 30dB)
- Adjust based on dataset characteristics and downstream performance
- Use validation set to optimize quality score weights
- Document threshold decisions for reproducibility

### **Computational Efficiency**
- Process spectral features in batches to reuse FFT computations
- Cache intermediate results for repeated analysis
- Use vectorized operations for metric calculations
- Skip expensive computations for obviously bad files

### **Robust Metric Calculation**
```python
# Handle edge cases in metric calculation
def safe_log10(x, default=0.0):
    """Safe logarithm calculation with fallback."""
    return np.log10(x) if x > 0 else default

def safe_divide(numerator, denominator, default=0.0):
    """Safe division with fallback for zero denominator."""
    return numerator / denominator if denominator != 0 else default
```

---

## 5. **Challenges & Pitfalls**

### **Genre-Specific Quality Variations**
- **Issue**: Different music genres have different quality characteristics
- **Example**: Electronic music may have higher clipping tolerance
- **Mitigation**: Use genre-aware quality thresholds
- **Solution**: Analyze quality distributions per genre, adjust thresholds accordingly

### **False Positive Rejections**
- **Issue**: Legitimate artistic choices (quiet passages, distortion) flagged as quality issues
- **Example**: Classical music with wide dynamic range, metal with intentional distortion
- **Mitigation**: Use multiple quality metrics, not just single thresholds
- **Solution**: Manual review of borderline cases, genre-specific rules

### **Noise Floor Estimation**
- **Issue**: Difficulty distinguishing between noise and quiet musical content
- **Symptoms**: Incorrect SNR calculations for quiet songs
- **Mitigation**: Use frequency-domain analysis to identify noise patterns
- **Solution**: Multi-band noise analysis, adaptive thresholding

### **Temporal Quality Variations**
- **Issue**: Audio quality may vary within a single file
- **Example**: Live recordings with varying microphone quality
- **Mitigation**: Analyze quality in segments, not just overall
- **Solution**: Sliding window analysis, reject files with too much variation

---

## 6. **Dependencies & Unlocks**

### **Prerequisites**
- **Phase 1 Complete**: Standardized audio files and metadata
- **Required Files**: 
  - `data\\processed\\phase1\\audio\\*.npy`
  - `data\\processed\\phase1\\metadata\\*.json`
- **Libraries**: `librosa`, `scipy`, `numpy` with audio processing capabilities

### **What This Phase Unlocks**
- **Phase 3**: Clean audio enables accurate silence detection
- **Phase 4**: High-quality audio improves beat detection accuracy
- **All Training Phases**: Filtered dataset reduces training noise and improves model performance
- **Quality Assurance**: Establishes quality baseline for generated charts

### **Output Dependencies**
Subsequent phases depend on these Phase 2 outputs:
- `data\\processed\\phase2\\filtered_audio\\*.npy` - High-quality audio only
- `data\\processed\\phase2\\filtered_metadata\\*.json` - Enhanced metadata with quality info
- `data\\processed\\phase2\\quality_report.json` - Dataset quality statistics

---

## 7. **Validation Strategy**

### **Unit Tests**
```python
def test_quality_assessment():
    """Test quality assessment on known samples."""
    # Test with high-quality synthetic audio
    sr = 22050
    duration = 5.0
    t = np.linspace(0, duration, int(sr * duration))
    clean_audio = 0.5 * np.sin(2 * np.pi * 440 * t)  # Clean 440Hz tone
    
    metrics = assess_audio_quality(clean_audio, sr)
    assert metrics["quality_pass"] == True
    assert metrics["snr_db"] > 40
    assert metrics["clipping_percentage"] < 0.1

def test_quality_rejection():
    """Test that poor quality audio is correctly rejected."""
    # Test with clipped audio
    clipped_audio = np.clip(np.random.randn(22050), -1, 1)
    metrics = assess_audio_quality(clipped_audio, 22050)
    assert metrics["quality_pass"] == False
    assert "Clipping detected" in str(metrics["quality_issues"])

def test_silence_detection():
    """Test silence percentage calculation."""
    # Create audio with 50% silence
    audio = np.concatenate([
        0.5 * np.random.randn(11025),  # 0.5s of signal
        np.zeros(11025)                # 0.5s of silence
    ])
    metrics = assess_audio_quality(audio, 22050)
    assert 45 <= metrics["silence_percentage"] <= 55  # Allow some tolerance
```

### **Quality Metrics Validation**
- **Pass Rate Target**: 85-95% of files should pass quality assessment
- **SNR Distribution**: Mean SNR should be >25dB for music dataset
- **Dynamic Range**: Mean DR should be >40dB for well-mastered music
- **Quality Score**: Mean quality score should be >0.8 for good dataset

### **Visual Validation**
```python
def plot_quality_distribution(quality_report_path: Path):
    """Plot quality metric distributions."""
    with open(quality_report_path, 'r') as f:
        report = json.load(f)
    
    # Load all quality metrics
    metrics_dir = quality_report_path.parent / "quality_metrics"
    all_metrics = []
    
    for metrics_file in metrics_dir.glob("*.json"):
        with open(metrics_file, 'r') as f:
            all_metrics.append(json.load(f))
    
    # Plot distributions
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    # SNR distribution
    snr_values = [m["snr_db"] for m in all_metrics]
    axes[0,0].hist(snr_values, bins=30, alpha=0.7)
    axes[0,0].axvline(20, color='r', linestyle='--', label='Threshold')
    axes[0,0].set_title('SNR Distribution')
    axes[0,0].set_xlabel('SNR (dB)')
    
    # Quality score distribution
    quality_scores = [m["quality_score"] for m in all_metrics]
    axes[0,1].hist(quality_scores, bins=30, alpha=0.7)
    axes[0,1].axvline(0.7, color='r', linestyle='--', label='Threshold')
    axes[0,1].set_title('Quality Score Distribution')
    axes[0,1].set_xlabel('Quality Score')
    
    # Dynamic range distribution
    dr_values = [m["dynamic_range_db"] for m in all_metrics]
    axes[1,0].hist(dr_values, bins=30, alpha=0.7)
    axes[1,0].axvline(30, color='r', linestyle='--', label='Threshold')
    axes[1,0].set_title('Dynamic Range Distribution')
    axes[1,0].set_xlabel('Dynamic Range (dB)')
    
    # Pass/fail pie chart
    pass_count = sum(1 for m in all_metrics if m["quality_pass"])
    fail_count = len(all_metrics) - pass_count
    axes[1,1].pie([pass_count, fail_count], labels=['Pass', 'Fail'], autopct='%1.1f%%')
    axes[1,1].set_title('Quality Assessment Results')
    
    plt.tight_layout()
    plt.show()
```

### **Example Success Case**
```python
# Expected output for high-quality audio
quality_metrics = assess_audio_quality(high_quality_audio, 22050)

# Expected results:
# {
#     "snr_db": 35.2,
#     "dynamic_range_db": 45.8,
#     "clipping_percentage": 0.0,
#     "silence_percentage": 12.3,
#     "spectral_centroid_mean": 2150.5,
#     "spectral_rolloff_mean": 8500.2,
#     "zero_crossing_rate": 0.15,
#     "quality_score": 0.87,
#     "quality_pass": True,
#     "quality_issues": []
# }
```

---

**Phase 2 Complete**. This phase filters the dataset to include only high-quality audio files, establishing a clean foundation for feature extraction and model training.

**Next**: [Phase 3: Silence Detection & Audio Segmentation](phase_03_silence_detection.md)
