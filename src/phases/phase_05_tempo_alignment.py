"""
Phase 5: Tempo Alignment & BPM Validation
Aligns detected beats with TJA BPM references and validates tempo consistency.
"""

import os
import sys
import json
import time
import logging
import numpy as np
import librosa
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from concurrent.futures import ProcessPoolExecutor, as_completed
import yaml
from tqdm import tqdm
import warnings

# Suppress librosa warnings
warnings.filterwarnings('ignore', category=UserWarning, module='librosa')

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class BeatDetectionResult:
    """Result of beat detection for a single audio file."""
    beats: List[float]  # Beat times in seconds
    tempo: float  # Estimated tempo in BPM
    onset_frames: List[int]  # Onset frame positions
    onset_times: List[float]  # Onset times in seconds
    confidence: float  # Detection confidence score


@dataclass
class TJAMetadata:
    """TJA file metadata."""
    bpm: float
    offset: float
    title: Optional[str] = None
    subtitle: Optional[str] = None
    wave: Optional[str] = None


@dataclass
class TempoAlignment:
    """Result of tempo alignment."""
    aligned_bpm: float
    bpm_confidence: float
    tempo_drift: float
    alignment_offset: float
    beat_grid: List[Dict[str, Any]]
    tempo_changes: List[Dict[str, Any]]


@dataclass
class BPMValidation:
    """Result of BPM validation."""
    tja_bpm: float
    detected_bpm: float
    bpm_error: float
    bpm_error_percentage: float
    validation_passed: bool
    validation_threshold: float
    segment_consistency: float


class TJAParser:
    """Parser for TJA files to extract metadata."""
    
    def __init__(self, encoding: str = "utf-8-sig", fallback_encoding: str = "shift_jis"):
        self.encoding = encoding
        self.fallback_encoding = fallback_encoding
    
    def parse_tja_file(self, tja_path: Path) -> Optional[TJAMetadata]:
        """Parse TJA file and extract metadata."""
        try:
            # Try primary encoding first
            try:
                with open(tja_path, 'r', encoding=self.encoding) as f:
                    content = f.read()
            except UnicodeDecodeError:
                # Fallback to alternative encoding
                with open(tja_path, 'r', encoding=self.fallback_encoding) as f:
                    content = f.read()
            
            # Parse headers
            metadata = {}
            for line in content.split('\n'):
                line = line.strip()
                if ':' in line and not line.startswith('#'):
                    key, value = line.split(':', 1)
                    key = key.strip().upper()
                    value = value.strip()
                    metadata[key] = value
            
            # Extract required fields
            bpm = float(metadata.get('BPM', 120.0))
            offset = float(metadata.get('OFFSET', 0.0))
            
            return TJAMetadata(
                bpm=bpm,
                offset=offset,
                title=metadata.get('TITLE'),
                subtitle=metadata.get('SUBTITLE'),
                wave=metadata.get('WAVE')
            )
            
        except Exception as e:
            logger.error(f"Error parsing TJA file {tja_path}: {e}")
            return None
    
    def find_tja_file(self, song_name: str, tja_data_dir: Path) -> Optional[Path]:
        """Find TJA file for given song name."""
        try:
            # Search for TJA file recursively
            tja_files = list(tja_data_dir.rglob(f"{song_name}.tja"))
            
            if tja_files:
                return tja_files[0]
            
            # Try without extension matching
            for tja_file in tja_data_dir.rglob("*.tja"):
                if song_name.lower() in tja_file.stem.lower():
                    return tja_file
            
            return None
            
        except Exception as e:
            logger.error(f"Error finding TJA file for {song_name}: {e}")
            return None


class BeatDetector:
    """Beat detection using librosa."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config['beat_detection']
        self.sr = self.config['sr']
        self.hop_length = self.config['hop_length']
    
    def detect_beats(self, audio_path: Path) -> Optional[BeatDetectionResult]:
        """Detect beats in audio file."""
        try:
            # Load audio
            y, sr = librosa.load(audio_path, sr=self.sr)
            
            if len(y) == 0:
                logger.warning(f"Empty audio file: {audio_path}")
                return None
            
            # Detect onsets
            onset_frames = librosa.onset.onset_detect(
                y=y, 
                sr=sr,
                hop_length=self.hop_length,
                units='frames'
            )
            onset_times = librosa.frames_to_time(
                onset_frames, 
                sr=sr, 
                hop_length=self.hop_length
            )
            
            # Detect beats
            tempo, beats = librosa.beat.beat_track(
                y=y,
                sr=sr,
                hop_length=self.hop_length,
                units='time',
                tightness=self.config.get('tightness', 100),
                trim=self.config.get('trim', True),
                start_bpm=self.config.get('start_bpm')
            )
            
            # Calculate confidence based on beat regularity
            if len(beats) > 1:
                beat_intervals = np.diff(beats)
                confidence = 1.0 - (np.std(beat_intervals) / np.mean(beat_intervals))
                confidence = max(0.0, min(1.0, confidence))
            else:
                confidence = 0.0
            
            return BeatDetectionResult(
                beats=beats.tolist(),
                tempo=float(tempo),
                onset_frames=onset_frames.tolist(),
                onset_times=onset_times.tolist(),
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Error detecting beats in {audio_path}: {e}")
            return None


class TempoAligner:
    """Aligns detected beats with TJA tempo."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config['tempo_alignment']
        self.tolerance = self.config['bpm_tolerance']
    
    def align_tempo_with_tja(
        self, 
        beat_result: BeatDetectionResult, 
        tja_metadata: TJAMetadata
    ) -> TempoAlignment:
        """Align detected beats with TJA tempo."""
        try:
            beats = np.array(beat_result.beats)
            detected_bpm = beat_result.tempo
            target_bpm = tja_metadata.bmp
            target_offset = tja_metadata.offset
            
            # Check if alignment is needed
            bmp_error = abs(detected_bpm - target_bpm) / target_bpm
            
            if bmp_error <= self.tolerance:
                # Beats are already well-aligned
                beat_grid = self._create_beat_grid_from_detections(beats, beat_result.confidence)
                return TempoAlignment(
                    aligned_bpm=detected_bpm,
                    bpm_confidence=beat_result.confidence,
                    tempo_drift=0.0,
                    alignment_offset=0.0,
                    beat_grid=beat_grid,
                    tempo_changes=[]
                )
            else:
                # Perform alignment
                return self._perform_tempo_alignment(beats, target_bpm, target_offset, beat_result.confidence)
                
        except Exception as e:
            logger.error(f"Error in tempo alignment: {e}")
            # Return default alignment
            return TempoAlignment(
                aligned_bpm=120.0,
                bpm_confidence=0.0,
                tempo_drift=0.0,
                alignment_offset=0.0,
                beat_grid=[],
                tempo_changes=[]
            )
    
    def _create_beat_grid_from_detections(self, beats: np.ndarray, confidence: float) -> List[Dict[str, Any]]:
        """Create beat grid from detected beats."""
        beat_grid = []
        for i, beat_time in enumerate(beats):
            beat_grid.append({
                "beat_time": float(beat_time),
                "original_time": float(beat_time),
                "correction": 0.0,
                "grid_position": i,
                "confidence": confidence
            })
        return beat_grid
    
    def _perform_tempo_alignment(
        self, 
        beats: np.ndarray, 
        target_bpm: float, 
        target_offset: float,
        confidence: float
    ) -> TempoAlignment:
        """Perform tempo alignment using optimization."""
        target_interval = 60.0 / target_bpm
        
        # Find optimal phase alignment
        def alignment_cost(phase_offset):
            max_time = np.max(beats) if len(beats) > 0 else 10.0
            expected_beats = np.arange(target_offset + phase_offset, max_time, target_interval)
            
            if len(expected_beats) == 0 or len(beats) == 0:
                return float('inf')
            
            costs = []
            for beat_time in beats:
                distances = np.abs(expected_beats - beat_time)
                min_distance = np.min(distances)
                costs.append(min_distance)
            
            return np.mean(costs) if costs else float('inf')
        
        # Optimize phase offset
        phase_range = np.linspace(0, target_interval, self.config['phase_search_resolution'])
        phase_costs = [alignment_cost(phase) for phase in phase_range]
        optimal_phase = phase_range[np.argmin(phase_costs)]
        
        # Generate aligned beat grid
        max_time = np.max(beats) + target_interval if len(beats) > 0 else 10.0
        aligned_beat_times = np.arange(target_offset + optimal_phase, max_time, target_interval)
        
        # Match detected beats to aligned grid
        beat_grid = []
        for i, detected_time in enumerate(beats):
            if len(aligned_beat_times) > 0:
                distances = np.abs(aligned_beat_times - detected_time)
                closest_idx = np.argmin(distances)
                aligned_time = aligned_beat_times[closest_idx]
                correction = aligned_time - detected_time
                
                beat_grid.append({
                    "beat_time": float(aligned_time),
                    "original_time": float(detected_time),
                    "correction": float(correction),
                    "grid_position": int(closest_idx),
                    "confidence": confidence
                })
        
        # Calculate tempo drift
        if len(beats) > 1:
            actual_intervals = np.diff(beats)
            tempo_drift = (np.std(actual_intervals) / np.mean(actual_intervals)) * 100
        else:
            tempo_drift = 0.0
        
        # Detect tempo changes
        tempo_changes = self._detect_tempo_changes(beats, target_bpm)
        
        return TempoAlignment(
            aligned_bpm=target_bpm,
            bpm_confidence=confidence,
            tempo_drift=float(tempo_drift),
            alignment_offset=float(optimal_phase),
            beat_grid=beat_grid,
            tempo_changes=tempo_changes
        )
    
    def _detect_tempo_changes(self, beats: np.ndarray, base_bpm: float) -> List[Dict[str, Any]]:
        """Detect tempo changes using sliding window analysis."""
        window_size = self.config['tempo_change_window']
        threshold = self.config['tempo_change_threshold']
        
        if len(beats) < window_size * 2:
            return []
        
        tempo_changes = []
        base_interval = 60.0 / base_bpm
        
        for i in range(window_size, len(beats) - window_size):
            window_start = i - window_size // 2
            window_end = i + window_size // 2
            
            window_times = beats[window_start:window_end]
            if len(window_times) > 1:
                local_intervals = np.diff(window_times)
                local_bpm = 60.0 / np.mean(local_intervals)
                
                bmp_change = abs(local_bpm - base_bpm) / base_bpm
                if bmp_change > threshold:
                    tempo_changes.append({
                        "time": float(beats[i]),
                        "old_bpm": float(base_bpm),
                        "new_bpm": float(local_bmp),
                        "confidence": float(1.0 - bmp_change)
                    })
                    base_bpm = local_bpm
        
        return tempo_changes


class BPMValidator:
    """Validates BPM alignment results."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config['bpm_validation']

    def validate_bpm_alignment(
        self,
        detected_bpm: float,
        tja_bpm: float,
        segment_bpms: List[float],
        validation_threshold: float = None
    ) -> BPMValidation:
        """Validate BPM alignment against TJA reference."""
        if validation_threshold is None:
            validation_threshold = self.config['max_bpm_error_percent'] / 100.0

        # Calculate BPM error
        bpm_error = abs(detected_bpm - tja_bpm)
        bpm_error_percentage = (bpm_error / tja_bpm) * 100

        # Check validation
        validation_passed = bpm_error_percentage <= (validation_threshold * 100)

        # Calculate segment consistency
        if len(segment_bpms) > 1:
            segment_consistency = 1.0 - (np.std(segment_bpms) / np.mean(segment_bpms))
        else:
            segment_consistency = 1.0

        return BPMValidation(
            tja_bpm=float(tja_bpm),
            detected_bpm=float(detected_bpm),
            bpm_error=float(bpm_error),
            bpm_error_percentage=float(bpm_error_percentage),
            validation_passed=validation_passed,
            validation_threshold=float(validation_threshold * 100),
            segment_consistency=float(max(0.0, segment_consistency))
        )


class Phase5TempoAlignment:
    """Main Phase 5 implementation class."""

    def __init__(self, config_path: str):
        """Initialize Phase 5 processor."""
        self.config_path = Path(config_path)
        self.config = self._load_config()

        # Initialize components
        self.tja_parser = TJAParser(
            encoding=self.config['tja_parsing']['encoding'],
            fallback_encoding=self.config['tja_parsing']['fallback_encoding']
        )
        self.beat_detector = BeatDetector(self.config)
        self.tempo_aligner = TempoAligner(self.config)
        self.bpm_validator = BPMValidator(self.config)

        # Setup paths
        self.setup_output_directories()

        # Setup logging
        self.setup_logging()

        # Statistics tracking
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'successful_alignments': 0,
            'failed_alignments': 0,
            'validation_passed': 0,
            'validation_failed': 0,
            'processing_errors': [],
            'start_time': None,
            'end_time': None
        }

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Error loading config from {self.config_path}: {e}")
            raise

    def setup_output_directories(self):
        """Create output directories."""
        paths = self.config['paths']
        for key, path in paths.items():
            if key.startswith('output') or key in ['tempo_alignment', 'bmp_validation', 'aligned_beats', 'timing_analysis', 'processing_logs']:
                Path(path).mkdir(parents=True, exist_ok=True)

        # Create cache directory if enabled
        if self.config['hardware']['enable_caching']:
            Path(self.config['hardware']['cache_dir']).mkdir(parents=True, exist_ok=True)

    def setup_logging(self):
        """Setup logging configuration."""
        log_config = self.config['logging']
        log_file = Path(self.config['paths']['processing_logs']) / 'phase5_processing.log'

        # Configure file handler
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(getattr(logging, log_config['file_level']))

        # Configure formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)

        # Add handler to logger
        logger.addHandler(file_handler)
        logger.setLevel(getattr(logging, log_config['console_level']))

    def process_single_file(self, audio_file: Path) -> Dict[str, Any]:
        """Process a single audio file."""
        song_name = audio_file.stem
        result = {
            'song_name': song_name,
            'success': False,
            'error': None,
            'alignment_result': None,
            'validation_result': None
        }

        try:
            logger.info(f"Processing {song_name}")

            # Find corresponding TJA file
            tja_file = self.tja_parser.find_tja_file(
                song_name,
                Path(self.config['paths']['tja_data_dir'])
            )

            if not tja_file:
                result['error'] = f"No TJA file found for {song_name}"
                return result

            # Parse TJA metadata
            tja_metadata = self.tja_parser.parse_tja_file(tja_file)
            if not tja_metadata:
                result['error'] = f"Failed to parse TJA file: {tja_file}"
                return result

            # Detect beats
            beat_result = self.beat_detector.detect_beats(audio_file)
            if not beat_result:
                result['error'] = f"Failed to detect beats in {audio_file}"
                return result

            # Perform tempo alignment
            alignment_result = self.tempo_aligner.align_tempo_with_tja(beat_result, tja_metadata)

            # Validate BPM alignment
            validation_result = self.bmp_validator.validate_bmp_alignment(
                beat_result.tempo,
                tja_metadata.bmp,
                [beat_result.tempo],  # Single segment for now
                self.config['bmp_validation']['max_bmp_error_percent'] / 100.0
            )

            # Save results
            self._save_file_results(song_name, alignment_result, validation_result, beat_result)

            result['success'] = True
            result['alignment_result'] = alignment_result
            result['validation_result'] = validation_result

            # Update statistics
            self.stats['successful_alignments'] += 1
            if validation_result.validation_passed:
                self.stats['validation_passed'] += 1
            else:
                self.stats['validation_failed'] += 1

            logger.info(f"Successfully processed {song_name}")

        except Exception as e:
            error_msg = f"Error processing {song_name}: {str(e)}"
            logger.error(error_msg)
            result['error'] = error_msg
            self.stats['failed_alignments'] += 1
            self.stats['processing_errors'].append({
                'song': song_name,
                'error': error_msg
            })

        return result

    def _save_file_results(
        self,
        song_name: str,
        alignment_result: TempoAlignment,
        validation_result: BPMValidation,
        beat_result: BeatDetectionResult
    ):
        """Save results for a single file."""
        paths = self.config['paths']

        # Save tempo alignment
        alignment_file = Path(paths['tempo_alignment']) / f"{song_name}.json"
        with open(alignment_file, 'w') as f:
            json.dump({
                'aligned_bpm': alignment_result.aligned_bpm,
                'bpm_confidence': alignment_result.bpm_confidence,
                'tempo_drift': alignment_result.tempo_drift,
                'alignment_offset': alignment_result.alignment_offset,
                'beat_grid': alignment_result.beat_grid,
                'tempo_changes': alignment_result.tempo_changes
            }, f, indent=2)

        # Save BPM validation
        validation_file = Path(paths['bmp_validation']) / f"{song_name}.json"
        with open(validation_file, 'w') as f:
            json.dump({
                'tja_bpm': validation_result.tja_bpm,
                'detected_bpm': validation_result.detected_bpm,
                'bmp_error': validation_result.bmp_error,
                'bmp_error_percentage': validation_result.bmp_error_percentage,
                'validation_passed': validation_result.validation_passed,
                'validation_threshold': validation_result.validation_threshold,
                'segment_consistency': validation_result.segment_consistency
            }, f, indent=2)

        # Save aligned beats
        aligned_beats_file = Path(paths['aligned_beats']) / f"{song_name}.json"
        with open(aligned_beats_file, 'w') as f:
            json.dump(alignment_result.beat_grid, f, indent=2)

        # Save timing analysis
        timing_analysis_file = Path(paths['timing_analysis']) / f"{song_name}.json"
        with open(timing_analysis_file, 'w') as f:
            json.dump({
                'original_beats': beat_result.beats,
                'original_tempo': beat_result.tempo,
                'onset_times': beat_result.onset_times,
                'detection_confidence': beat_result.confidence,
                'alignment_applied': len(alignment_result.beat_grid) > 0,
                'tempo_changes_detected': len(alignment_result.tempo_changes)
            }, f, indent=2)

    def run_tempo_alignment_pipeline(self) -> Dict[str, Any]:
        """Run the complete tempo alignment pipeline."""
        logger.info("Starting Phase 5: Tempo Alignment & BPM Validation")
        self.stats['start_time'] = time.time()

        try:
            # Find all audio files
            audio_dir = Path(self.config['paths']['input_audio'])
            audio_files = list(audio_dir.glob("*.npy"))

            if not audio_files:
                raise RuntimeError(f"No audio files found in {audio_dir}")

            self.stats['total_files'] = len(audio_files)
            logger.info(f"Found {len(audio_files)} audio files to process")

            # Process files
            if self.config['processing']['max_workers'] > 1:
                results = self._process_files_parallel(audio_files)
            else:
                results = self._process_files_sequential(audio_files)

            # Generate summary report
            summary = self._generate_summary_report(results)

            # Save summary
            summary_file = Path(self.config['paths']['output_root']) / 'alignment_report.json'
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2)

            self.stats['end_time'] = time.time()
            total_time = self.stats['end_time'] - self.stats['start_time']

            logger.info(f"Phase 5 completed in {total_time:.2f} seconds")
            logger.info(f"Processed: {self.stats['processed_files']}/{self.stats['total_files']} files")
            logger.info(f"Successful alignments: {self.stats['successful_alignments']}")
            logger.info(f"Validation passed: {self.stats['validation_passed']}")

            return summary

        except Exception as e:
            logger.error(f"Phase 5 failed: {str(e)}")
            raise

    def _process_files_sequential(self, audio_files: List[Path]) -> List[Dict[str, Any]]:
        """Process files sequentially."""
        results = []

        for audio_file in tqdm(audio_files, desc="Processing audio files"):
            result = self.process_single_file(audio_file)
            results.append(result)
            self.stats['processed_files'] += 1

        return results

    def _process_files_parallel(self, audio_files: List[Path]) -> List[Dict[str, Any]]:
        """Process files in parallel."""
        results = []
        max_workers = self.config['processing']['max_workers']

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_file = {
                executor.submit(self.process_single_file, audio_file): audio_file
                for audio_file in audio_files
            }

            # Collect results
            for future in tqdm(as_completed(future_to_file), total=len(audio_files), desc="Processing audio files"):
                result = future.result()
                results.append(result)
                self.stats['processed_files'] += 1

        return results

    def _generate_summary_report(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate comprehensive summary report."""
        successful_results = [r for r in results if r['success']]
        failed_results = [r for r in results if not r['success']]

        # Calculate statistics
        if successful_results:
            bmp_errors = [r['validation_result'].bmp_error_percentage for r in successful_results]
            avg_bmp_error = np.mean(bmp_errors)

            confidences = [r['alignment_result'].bmp_confidence for r in successful_results]
            avg_confidence = np.mean(confidences)

            tempo_drifts = [r['alignment_result'].tempo_drift for r in successful_results]
            avg_tempo_drift = np.mean(tempo_drifts)
        else:
            avg_bmp_error = 0.0
            avg_confidence = 0.0
            avg_tempo_drift = 0.0

        # Quality gate evaluation
        quality_gates = self.config['quality_gates']
        success_rate = len(successful_results) / len(results) if results else 0.0
        validation_pass_rate = self.stats['validation_passed'] / len(successful_results) if successful_results else 0.0

        quality_passed = (
            success_rate >= quality_gates['min_processing_success_rate'] and
            validation_pass_rate >= quality_gates['min_bmp_validation_pass_rate'] and
            avg_bmp_error <= quality_gates['max_avg_bmp_error'] and
            avg_confidence >= quality_gates['min_avg_alignment_confidence']
        )

        return {
            'phase': 'Phase 5: Tempo Alignment & BPM Validation',
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'processing_stats': {
                'total_files': self.stats['total_files'],
                'processed_files': self.stats['processed_files'],
                'successful_alignments': self.stats['successful_alignments'],
                'failed_alignments': self.stats['failed_alignments'],
                'validation_passed': self.stats['validation_passed'],
                'validation_failed': self.stats['validation_failed'],
                'processing_time': self.stats['end_time'] - self.stats['start_time'] if self.stats['end_time'] else 0.0
            },
            'quality_metrics': {
                'success_rate': success_rate,
                'validation_pass_rate': validation_pass_rate,
                'avg_bmp_error': avg_bmp_error,
                'avg_alignment_confidence': avg_confidence,
                'avg_tempo_drift': avg_tempo_drift
            },
            'quality_gates': {
                'passed': quality_passed,
                'requirements': quality_gates,
                'results': {
                    'success_rate_passed': success_rate >= quality_gates['min_processing_success_rate'],
                    'validation_rate_passed': validation_pass_rate >= quality_gates['min_bmp_validation_pass_rate'],
                    'bmp_error_passed': avg_bmp_error <= quality_gates['max_avg_bmp_error'],
                    'confidence_passed': avg_confidence >= quality_gates['min_avg_alignment_confidence']
                }
            },
            'errors': self.stats['processing_errors'],
            'configuration': self.config
        }
