"""
Phase 3 Execution Script: Model Architecture Design
Runs the complete Phase 3 process for TJA chart generation model architecture design.
"""

import sys
import os
import logging
import traceback
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.phases.phase_03_model_architecture import ModelArchitectureDesigner


def main():
    """Main execution function for Phase 3."""
    
    print("=" * 80)
    print("TJA Chart Generation - Phase 3: Model Architecture Design")
    print("=" * 80)
    
    try:
        # Configuration file path
        config_path = os.path.join(project_root, "configs", "phase_03_config.yaml")
        
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        print(f"Loading configuration from: {config_path}")
        
        # Initialize model architecture designer
        print("Initializing Model Architecture Designer...")
        designer = ModelArchitectureDesigner(config_path)
        
        # Run Phase 3 process
        print("Starting Phase 3 execution...")
        summary = designer.run_phase()
        
        # Display results
        print("\n" + "=" * 80)
        print("PHASE 3 EXECUTION SUMMARY")
        print("=" * 80)
        
        stats = summary['statistics']
        quality = summary['quality_metrics']
        
        print(f"Total Variants Attempted: {stats['total_variants_attempted']}")
        print(f"Successful Variants: {stats['successful_variants']}")
        print(f"Failed Variants: {stats['failed_variants']}")
        print(f"Success Rate: {stats['success_rate']:.2%}")
        print(f"Total Processing Time: {stats['total_processing_time']:.2f} seconds")
        print(f"Average Processing Time: {stats['average_processing_time']:.2f} seconds")
        
        print(f"\nQuality Metrics:")
        print(f"Meets Quality Gates: {'✅ YES' if quality['meets_quality_gates'] else '❌ NO'}")
        print(f"Architecture Variants Created: {quality['architecture_variants']}")
        print(f"Memory Profiles Generated: {quality['memory_profiles_generated']}")
        
        # Display architecture variants
        if summary['architecture_variants']:
            print(f"\nArchitecture Variants:")
            print("-" * 60)
            for variant in summary['architecture_variants']:
                print(f"Variant: {variant['variant']}")
                print(f"  Parameters: {variant['model_size']['total_parameters']:,}")
                print(f"  Model Size: {variant['model_size']['model_size_mb']:.2f} MB")
                print(f"  Processing Time: {variant['processing_time']:.2f}s")
                print()
        
        # Display memory profiles
        if summary['memory_profiles']:
            print(f"Memory Usage Profiles:")
            print("-" * 60)
            for profile in summary['memory_profiles']:
                memory = profile['memory_usage']
                print(f"Variant: {profile['variant']}")
                print(f"  Training Memory: {memory['total_training_memory_gb']:.2f} GB")
                print(f"  Inference Memory: {memory['total_inference_memory_gb']:.2f} GB")
                print(f"  Parameter Memory: {memory['parameter_memory_gb']:.2f} GB")
                print()
        
        # Check quality gates
        if quality['meets_quality_gates']:
            print("✅ Phase 3 completed successfully - All quality gates passed!")
        else:
            print("⚠️  Phase 3 completed with warnings - Some quality gates not met")
        
        print("\nOutput files saved to:")
        paths = summary['configuration']['paths']
        print(f"  Model Configs: {paths['model_configs']}")
        print(f"  Architecture Specs: {paths['architecture_specs']}")
        print(f"  Processing Logs: {paths['logs_output']}")
        
        print("\n" + "=" * 80)
        print("Phase 3 execution completed successfully!")
        print("Ready to proceed to Phase 4: Training Pipeline")
        print("=" * 80)
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️  Phase 3 execution interrupted by user")
        return 1
        
    except Exception as e:
        print(f"\n❌ Phase 3 execution failed: {str(e)}")
        print("\nFull error traceback:")
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
