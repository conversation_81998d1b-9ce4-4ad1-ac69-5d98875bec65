# Phase 1: Audio Loading & Format Standardization Configuration
# Windows-compatible paths with RTX 3070 memory management

# Input/Output Paths (Windows format)
paths:
  input_root: "data\\raw\\ese"
  output_root: "data\\processed\\phase1"
  audio_output: "data\\processed\\phase1\\audio"
  metadata_output: "data\\processed\\phase1\\metadata"
  logs_output: "data\\processed\\phase1\\processing_logs"

# Audio Processing Parameters
audio:
  target_sample_rate: 22050  # Standardized sample rate (Hz)
  target_channels: 1         # Mono audio
  target_dtype: "float32"    # Target data type
  supported_formats: [".ogg", ".wav", ".mp3", ".flac"]
  
# Memory Management (RTX 3070 constraints)
memory:
  max_ram_usage_gb: 8.0      # Maximum RAM usage (8GB limit)
  max_files_in_memory: 50    # Maximum files to process simultaneously
  chunk_size: 1024           # Audio chunk size for processing
  gc_frequency: 10           # Garbage collection frequency (every N files)
  
# Processing Parameters
processing:
  max_workers: 4             # Parallel processing workers
  batch_size: 25             # Files per batch
  validation_subset_size: 100 # Files for initial validation
  timeout_seconds: 30        # Timeout per file processing
  retry_attempts: 3          # Retry attempts for failed files
  
# Quality Gates
quality:
  min_success_rate: 0.95     # Minimum 95% success rate
  min_duration_seconds: 10   # Minimum audio duration
  max_duration_seconds: 600  # Maximum audio duration (10 minutes)
  min_sample_rate: 8000      # Minimum acceptable sample rate
  max_sample_rate: 192000    # Maximum acceptable sample rate
  
# Logging Configuration
logging:
  level: "INFO"              # Logging level (DEBUG, INFO, WARNING, ERROR)
  format: "%(asctime)s - %(levelname)s - %(message)s"
  file_encoding: "utf-8"     # Unicode support for file paths
  max_log_size_mb: 100       # Maximum log file size
  backup_count: 5            # Number of backup log files
  
# Error Handling
error_handling:
  continue_on_error: true    # Continue processing if individual files fail
  save_error_details: true   # Save detailed error information
  create_error_report: true  # Generate error summary report
  
# Output Format
output:
  save_numpy_arrays: true    # Save audio as .npy files
  save_metadata_json: true   # Save metadata as .json files
  compress_arrays: false     # Compress numpy arrays (slower but smaller)
  metadata_indent: 2         # JSON indentation for readability
  
# Validation Settings
validation:
  run_validation: true       # Run validation after processing
  validate_file_integrity: true  # Check file integrity
  validate_audio_properties: true # Check audio properties
  generate_statistics: true  # Generate processing statistics
